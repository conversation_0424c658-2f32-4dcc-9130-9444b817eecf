package com.ideal.service.nessus;

import com.ideal.ieai.server.nessus.model.LoopholeInfoBean;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @version 8.24.0
 * @date 2024-01-04 9:22
 */
public class LoopholeInfoServiceTest {

    @Test
    public void updateLoopholeInfoByRepair() {
        LoopholeInfoService nes = new LoopholeInfoService();
        String jsonData = "[{\"iid\":1846,\"loopholeName\":\"Dnsmasq \\u7f13\\u51b2\\u533a\\u6ea2\\u51fa\\u6f0f\\u6d1e(CVE-2021-45956)\",\"cveNumber\":\"CVE-2021-45956\",\"hostIp\":\"************\",\"riskLevel\":4,\"loopholeSource\":\"\\u90ae\\u4ef6\\u670d\\u52a1\\u7c7b\\u6f0f\\u6d1e\",\"repairUser\":\"Server Admin\",\"loopholeHarm\":\"\\u4e0d\\u6e05\\u695a\",\"loopholeLocation\":\"\\u4e0d\\u6e05\\u695a\",\"loopholeDescribe\":\"Dnsmasq\\u662f\\u4e00\\u6b3e\\u4f7f\\u7528C\\u8bed\\u8a00\\u7f16\\u5199\\u7684\\u8f7b\\u91cf\\u7ea7DNS\\u8f6c\\u53d1\\u548cDHCP\\u3001TFTP\\u670d\\u52a1\\u5668\\u3002 \\nDnsmasq 2.86\\u5b58\\u5728\\u5b89\\u5168\\u6f0f\\u6d1e\\uff0c\\u8be5\\u6f0f\\u6d1e\\u6e90\\u4e8e\\u5728\\u63d0\\u53d6\\u540d\\u79f0\\u4e2d\\u6709\\u4e00\\u4e2a\\u57fa\\u4e8e\\u5806\\u7684\\u7f13\\u51b2\\u533a\\u6ea2\\u51fa(\\u4ece\\u54c8\\u5e0c\\u95ee\\u9898\\u548c\\u6a21\\u7ccautil.c\\u4e2d\\u8c03\\u7528)\\u3002\",\"fixPlan\":\"\\u4fee\\u590d\\u65b9\\u6848,\\u518d\\u6b21\\u6267\\u884c\\u5e76\\u91cd\\u65b0\\u4fee\\u590d\",\"repairId\":0,\"changeOrderNO\":\"\",\"taskName\":\"\",\"tplName\":\"\",\"cPid\":3,\"cpName\":\"************\",\"agentId\":6,\"agentIp\":\"**********\",\"agentName\":\"**********\",\"osName\":\"Linux\",\"status\":0,\"loopholePort\":\"80\",\"occurNum\":1,\"cvssScore\":\"5.1\",\"strongSuggest\":\"\\u76ee\\u524d\\u5382\\u5546\\u6682\\u672a\\u53d1\\u5e03\\u4fee\\u590d\\u63aa\\u65bd\\u89e3\\u51b3\\u6b64\\u5b89\\u5168\\u95ee\\u9898\\uff0c\\u5efa\\u8bae\\u4f7f\\u7528\\u6b64\\u8f6f\\u4ef6\\u7684\\u7528\\u6237\\u968f\\u65f6\\u5173\\u6ce8\\u5382\\u5546\\u4e3b\\u9875\\u6216\\u53c2\\u8003\\u7f51\\u5740\\u4ee5\\u83b7\\u53d6\\u89e3\\u51b3\\u529e\\u6cd5\\uff1a \\nhttps://github.com/google/oss-fuzz-vulns/blob/main/vulns/dnsmasq/OSV-2021-929.yaml\",\"rspInfo\":\"Server Admin\\u6d4b\\u8bd5\\u6570\\u636e\",\"isDanger\":\"\\u662f\",\"loopholeTimeStr\":\"2023-11-13\",\"repairStatus\":3,\"repairTimeStr\":\"2024-01-04\",\"remark\":\"\\u518d\\u6b21\\u4fee\\u590d\\u5931\\u8d25\",\"retestStatus\":0,\"retestUser\":\"\",\"retestTimeStr\":\"\",\"scheduleEndTimeStr\":\"\"}]";
        try{
            List<LoopholeInfoBean> list = nes.jsonTranslateRepair(jsonData);
            assertNotNull("断言查询结果为NULL", list);
            assertTrue("断言查询结果为空", list.size() > 0);
            for (LoopholeInfoBean infoBean : list)
            {
                System.out.println("漏洞名称：" + infoBean.getLoopholeName()+",修复计划：" + infoBean.getFixPlan());
            }
        }catch(Exception e){
            fail("查询失败");
            e.printStackTrace();
        }
    }

}