package com.ideal.controller.timetask.restfulapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.quartz.TimetaskService;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.timetask.repository.TimetaskInfoBean;
import com.ideal.ieai.server.timetask.repository.executetask.TaskInstanceBean;
import com.ideal.ieai.server.timetask.repository.restfulapi.TimeTaskResultMessage;
import com.ideal.service.timetask.manage.TTManageService;
import com.ideal.service.timetask.restfulapi.TimeTaskService;
import org.apache.log4j.Logger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.MockedStatic;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

import static org.mockito.Mockito.*;

public class TimeTaskControllerTest {

    /***
     * 每个接口需要测试项：
     * 1.参数转换
     * 2.设计状态校验的，验证状态
     */

    @BeforeAll
    public static void init() {
        // 声明静态 mock 类
        ServerEnv serverEnv = mock(ServerEnv.class);
        MockedStatic<ServerEnv> serverEnvMockedStatic = mockStatic(ServerEnv.class);
        serverEnvMockedStatic.when(ServerEnv::getServerEnv).thenReturn(serverEnv);
        MockedStatic<Environment> environmentMockedStatic = mockStatic(Environment.class);
        when(Environment.getInstance()).thenReturn((Environment) serverEnv);

        // 声明开关
        when(serverEnv.getAomsApiSwitch()).thenReturn(true);

        MockedStatic<DBResource> dbResourceMockedStatic = mockStatic(DBResource.class);
        MockedStatic<TimetaskService> timetaskServiceMockedStatic = mockStatic(TimetaskService.class);

        MockedStatic<TimeTaskService> timeTaskServiceMockedStatic = mockStatic(TimeTaskService.class);
        MockedStatic<TTManageService> ttManageServiceMockedStatic = mockStatic(TTManageService.class);
    }

    @DisplayName("定时任务任务列表信息接口-成功")
    @ParameterizedTest
    @CsvSource(value = {"{\"condition\":\"\",\"limit\":\"10\",\"start\":\"0\"}", "{}"}, delimiterString = "|")
    public void timeTaskInfoSuccess(String body) throws Exception {
        System.out.println(body);

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(false);
        when(resultSetMock.getString(anyString())).thenReturn("1");

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

//
        Assertions.assertDoesNotThrow(() -> timeTaskController.timeTaskInfo(body, request, response));
        Object attribute = request.getAttribute("resp");
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务任务列表信息接口-参数转换失败")
    @ParameterizedTest
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void timeTaskInfoFailure(String body) {
        System.out.println(body);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.timeTaskInfo(body, request, response));
        Object attribute = request.getAttribute("resp");
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务任务列表信息接口-传递空值参数")
    @Test
    public void timeTaskInfoFailure() {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.timeTaskInfo("", request, response));
        Object attribute = request.getAttribute("resp");
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-传递空值参数")
    @Test
    public void makeTaskEffectFailure() {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect("", request, response));
        Object attribute = request.getAttribute("resp");
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-参数转换失败")
    @ParameterizedTest
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void makeTaskEffectFailure(String body) {
        System.out.println(body);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-传递任务不存在")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makeTaskEffectValidTaskState(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, false);
        when(resultSetMock.getInt(anyString())).thenReturn(0);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-任务下未配置调度IP")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makeTaskEffectValidIp(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("");

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(TimeTaskResultMessage.class), anyInt());

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-校验传递参数状态正确")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makeTaskEffectValidState(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).startTimetask(any(), any(), any());

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-校验数据库中任务状态cron非法")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makeTaskEffectValidNotExistAvailableCron(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("验证cron的任务名称");
        timetaskInfoBean.setTaskRuntime("* * - - * ");
        List list = new ArrayList();
        list.add(timetaskInfoBean);
        Object[] resObject = new Object[]{list};
        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(resObject);

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(TimeTaskResultMessage.class), anyInt());
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());
        doCallRealMethod().when(timeTaskService).startTimetask(anyList(), any(), anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.validateCronForTaskList(anyList())).thenCallRealMethod();

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-校验无可用ServerIP")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makeTaskEffectValidNotExistAvailableIp(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.validateCronForTaskList(anyList())).thenCallRealMethod();

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务生效接口-成功")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makeTaskEffectSuccess(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

//        TimetaskService.getInstance().checkTTServerCnt();
        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.validateCronForTaskList(anyList())).thenReturn("");
        when(timetaskService.checkTTServerCnt()).thenReturn(true);

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(TimeTaskResultMessage.class), anyInt());
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).startTimetask(anyList(), any(), anyString());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());
        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        List<TimetaskInfoBean> data = new ArrayList();
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("测试任务名称");
        timetaskInfoBean.setEnable(1);
        data.add(timetaskInfoBean);
        Object[] objects = new Object[]{data};

        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(objects);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @DisplayName("任务失效接口-传递空参数")
    @Test
    public void makeTaskStop() throws Exception {

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStop("", request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务失效接口-传递无效参数")
    @ParameterizedTest
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void makeTaskStopValidParameterError(String body) throws Exception {

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStop(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务失效接口-传递任务不存在")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskStopValidParameter(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, false);
        when(resultSetMock.getInt(anyString())).thenReturn(0);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

//        TimeTaskService.getInstance().stopTimetask
        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).stopTimetask(anyString(), any(), any(), any());

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStop(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务失效接口-传递任务状态不是运行中")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskStopValidState(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).stopTimetask(anyString(), any(), any(), any());

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStop(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("任务失效接口-成功")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskStopSuccess(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.getTaskInfoActByTaskIDs(anyString())).thenReturn(null);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        Map data = new HashMap();
        data.put("success", true);
        when(timeTaskService.checkInstanceAndStop(anyString())).thenCallRealMethod();
        doCallRealMethod().when(timeTaskService).stopTimetask(anyString(), any(), any(), any());

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStop(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @DisplayName("停止指定时间段内任务接口-传递空参数")
    @Test
    public void makeTaskStopByTime() throws Exception {

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStopByTime("", request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("停止指定时间段内任务接口-传递无效参数")
    @ParameterizedTest
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void makeTaskStopByTimeValidParameterError(String body) throws Exception {

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStopByTime(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("停止指定时间段内任务接口-验证参数中每个属性值")
    @ParameterizedTest
    @CsvSource(value = {"{\"nextStartTimeEnd\":\"2024-10-20\",\"stopType\":\"force\"}", "{\"nextStartTimeBegin\":\"2024-09-19\",\"stopType\":\"force\"}", "{\"nextStartTimeBegin\":\"2024-09-19\",\"nextStartTimeEnd\":\"2024-10-20\"}"}, delimiterString = "|")
    public void makeTaskStopByTimeValidEachParameterError(String body) throws Exception {

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStopByTime(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("停止指定时间段内任务接口-传递时间段内没有可停止任务")
    @ParameterizedTest
    @CsvSource(value = {"{\"nextStartTimeBegin\":\"2024-09-19\",\"nextStartTimeEnd\":\"2024-10-20\",\"stopType\":\"force\"}"}, delimiterString = "|")
    public void makeTaskStopByTimeValidExistTask(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, false);
        when(resultSetMock.getInt(anyString())).thenReturn(0);

//        TimeTaskService.getInstance().getTimeTaskInfoByTime(timetaskBean, beans);
        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.getTaskInfoActByTaskIDs(anyString())).thenReturn(null);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStopByTime(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("停止指定时间段内任务接口-成功")
    @ParameterizedTest
    @CsvSource(value = {"{\"nextStartTimeBegin\":\"2024-09-19\",\"nextStartTimeEnd\":\"2024-10-20\",\"stopType\":\"force\"}"}, delimiterString = "|")
    public void makeTaskStopByTimeSuccess(String body) throws Exception {
        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, false);
        when(resultSetMock.getInt(anyString())).thenReturn(0);

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        List<TimetaskInfoBean> data = new ArrayList();
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("测试任务名称");
        timetaskInfoBean.setEnable(1);
        data.add(timetaskInfoBean);
        doCallRealMethod().when(timeTaskService).validStopByTimeParam(anyString(), any());
        when(timeTaskService.getTimeTaskInfoByTime(any(), any())).thenReturn(data);
        doCallRealMethod().when(timeTaskService).stopTimetask(anyString(), anyList(), any(), anyBoolean());
        doCallRealMethod().when(timeTaskService).checkInstanceAndStop(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.getTaskInfoActByTaskIDs(anyString())).thenReturn(Collections.emptyList());

//        TTManageService.getInstance().getTimetaskInfoByIdsStr
        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);

        Object[] objects = new Object[]{data};

        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(objects);

        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskStopByTime(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @Test
    @DisplayName("任务立即执行接口-空参数")
    public void taskImmediateStart() {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart("", request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @ParameterizedTest
    @DisplayName("任务立即执行接口-参数转换失败")
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void taskImmediateStartValidParameter(String body) {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @ParameterizedTest
    @DisplayName("任务立即执行接口-传递任务不存在")
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void taskImmediateStartNotExistTask(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, false);
        when(resultSetMock.getInt(anyString())).thenReturn(0);

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @ParameterizedTest
    @DisplayName("任务立即执行接口-任务未配置调度IP")
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void taskImmediateStartNotSetIp(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("");

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @ParameterizedTest
    @DisplayName("任务立即执行接口-任务cron不符合校验")
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void taskImmediateStartValidCron(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();


        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("验证cron的任务名称");
        timetaskInfoBean.setTaskRuntime("* * - - * ");
        List list = new ArrayList();
        list.add(timetaskInfoBean);
        Object[] resObject = new Object[]{list};
        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(resObject);

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).taskImmediateStart(anyList(), any(), anyLong());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.validateCronForTaskList(anyList())).thenCallRealMethod();

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @ParameterizedTest
    @DisplayName("任务立即执行接口-没有可用的定时任务server")
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void taskImmediateStartNotAvailableIp(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();


        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("验证cron的任务名称");
        timetaskInfoBean.setTaskRuntime("0 30 15 * * ? *");
        List list = new ArrayList();
        list.add(timetaskInfoBean);
        Object[] resObject = new Object[]{list};
        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(resObject);

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).taskImmediateStart(anyList(), any(), anyLong());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.validateCronForTaskList(anyList())).thenCallRealMethod();

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }


    @ParameterizedTest
    @DisplayName("任务立即执行接口-成功")
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void taskImmediateStartSuccess(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("验证cron的任务名称");
        timetaskInfoBean.setTaskRuntime("0 30 15 * * ? *");
        List list = new ArrayList();
        list.add(timetaskInfoBean);
        Object[] resObject = new Object[]{list};
        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(resObject);

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).taskImmediateStart(anyList(), any(), anyLong());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        when(timetaskService.validateCronForTaskList(anyList())).thenCallRealMethod();
        when(timetaskService.checkTTServerCnt()).thenReturn(true);

        Assertions.assertDoesNotThrow(() -> timeTaskController.taskImmediateStart(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务暂停接口-空参数")
    @Test
    public void makeTaskPause() {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskPause("", request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务暂停接口-参数转换失败")
    @ParameterizedTest
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void makeTaskPauseValidParameter(String body) {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskPause(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务暂停接口-任务不存在")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskPauseNotExistTask(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validPauseParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getStopArrMess(any());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(0);

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskPause(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务暂停接口-任务没有配置调度IP")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskPauseNotSetIp(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validPauseParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getStopArrMess(any());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("");

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskPause(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务暂停接口-无运行中的任务")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskPauseNotExistRunningTask(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validPauseParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getStopArrMess(any());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        doCallRealMethod().when(timeTaskService).stopTimetask(anyString(), anyList(), any(), anyBoolean());
        doCallRealMethod().when(timeTaskService).checkInstanceAndStop(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        List list = new ArrayList();
//        TaskInstanceBean bean = new TaskInstanceBean();
//        list.add(bean);
        when(timetaskService.getTaskInfoActByTaskIDs(anyString())).thenReturn(list);

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskPause(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @DisplayName("定时任务暂停接口-成功")
    @ParameterizedTest
    @CsvSource(value = {"[{\"stopType\":\"force\",\"timeTaskList\":[{\"enable\":1,\"iid\":108,\"taskName\":\"11111\"}]}]"}, delimiterString = "|")
    public void makeTaskPauseSuccess(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validPauseParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getStopArrMess(any());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        doCallRealMethod().when(timeTaskService).stopTimetask(anyString(), anyList(), any(), anyBoolean());
        doCallRealMethod().when(timeTaskService).checkInstanceAndStop(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        List list = new ArrayList();
        TaskInstanceBean bean = new TaskInstanceBean();
        list.add(bean);
        when(timetaskService.getTaskInfoActByTaskIDs(anyString())).thenReturn(list);

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        List<TimetaskInfoBean> data = new ArrayList();
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("测试任务名称");
        timetaskInfoBean.setEnable(1);
        data.add(timetaskInfoBean);
        Object[] objects = new Object[]{data};

        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(objects);

        Assertions.assertDoesNotThrow(() -> timeTaskController.makeTaskPause(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }

    @DisplayName("暂停状态的定时任务生效接口-空参数")
    @Test
    public void makePauseTaskEffect() {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makePauseTaskEffect("", request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("暂停状态的定时任务生效接口-参数转换失败")
    @ParameterizedTest
    @CsvSource(value = {" 1231", "condition:\"\""}, delimiterString = "|")
    public void makePauseTaskEffect(String body) {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        Assertions.assertDoesNotThrow(() -> timeTaskController.makePauseTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("暂停状态的定时任务生效接口-任务周期非法")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makePauseTaskEffectValidCron(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).startTimetask(anyList(), any(), anyString());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        doCallRealMethod().when(timetaskService).validateCronForTaskList(anyList());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        List<TimetaskInfoBean> data = new ArrayList();
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("测试任务名称");
        timetaskInfoBean.setEnable(1);
        timetaskInfoBean.setTaskRuntime("* * - - * ");
        data.add(timetaskInfoBean);
        Object[] objects = new Object[]{data};

        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(objects);

        Assertions.assertDoesNotThrow(() -> timeTaskController.makePauseTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("暂停状态的定时任务生效接口-判断是否有可用IP")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makePauseTaskEffectValidIp(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).startTimetask(anyList(), any(), anyString());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        doCallRealMethod().when(timetaskService).validateCronForTaskList(anyList());

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        List<TimetaskInfoBean> data = new ArrayList();
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("测试任务名称");
        timetaskInfoBean.setEnable(1);
        timetaskInfoBean.setTaskRuntime("0 30 15 * * ? *");
        data.add(timetaskInfoBean);
        Object[] objects = new Object[]{data};

        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(objects);

        Assertions.assertDoesNotThrow(() -> timeTaskController.makePauseTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(false, jsonObject.get("isOk"));
    }

    @DisplayName("暂停状态的定时任务生效接口-成功")
    @ParameterizedTest
    @CsvSource(value = {"[{\"enable\":0,\"iid\":108,\"taskName\":\"11111\"}]"}, delimiterString = "|")
    public void makePauseTaskEffectSuccess(String body) throws Exception {
        TimeTaskController timeTaskController = new TimeTaskController();
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();

        TimeTaskService timeTaskService = mock(TimeTaskService.class);
        when(TimeTaskService.getInstance()).thenReturn(timeTaskService);
        doCallRealMethod().when(timeTaskService).validParam(anyList(), anyString(), any(), anyInt());
        doCallRealMethod().when(timeTaskService).getArrMess(any());
        doCallRealMethod().when(timeTaskService).startTimetask(anyList(), any(), anyString());
        doCallRealMethod().when(timeTaskService).validateCronAndStart(anyString());

        TimetaskService timetaskService = mock(TimetaskService.class);
        when(TimetaskService.getInstance()).thenReturn(timetaskService);
        doCallRealMethod().when(timetaskService).validateCronForTaskList(anyList());

        when(timetaskService.checkTTServerCnt()).thenReturn(true);

        // 声明数据库连接
        Connection connectionMock = mock(Connection.class);
        PreparedStatement preparedStatementMock = mock(PreparedStatement.class);
        ResultSet resultSetMock = mock(ResultSet.class);

        // 根据测试返回数据
        when(DBResource.getConnection(anyString(), any(Logger.class), anyInt())).thenReturn(connectionMock);
        when(connectionMock.prepareStatement(anyString())).thenReturn(preparedStatementMock);
        when(preparedStatementMock.executeQuery()).thenReturn(resultSetMock);
        when(resultSetMock.next()).thenReturn(true, true, false, false);
        when(resultSetMock.getInt(anyString())).thenReturn(1);
        when(resultSetMock.getString(anyString())).thenReturn("*************");

        TTManageService ttManageService = mock(TTManageService.class);
        when(TTManageService.getInstance()).thenReturn(ttManageService);
        List<TimetaskInfoBean> data = new ArrayList();
        TimetaskInfoBean timetaskInfoBean = new TimetaskInfoBean();
        timetaskInfoBean.setTaskName("测试任务名称");
        timetaskInfoBean.setEnable(1);
        timetaskInfoBean.setTaskRuntime("0 30 15 * * ? *");
        data.add(timetaskInfoBean);
        Object[] objects = new Object[]{data};

        when(ttManageService.getTimetaskInfoByIdsStr(anyString(), anyInt())).thenReturn(objects);

        Assertions.assertDoesNotThrow(() -> timeTaskController.makePauseTaskEffect(body, request, response));
        Object attribute = request.getAttribute("resp");
        System.out.println(attribute);
        JSONObject jsonObject = JSON.parseObject(attribute.toString());
        Assertions.assertSame(true, jsonObject.get("isOk"));
    }
}
