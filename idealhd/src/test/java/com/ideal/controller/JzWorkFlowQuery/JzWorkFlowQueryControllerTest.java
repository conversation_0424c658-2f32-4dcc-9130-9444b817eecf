package com.ideal.controller.JzWorkFlowQuery;

import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import junit.framework.TestCase;

public class JzWorkFlowQueryControllerTest extends TestCase {

    public void testJzWorkFlowQuery() {
      /*  public static final String  ACT_STATE_NULL                       = "Null";
        public static final String  ACT_STATE_WAITING                    = "Waiting";
        public static final String  ACT_STATE_READY                      = "Ready";
        public static final String  ACT_STATE_RUNNING                    = "Running";
        public static final String  ACT_STATE_FINISH                     = "Finished";
        public static final String  ACT_STATE_SKIPPED                    = "Skipped";
        public static final String  ACT_STATE_FAIL_SKIPPED               = "Fail:Skipped";
        public static final String  ACT_STATE_FAIL                       = "Fail";
        public static final String  ACT_STATE_FAIL_BUSINESS              = "Fail:Business";*/
        String userId ="1";
        String statue="Fail:Business";
        String taskexcelevel="Fail:Business";
        String actType="3" ;
       /* String  iretrynum="1";
        String  iredocur="3";*/
        String  iretrynum="sfa";
        String  iredocur="afasdf";
        try{
            String state  = WorkflowManager.getInstance().getFirstStatusYL(statue,taskexcelevel,actType,iretrynum,iredocur);
            assertNotNull("断言自动重试中结果为NULL", state);
            System.out.println(" activity state constants:" + state);


        }catch(Exception e){
            fail("获取自动重试中异常!");
            e.printStackTrace();
        }
    }
}