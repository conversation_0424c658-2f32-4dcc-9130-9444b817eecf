package com.ideal.controller.ftpConfig;

import com.ideal.ieai.server.ftpConfig.FtpConfigManager;
import com.ideal.ieai.server.ftpConfig.FtpConfigModel;
import junit.framework.TestCase;

import java.util.List;

public class FtpConfigControllerTest extends TestCase {

    public void testSaveFtpConfig() {
        String jsonData = "[{\"iid\":\"\",\"ibusnessysname\":\"\\u4e1a\\u52a11\\u7cfb\\u7edf1000\",\"iftpip\":\"127.0.0.1\",\"iftpport\":21,\"iftpuser\":\"\",\"iftppassword\":\"\",\"iftppath\":\"\"}]";
        FtpConfigManager manage = new FtpConfigManager();
        try{
            List<FtpConfigModel> list = manage.jsonTranslateFtpConfig(jsonData);
            assertNotNull("断言查询结果为NULL", list);
            assertTrue("断言查询结果为空", list.size() > 0);
            for (FtpConfigModel ftpConfigModel : list)
            {
                System.out.println("业务系统名称：" + ftpConfigModel.getIbusnessysname());
            }
        }catch(Exception e){
            fail("查询失败");
            e.printStackTrace();
        }
    }
}