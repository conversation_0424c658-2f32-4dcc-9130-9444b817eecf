package test.agent;

import com.ideal.common.utils.IPTools;
import junit.framework.Test;
import junit.framework.TestCase;
import junit.framework.TestSuite;

import java.math.BigDecimal;

/**
 * Unit test for simple App.
 */
public class AppTest extends TestCase
{
    /**
     * Create the test case
     *
     * @param testName name of the test case
     */
    public AppTest ( String testName )
    {
        super(testName);
    }

    /**
     * @return the suite of tests being tested
     */
    public static Test suite ()
    {
        return new TestSuite(AppTest.class);
    }

    /**
     * Rigourous Test :-)
     */
    public void testGetAlias ()
    {
        String ip = "**********";
        BigDecimal num = IPTools.getAlias(ip);
        System.out.println("**********-->" + num);
        assertTrue(true);
    }

    public void testIpv6Alias ()
    {
        String ip = "[2001:470:c:1818:123:2]";
        BigDecimal num = IPTools.ipv6Alias(ip);
        //int num= MurmurHash.hash32(ip);
        System.out.println(ip + "-->" + num);
        assertTrue(true);
    }

    public void test12(){
        long ip = -1234567891234L;
        long n = 1000000000000L;
        long num = ip % n;
        System.out.println(ip + "-->" + num);
    }
}
