<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:mvc="http://www.springframework.org/schema/mvc" 
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
							http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
							http://www.springframework.org/schema/mvc 
							http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
							http://www.springframework.org/schema/context 
							http://www.springframework.org/schema/context/spring-context-3.0.xsd
  							http://dubbo.apache.org/schema/dubbo          
    						http://dubbo.apache.org/schema/dubbo/dubbo.xsd">
    						
      						
 	<!-- 需要脚本服务化功能，使用打开下面内容 -->	
 	<!--<dubbo:application name="consumer-of-ieai-web">
 		<dubbo:parameter key="qos.enable" value="false" />
 		<dubbo:parameter key="qos.accept.foreign.ip" value="false" />
 		<dubbo:parameter key="qos.port" value="22222" />
 	</dubbo:application>
	<dubbo:registry
		protocol="zookeeper"
		address="127.0.0.1:2181" />
	<dubbo:annotation package="com.ideal.sscontroller" />
	<context:component-scan base-package="com.ideal.sscontroller" />-->
	<!-- 需要脚本服务化功能，使用打开上面内容 -->
		
 
</beans>