<?xml version="1.0" encoding="UTF-8"?>
<deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">
 <globalConfiguration>
  <parameter name="sendMultiRefs" value="true"/>
  <parameter name="disablePrettyXML" value="true"/>
  <parameter name="adminPassword" value="admin"/>
  <!-- <parameter name="attachments.Directory" value="E:\ENTEGORCITIC1024\server\iEAIWebService\WEB-INF\attachments"/> -->
  <parameter name="attachments.Directory" value=""/>
  <parameter name="dotNetSoapEncFix" value="true"/>
  <parameter name="enableNamespacePrefixOptimization" value="false"/>
  <parameter name="sendXMLDeclaration" value="true"/>
  <parameter name="attachments.implementation" value="org.apache.axis.attachments.AttachmentsImpl"/>
  <parameter name="sendXsiTypes" value="true"/>
  <requestFlow>
   <handler type="java:org.apache.axis.handlers.JWSHandler">
    <parameter name="scope" value="session"/>
   </handler>
   <handler type="java:org.apache.axis.handlers.JWSHandler">
    <parameter name="scope" value="request"/>
    <parameter name="extension" value=".jwr"/>
   </handler>
  </requestFlow>
 </globalConfiguration>
 <handler name="URLMapper" type="java:org.apache.axis.handlers.http.URLMapper"/>
 <handler name="Authenticate" type="java:org.apache.axis.handlers.SimpleAuthenticationHandler"/>
 <handler name="LocalResponder" type="java:org.apache.axis.transport.local.LocalResponder"/>
 <service name="AdminService" provider="java:MSG">
  <parameter name="allowedMethods" value="AdminService"/>
  <parameter name="enableRemoteAdmin" value="false"/>
  <parameter name="className" value="org.apache.axis.utils.Admin"/>
  <namespace>http://xml.apache.org/axis/wsdd/</namespace>
 </service>
 <service name="AgentService" provider="java:RPC">
  <operation name="getProjectName" qname="ns1:getProjectName" returnQName="getProjectNameReturn" returnType="xsd:string" soapAction="" xmlns:ns1="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="getFlowPoolNum" qname="ns2:getFlowPoolNum" returnQName="getFlowPoolNumReturn" returnType="xsd:long" soapAction="" xmlns:ns2="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="agentHost" type="xsd:string"/>
   <parameter name="prjFlowName" type="xsd:string"/>
  </operation>
  <operation name="appLog" qname="ns3:appLog" soapAction="" xmlns:ns3="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="LogMsg" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getWorkflowName" qname="ns4:getWorkflowName" returnQName="getWorkflowNameReturn" returnType="xsd:string" soapAction="" xmlns:ns4="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="getFlowInsName" qname="ns5:getFlowInsName" returnQName="getFlowInsNameReturn" returnType="xsd:string" soapAction="" xmlns:ns5="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="getSchemaByFullName" qname="ns6:getSchemaByFullName" returnQName="getSchemaByFullNameReturn" returnType="xsd:string" soapAction="" xmlns:ns6="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
   <parameter name="SchemaFullName" type="xsd:string"/>
  </operation>
  <operation name="getCurPrjSchemas" qname="ns7:getCurPrjSchemas" returnQName="getCurPrjSchemasReturn" returnType="ns7:ArrayOf_xsd_string" soapAction="" xmlns:ns7="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllSchema" qname="ns8:getAllSchema" returnQName="getAllSchemaReturn" returnType="ns8:ArrayOf_xsd_string" soapAction="" xmlns:ns8="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getEnvVarValueByName" qname="ns9:getEnvVarValueByName" returnQName="getEnvVarValueByNameReturn" returnType="xsd:string" soapAction="" xmlns:ns9="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
   <parameter name="VarName" type="xsd:string"/>
  </operation>
  <operation name="getEnvVarTypeByName" qname="ns10:getEnvVarTypeByName" returnQName="getEnvVarTypeByNameReturn" returnType="xsd:string" soapAction="" xmlns:ns10="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
   <parameter name="VarName" type="xsd:string"/>
  </operation>
  <operation name="getAllEnvValue" qname="ns12:getAllEnvValue" returnQName="getAllEnvValueReturn" returnType="ns11:StringMap" soapAction="" xmlns:ns11="http://agent.webservice.server.ieai.ideal.com" xmlns:ns12="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllEnvType" qname="ns14:getAllEnvType" returnQName="getAllEnvTypeReturn" returnType="ns13:StringMap" soapAction="" xmlns:ns13="http://agent.webservice.server.ieai.ideal.com" xmlns:ns14="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isInTransactionStruct" qname="ns15:isInTransactionStruct" returnQName="isInTransactionStructReturn" returnType="xsd:boolean" soapAction="" xmlns:ns15="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="executeJSAct" qname="ns17:executeJSAct" returnQName="executeJSActReturn" returnType="ns16:StringMap" soapAction="" xmlns:ns16="http://agent.webservice.server.ieai.ideal.com" xmlns:ns17="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="inputDef" type="ns17:ArrayOf_tns1_WSParameterDef"/>
   <parameter name="outputDef" type="ns17:ArrayOf_tns1_WSParameterDef"/>
   <parameter name="input" type="ns16:StringMap"/>
   <parameter name="jsCode" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllCommonActInfo" qname="ns18:getAllCommonActInfo" returnQName="getAllCommonActInfoReturn" returnType="ns18:ArrayOf_xsd_string" soapAction="" xmlns:ns18="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getNetworkRecoverWaitTime" qname="ns19:getNetworkRecoverWaitTime" returnQName="getNetworkRecoverWaitTimeReturn" returnType="xsd:long" soapAction="" xmlns:ns19="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="getCurCommAct" qname="ns20:getCurCommAct" returnQName="getCurCommActReturn" returnType="xsd:string" soapAction="" xmlns:ns20="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="getActivityStates" qname="ns21:getActivityStates" returnQName="getActivityStatesReturn" returnType="ns21:ArrayOf_xsd_string" soapAction="" xmlns:ns21="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actId" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isDisappear" qname="ns22:isDisappear" returnQName="isDisappearReturn" returnType="xsd:boolean" soapAction="" xmlns:ns22="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
   <parameter name="actId" type="xsd:int"/>
  </operation>
  <operation name="isFlowStopped" qname="ns23:isFlowStopped" returnQName="isFlowStoppedReturn" returnType="xsd:boolean" soapAction="" xmlns:ns23="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
  </operation>
  <operation name="getResourceByName" qname="ns24:getResourceByName" returnQName="getResourceByNameReturn" returnType="xsd:string" soapAction="" xmlns:ns24="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
   <parameter name="Name" type="xsd:string"/>
  </operation>
  <operation name="getAllResource" qname="ns25:getAllResource" returnQName="getAllResourceReturn" returnType="ns25:ArrayOf_xsd_string" soapAction="" xmlns:ns25="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getRExecRequest" qname="ns27:getRExecRequest" returnQName="getRExecRequestReturn" returnType="ns26:WSRExecRequest" soapAction="" xmlns:ns26="http://agent.webservice.server.ieai.ideal.com" xmlns:ns27="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="RequestUUID" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="loadShellFile" qname="ns28:loadShellFile" returnQName="loadShellFileReturn" returnType="xsd:base64Binary" soapAction="" xmlns:ns28="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="agentip" type="xsd:string"/>
   <parameter name="filenName" type="xsd:string"/>
  </operation>
  <operation name="resultForCM" qname="ns29:resultForCM" returnQName="resultForCMReturn" returnType="xsd:boolean" soapAction="" xmlns:ns29="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="RequestUUID" type="xsd:string"/>
   <parameter name="output" type="xsd:string"/>
  </operation>
  <operation name="remoteDownloadAdaptor" qname="ns30:remoteDownloadAdaptor" returnQName="remoteDownloadAdaptorReturn" returnType="xsd:base64Binary" soapAction="" xmlns:ns30="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="RequestUUID" type="xsd:string"/>
   <parameter name="adaptorName" type="xsd:string"/>
   <parameter name="AdaptorUUID" type="xsd:string"/>
  </operation>
  <operation name="updateRequestStatus" qname="ns31:updateRequestStatus" soapAction="" xmlns:ns31="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="ReuqestUUID" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Status" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Output" type="ns32:StringMap" xmlns:ns32="http://agent.webservice.server.ieai.ideal.com"/>
   <parameter name="exceptionSerialize" type="xsd:base64Binary" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="version" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="stateData" type="ns33:WSActStateData" xmlns:ns33="http://agent.webservice.server.ieai.ideal.com"/>
  </operation>
  <operation name="getCurPrjCalendarName" qname="ns34:getCurPrjCalendarName" returnQName="getCurPrjCalendarNameReturn" returnType="xsd:string" soapAction="" xmlns:ns34="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="ReuqestUUID" type="xsd:string"/>
   <parameter name="flowName" type="xsd:string"/>
  </operation>
  <operation name="updateRunInfoFile" qname="ns35:updateRunInfoFile" soapAction="" xmlns:ns35="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="ReuqestUUID" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="rightContent" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorContent" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="procIdContent" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="updateAgentAutoStatus" qname="ns36:updateAgentAutoStatus" soapAction="" xmlns:ns36="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="agentIp" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="agentPort" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="revisionaf" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="retrunCheckInfo" qname="ns37:retrunCheckInfo" soapAction="" xmlns:ns37="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="prjName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="agentIp" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="port" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="cpu" type="xsd:double" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="memry" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="retrunSysState" qname="ns38:retrunSysState" soapAction="" xmlns:ns38="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="sysState" type="ns39:StringMap" xmlns:ns39="http://agent.webservice.server.ieai.ideal.com"/>
  </operation>
  <operation name="isIPAdress" qname="ns40:isIPAdress" returnQName="isIPAdressReturn" returnType="xsd:boolean" soapAction="" xmlns:ns40="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="str" type="xsd:string"/>
  </operation>
  <operation name="getJarFile" qname="ns41:getJarFile" returnQName="getJarFileReturn" returnType="xsd:base64Binary" soapAction="" xmlns:ns41="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="fileName" type="xsd:string"/>
  </operation>
  <operation name="evaluate" qname="ns42:evaluate" returnQName="evaluateReturn" returnType="xsd:string" soapAction="" xmlns:ns42="http://agent.webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="requestId" type="xsd:string"/>
   <parameter name="expr" type="xsd:string"/>
  </operation>
  <operation name="evaluate" qname="ns44:evaluate" returnQName="evaluateReturn" returnType="ns43:anyType" soapAction="" xmlns:ns43="http://www.w3.org/1999/XMLSchema" xmlns:ns44="http://agent.webservice.v30.ieai.ideal.com">
   <parameter name="StrExpr" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <parameter name="allowedMethods" value="updateAgentAutoStatus evaluate executeJSAct getNetworkRecoverWaitTime appLog retrunCheckInfo getEnvVarTypeByName loadShellFile getFlowInsName getResourceByName getSchemaByFullName getAllEnvValue getJarFile updateRunInfoFile getEnvVarValueByName remoteDownloadAdaptor getRExecRequest retrunSysState isDisappear getAllEnvType getFlowPoolNum getCurCommAct getAllResource resultForCM isIPAdress getProjectName getCurPrjCalendarName isFlowStopped updateRequestStatus getAllCommonActInfo getAllSchema getCurPrjSchemas isInTransactionStruct getActivityStates getWorkflowName"/>
  <parameter name="wsdlPortType" value="AgentService"/>
  <parameter name="wsdlServicePort" value="AgentService"/>
  <parameter name="className" value="com.ideal.ieai.server.webservice.agent.AgentService"/>
  <parameter name="wsdlTargetNamespace" value="http://agent.webservice.v30.ieai.ideal.com"/>
  <parameter name="wsdlServiceElement" value="AgentServiceService"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.ArrayDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns45:ArrayOf_tns1_WSParameterDef" serializer="org.apache.axis.encoding.ser.ArraySerializerFactory" type="java:com.ideal.ieai.server.webservice.agent.WSParameterDef[]" xmlns:ns45="http://agent.webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns46:StringMap" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.agent.StringMap" xmlns:ns46="http://agent.webservice.server.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.ArrayDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns47:ArrayOf_xsd_string" serializer="org.apache.axis.encoding.ser.ArraySerializerFactory" type="java:java.lang.String[]" xmlns:ns47="http://agent.webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns48:WSParameterDef" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.agent.WSParameterDef" xmlns:ns48="http://agent.webservice.server.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns49:WSRExecRequest" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.agent.WSRExecRequest" xmlns:ns49="http://agent.webservice.server.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns50:WSActStateData" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.agent.WSActStateData" xmlns:ns50="http://agent.webservice.server.ieai.ideal.com"/>
 </service>
 <service name="Version" provider="java:RPC">
  <parameter name="allowedMethods" value="getVersion"/>
  <parameter name="className" value="org.apache.axis.Version"/>
 </service>
 <service name="IEAIService" provider="java:RPC">
  <operation name="login" qname="ns51:login" returnQName="loginReturn" returnType="ns51:WsClientSession" soapAction="" xmlns:ns51="http://webservice.v30.ieai.ideal.com">
   <parameter name="session" type="ns51:WsClientSession"/>
   <parameter name="password" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="forceLogin" qname="ns52:forceLogin" returnQName="forceLoginReturn" returnType="ns52:WsClientSession" soapAction="" xmlns:ns52="http://webservice.v30.ieai.ideal.com">
   <parameter name="session" type="ns52:WsClientSession"/>
   <parameter name="password" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="logout" qname="ns53:logout" soapAction="" xmlns:ns53="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="autoLogout" qname="ns54:autoLogout" soapAction="" xmlns:ns54="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isSessionValid" qname="ns55:isSessionValid" returnQName="isSessionValidReturn" returnType="xsd:boolean" soapAction="" xmlns:ns55="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessionId" type="xsd:string"/>
  </operation>
  <operation name="getAllClientSessions" qname="ns56:getAllClientSessions" returnQName="getAllClientSessionsReturn" returnType="ns56:ArrayOfWsClientSession" soapAction="" xmlns:ns56="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="kickoutSession" qname="ns57:kickoutSession" returnQName="kickoutSessionReturn" returnType="ns57:WsClientSession" soapAction="" xmlns:ns57="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="targetSessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="addSNToDomain" qname="ns58:addSNToDomain" soapAction="" xmlns:ns58="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Ip" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="freezeServiceNode" qname="ns59:freezeServiceNode" soapAction="" xmlns:ns59="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Ip" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllNodes" qname="ns60:getAllNodes" returnQName="getAllNodesReturn" returnType="ns60:ArrayOfServiceNode" soapAction="" xmlns:ns60="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAvailableNodes" qname="ns61:getAvailableNodes" returnQName="getAvailableNodesReturn" returnType="ns61:ArrayOfServiceNode" soapAction="" xmlns:ns61="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getCurrServiceNode" qname="ns62:getCurrServiceNode" returnQName="getCurrServiceNodeReturn" returnType="ns62:ServiceNode" soapAction="" xmlns:ns62="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getIEAIGroupMembers" qname="ns63:getIEAIGroupMembers" returnQName="getIEAIGroupMembersReturn" returnType="ns63:ArrayOfAddress" soapAction="" xmlns:ns63="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getServiceNode" qname="ns64:getServiceNode" returnQName="getServiceNodeReturn" returnType="ns64:ServiceNode" soapAction="" xmlns:ns64="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="ip" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isDBNormal" qname="ns65:isDBNormal" returnQName="isDBNormalReturn" returnType="xsd:boolean" soapAction="" xmlns:ns65="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
  </operation>
  <operation name="isNodeStarted" qname="ns66:isNodeStarted" returnQName="isNodeStartedReturn" returnType="xsd:boolean" soapAction="" xmlns:ns66="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="ip" type="xsd:string"/>
  </operation>
  <operation name="removeSNFromDomain" qname="ns67:removeSNFromDomain" soapAction="" xmlns:ns67="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="ip" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="shutdownSN" qname="ns68:shutdownSN" soapAction="" xmlns:ns68="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="ip" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="unfreezeServiceNode" qname="ns69:unfreezeServiceNode" soapAction="" xmlns:ns69="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="ip" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getDomainInfo" qname="ns70:getDomainInfo" returnQName="getDomainInfoReturn" returnType="ns70:DomainInfo" soapAction="" xmlns:ns70="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFlowDefinition" qname="ns71:getFlowDefinition" returnQName="getFlowDefinitionReturn" returnType="ns71:WSProjectDef" soapAction="" xmlns:ns71="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="prjName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFlowTaskNames" qname="ns72:getFlowTaskNames" returnQName="getFlowTaskNamesReturn" returnType="ns72:ArrayOf_xsd_string" soapAction="" xmlns:ns72="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="prjName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getPartInfo" qname="ns73:getPartInfo" returnQName="getPartInfoReturn" returnType="ns73:PartInfo" soapAction="" xmlns:ns73="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="prjName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flwName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getCanStartProjects" qname="ns74:getCanStartProjects" returnQName="getCanStartProjectsReturn" returnType="ns74:ArrayOfProjectInfo" soapAction="" xmlns:ns74="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getRelatedProject" qname="ns75:getRelatedProject" returnQName="getRelatedProjectReturn" returnType="ns75:ArrayOfProjectInfo" soapAction="" xmlns:ns75="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUAProjectList" qname="ns76:getUAProjectList" returnQName="getUAProjectListReturn" returnType="ns76:ArrayOfProjectInfo" soapAction="" xmlns:ns76="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="deleteProject" qname="ns77:deleteProject" soapAction="" xmlns:ns77="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="downloadProject" qname="ns78:downloadProject" returnQName="downloadProjectReturn" returnType="xsd:base64Binary" soapAction="" xmlns:ns78="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="projectName" type="xsd:string"/>
  </operation>
  <operation name="freezeProject" qname="ns79:freezeProject" soapAction="" xmlns:ns79="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="unfreezeProject" qname="ns80:unfreezeProject" soapAction="" xmlns:ns80="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isProjectExist" qname="ns81:isProjectExist" returnQName="isProjectExistReturn" returnType="xsd:boolean" soapAction="" xmlns:ns81="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="projectName" type="xsd:string"/>
  </operation>
  <operation name="isAdaptorExist" qname="ns82:isAdaptorExist" returnQName="isAdaptorExistReturn" returnType="xsd:boolean" soapAction="" xmlns:ns82="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="projectName" type="xsd:string"/>
  </operation>
  <operation name="existProject" qname="ns83:existProject" returnQName="existProjectReturn" returnType="xsd:int" soapAction="" xmlns:ns83="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="projectName" type="xsd:string"/>
  </operation>
  <operation name="updateProject" qname="ns84:updateProject" soapAction="" xmlns:ns84="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="comment" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getProjectVersionHistory" qname="ns85:getProjectVersionHistory" returnQName="getProjectVersionHistoryReturn" returnType="ns85:ArrayOfProjectInfo" soapAction="" xmlns:ns85="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getProjectLogHistory" qname="ns86:getProjectLogHistory" returnQName="getProjectLogHistoryReturn" returnType="ns86:ArrayOfProjectHistoryInfo" soapAction="" xmlns:ns86="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getRelatedAdaptor" qname="ns87:getRelatedAdaptor" returnQName="getRelatedAdaptorReturn" returnType="ns87:ArrayOfAdaptorInfo" soapAction="" xmlns:ns87="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUAAdaptorList" qname="ns88:getUAAdaptorList" returnQName="getUAAdaptorListReturn" returnType="ns88:ArrayOfAdaptorInfo" soapAction="" xmlns:ns88="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="deleteAdaptor" qname="ns89:deleteAdaptor" soapAction="" xmlns:ns89="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="adaptorName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="downloadAdaptor" qname="ns90:downloadAdaptor" returnQName="downloadAdaptorReturn" returnType="xsd:base64Binary" soapAction="" xmlns:ns90="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="adaptorName" type="xsd:string"/>
  </operation>
  <operation name="freezeAdaptor" qname="ns91:freezeAdaptor" soapAction="" xmlns:ns91="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="adaptorName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="unfreezeAdaptor" qname="ns92:unfreezeAdaptor" soapAction="" xmlns:ns92="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="adaptorName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="updateAdaptor" qname="ns93:updateAdaptor" soapAction="" xmlns:ns93="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="adaptorName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="comment" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAdaptorVersionHistory" qname="ns94:getAdaptorVersionHistory" returnQName="getAdaptorVersionHistoryReturn" returnType="ns94:ArrayOfAdaptorInfo" soapAction="" xmlns:ns94="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="adaptorName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAdaptorLogHistory" qname="ns95:getAdaptorLogHistory" returnQName="getAdaptorLogHistoryReturn" returnType="ns95:ArrayOfAdaptorHistoryInfo" soapAction="" xmlns:ns95="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="adaptroName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="acquireTask" qname="ns96:acquireTask" soapAction="" xmlns:ns96="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="forceAqcuire" type="xsd:boolean" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="approveDelegatedTask" qname="ns97:approveDelegatedTask" soapAction="" xmlns:ns97="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="cancleTaskDelegation" qname="ns98:cancleTaskDelegation" soapAction="" xmlns:ns98="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="completeTask" qname="ns99:completeTask" soapAction="" xmlns:ns99="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="delegateTask" qname="ns100:delegateTask" soapAction="" xmlns:ns100="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="UserId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="forwardTask" qname="ns101:forwardTask" soapAction="" xmlns:ns101="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="UserId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFlowInfosByPendingTasks" qname="ns102:getFlowInfosByPendingTasks" returnQName="getFlowInfosByPendingTasksReturn" returnType="ns102:ArrayOfWorkflowInfo" soapAction="" xmlns:ns102="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getPendingTasks" qname="ns103:getPendingTasks" returnQName="getPendingTasksReturn" returnType="ns103:ArrayOfTaskInfo" soapAction="" xmlns:ns103="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTaskDeadline" qname="ns104:getTaskDeadline" returnQName="getTaskDeadlineReturn" returnType="xsd:dateTime" soapAction="" xmlns:ns104="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="SessId" type="xsd:string"/>
   <parameter name="TaskId" type="xsd:long"/>
  </operation>
  <operation name="getTaskDelegateCandidates" qname="ns105:getTaskDelegateCandidates" returnQName="getTaskDelegateCandidatesReturn" returnType="ns105:ArrayOfWsUserBasicInfo" soapAction="" xmlns:ns105="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTaskDetail" qname="ns106:getTaskDetail" returnQName="getTaskDetailReturn" returnType="ns106:TaskInfo" soapAction="" xmlns:ns106="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTaskForwardCandidates" qname="ns107:getTaskForwardCandidates" returnQName="getTaskForwardCandidatesReturn" returnType="ns107:ArrayOfWsUserBasicInfo" soapAction="" xmlns:ns107="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTasksOfJob" qname="ns108:getTasksOfJob" returnQName="getTasksOfJobReturn" returnType="ns108:ArrayOfTaskInfo" soapAction="" xmlns:ns108="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getOnlyTaskOwnerWithoutFinishedSkipped" qname="ns109:getOnlyTaskOwnerWithoutFinishedSkipped" returnQName="getOnlyTaskOwnerWithoutFinishedSkippedReturn" returnType="ns109:ArrayOfTaskInfo" soapAction="" xmlns:ns109="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTasksToAlert" qname="ns110:getTasksToAlert" returnQName="getTasksToAlertReturn" returnType="ns110:ArrayOfTaskInfo" soapAction="" xmlns:ns110="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="hasTaskAlert" qname="ns111:hasTaskAlert" returnQName="hasTaskAlertReturn" returnType="xsd:boolean" soapAction="" xmlns:ns111="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="SessionId" type="xsd:string"/>
  </operation>
  <operation name="isTaskManager" qname="ns112:isTaskManager" returnQName="isTaskManagerReturn" returnType="xsd:boolean" soapAction="" xmlns:ns112="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="SessId" type="xsd:string"/>
   <parameter name="TaskId" type="xsd:long"/>
  </operation>
  <operation name="performActOnTask" qname="ns113:performActOnTask" soapAction="" xmlns:ns113="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="OpId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="queryTask" qname="ns114:queryTask" returnQName="queryTaskReturn" returnType="ns114:TaskQueryResult" soapAction="" xmlns:ns114="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Criteria" type="ns114:TaskQueryCriteria"/>
  </operation>
  <operation name="queryTaskHistory" qname="ns115:queryTaskHistory" returnQName="queryTaskHistoryReturn" returnType="ns115:ArrayOfTaskHistory" soapAction="" xmlns:ns115="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="filter" type="ns115:WsAppLogFilter"/>
  </operation>
  <operation name="rejectDelegatedTask" qname="ns116:rejectDelegatedTask" soapAction="" xmlns:ns116="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="releaseTask" qname="ns117:releaseTask" soapAction="" xmlns:ns117="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setPropertyValue" qname="ns118:setPropertyValue" soapAction="" xmlns:ns118="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="wspev" type="ns118:WSParamEnvValue"/>
  </operation>
  <operation name="getTaskId" qname="ns119:getTaskId" returnQName="getTaskIdReturn" returnType="xsd:long" soapAction="" xmlns:ns119="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="flowId" type="xsd:long"/>
   <parameter name="actId" type="xsd:long"/>
  </operation>
  <operation name="getFlowParameters" qname="ns120:getFlowParameters" returnQName="getFlowParametersReturn" returnType="ns120:ArrayOfWSParamEnvValue" soapAction="" xmlns:ns120="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAttachments" qname="ns121:getAttachments" returnQName="getAttachmentsReturn" returnType="ns121:ArrayOfTaskAttachment" soapAction="" xmlns:ns121="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="withContent" type="xsd:boolean" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAttachment" qname="ns122:getAttachment" returnQName="getAttachmentReturn" returnType="ns122:TaskAttachment" soapAction="" xmlns:ns122="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="AttachmentId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFullActInfo" qname="ns123:getFullActInfo" returnQName="getFullActInfoReturn" returnType="ns123:WsFullActInfo" soapAction="" xmlns:ns123="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="addTaskAttachment" qname="ns124:addTaskAttachment" returnQName="addTaskAttachmentReturn" returnType="xsd:long" soapAction="" xmlns:ns124="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="SessId" type="xsd:string"/>
   <parameter name="TaskId" type="xsd:long"/>
   <parameter name="AttachName" type="xsd:string"/>
   <parameter name="Content" type="xsd:base64Binary"/>
  </operation>
  <operation name="setTaskAttachment" qname="ns125:setTaskAttachment" soapAction="" xmlns:ns125="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="AttachmentId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="AttachmentName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Content" type="xsd:base64Binary" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="deleteTaskAttachment" qname="ns126:deleteTaskAttachment" soapAction="" xmlns:ns126="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="AttachmentId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="forceReleaseTaskOwner" qname="ns127:forceReleaseTaskOwner" soapAction="" xmlns:ns127="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setTaskOwner" qname="ns128:setTaskOwner" soapAction="" xmlns:ns128="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskOwnerId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="skipTask" qname="ns129:skipTask" soapAction="" xmlns:ns129="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="Desc" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="updateFlowProperties" qname="ns130:updateFlowProperties" soapAction="" xmlns:ns130="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="TaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="wspevs" type="ns130:ArrayOfWSParamEnvValue"/>
  </operation>
  <operation name="finishTaskItem" qname="ns131:finishTaskItem" soapAction="" xmlns:ns131="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="itemId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="updateTaskProperties" qname="ns132:updateTaskProperties" soapAction="" xmlns:ns132="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskParams" type="ns132:ArrayOfWSTaskProperty"/>
  </operation>
  <operation name="checkUserPermission" qname="ns133:checkUserPermission" returnQName="checkUserPermissionReturn" returnType="xsd:boolean" soapAction="" xmlns:ns133="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="loginId" type="xsd:string"/>
   <parameter name="type" type="xsd:short"/>
   <parameter name="prjAdpName" type="xsd:string"/>
   <parameter name="pid" type="xsd:string"/>
  </operation>
  <operation name="checkUserPermission" qname="ns134:checkUserPermission" returnQName="checkUserPermissionReturn" returnType="xsd:boolean" soapAction="" xmlns:ns134="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="userId" type="xsd:long"/>
   <parameter name="type" type="xsd:int"/>
   <parameter name="prjAdpName" type="xsd:string"/>
   <parameter name="pID" type="xsd:string"/>
  </operation>
  <operation name="updateAccountInfo" qname="ns135:updateAccountInfo" soapAction="" xmlns:ns135="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userInfo" type="ns135:WsUserBasicInfo"/>
  </operation>
  <operation name="getUserCalendarInfo" qname="ns136:getUserCalendarInfo" returnQName="getUserCalendarInfoReturn" returnType="ns136:CalendarInfo" soapAction="" xmlns:ns136="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="createRole" qname="ns137:createRole" returnQName="createRoleReturn" returnType="xsd:long" soapAction="" xmlns:ns137="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="role" type="ns137:WsRoleInfo"/>
  </operation>
  <operation name="createUser" qname="ns138:createUser" returnQName="createUserReturn" returnType="xsd:long" soapAction="" xmlns:ns138="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="user" type="ns138:WsUserBasicInfo"/>
  </operation>
  <operation name="getAllRole" qname="ns139:getAllRole" returnQName="getAllRoleReturn" returnType="ns139:ArrayOfWsRoleInfo" soapAction="" xmlns:ns139="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllUser" qname="ns140:getAllUser" returnQName="getAllUserReturn" returnType="ns140:ArrayOfWsUserBasicInfo" soapAction="" xmlns:ns140="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getRole" qname="ns141:getRole" returnQName="getRoleReturn" returnType="ns141:WsRoleInfo" soapAction="" xmlns:ns141="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="roleID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getRolesFromUser" qname="ns142:getRolesFromUser" returnQName="getRolesFromUserReturn" returnType="ns142:ArrayOfWsRoleInfo" soapAction="" xmlns:ns142="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUser" qname="ns143:getUser" returnQName="getUserReturn" returnType="ns143:WsUserBasicInfo" soapAction="" xmlns:ns143="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getSelfInfo" qname="ns144:getSelfInfo" returnQName="getSelfInfoReturn" returnType="ns144:WsUserBasicInfo" soapAction="" xmlns:ns144="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUsersFromRole" qname="ns145:getUsersFromRole" returnQName="getUsersFromRoleReturn" returnType="ns145:ArrayOfWsUserBasicInfo" soapAction="" xmlns:ns145="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="roleId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isSA" qname="ns146:isSA" returnQName="isSAReturn" returnType="xsd:boolean" soapAction="" xmlns:ns146="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
  </operation>
  <operation name="isUA" qname="ns147:isUA" returnQName="isUAReturn" returnType="xsd:boolean" soapAction="" xmlns:ns147="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
  </operation>
  <operation name="isUserInRole" qname="ns148:isUserInRole" returnQName="isUserInRoleReturn" returnType="xsd:boolean" soapAction="" xmlns:ns148="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="UserID" type="xsd:long"/>
   <parameter name="roleID" type="xsd:long"/>
  </operation>
  <operation name="removeRoles" qname="ns149:removeRoles" soapAction="" xmlns:ns149="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="roles" type="ns149:ArrayOf_xsd_long"/>
  </operation>
  <operation name="removeRole" qname="ns150:removeRole" soapAction="" xmlns:ns150="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="roleId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="removeUsers" qname="ns151:removeUsers" soapAction="" xmlns:ns151="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="UserIDs" type="ns151:ArrayOf_xsd_long"/>
  </operation>
  <operation name="removeUser" qname="ns152:removeUser" soapAction="" xmlns:ns152="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="UserID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setRoleUsers" qname="ns153:setRoleUsers" returnQName="setRoleUsersReturn" returnType="xsd:boolean" soapAction="" xmlns:ns153="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="UserIDs" type="ns153:ArrayOf_xsd_long"/>
   <parameter name="roleID" type="xsd:long"/>
  </operation>
  <operation name="setUserRoles" qname="ns154:setUserRoles" returnQName="setUserRolesReturn" returnType="xsd:boolean" soapAction="" xmlns:ns154="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="roleIDs" type="ns154:ArrayOf_xsd_long"/>
   <parameter name="UserID" type="xsd:long"/>
  </operation>
  <operation name="updateRole" qname="ns155:updateRole" soapAction="" xmlns:ns155="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="ModRole" type="ns155:WsRoleInfo"/>
  </operation>
  <operation name="updateUser" qname="ns156:updateUser" soapAction="" xmlns:ns156="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="ModUser" type="ns156:WsUserBasicInfo"/>
  </operation>
  <operation name="checkOwnPermission" qname="ns157:checkOwnPermission" returnQName="checkOwnPermissionReturn" returnType="xsd:boolean" soapAction="" xmlns:ns157="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="type" type="xsd:short"/>
   <parameter name="permissionId" type="xsd:string"/>
   <parameter name="prjAdpName" type="xsd:string"/>
  </operation>
  <operation name="getRolePermissionSet" qname="ns158:getRolePermissionSet" returnQName="getRolePermissionSetReturn" returnType="ns158:PermissionSet" soapAction="" xmlns:ns158="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="roleID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUserPermissionSet" qname="ns159:getUserPermissionSet" returnQName="getUserPermissionSetReturn" returnType="ns159:PermissionSet" soapAction="" xmlns:ns159="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setRolePermissionSet" qname="ns160:setRolePermissionSet" soapAction="" xmlns:ns160="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="PermSet" type="ns160:PermissionSet"/>
   <parameter name="roleID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setUserPermissionSet" qname="ns161:setUserPermissionSet" soapAction="" xmlns:ns161="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="PermSet" type="ns161:PermissionSet"/>
   <parameter name="UserID" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="updateUserRelatedInfo" qname="ns162:updateUserRelatedInfo" soapAction="" xmlns:ns162="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userInfo" type="ns162:WsUserBasicInfo"/>
  </operation>
  <operation name="setSAPwd" qname="ns163:setSAPwd" soapAction="" xmlns:ns163="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="password" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setUAPwd" qname="ns164:setUAPwd" soapAction="" xmlns:ns164="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="password" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="setUserSelfInfo" qname="ns165:setUserSelfInfo" soapAction="" xmlns:ns165="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userInfo" type="ns165:WsUserBasicInfo"/>
  </operation>
  <operation name="setFlowLogConfig" qname="ns166:setFlowLogConfig" soapAction="" xmlns:ns166="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="prjName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="config" type="ns166:WorkflwLogConfig"/>
  </operation>
  <operation name="getFlowLogConfig" qname="ns167:getFlowLogConfig" returnQName="getFlowLogConfigReturn" returnType="ns167:WorkflwLogConfig" soapAction="" xmlns:ns167="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="prjName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllFlowsInfo" qname="ns168:getAllFlowsInfo" returnQName="getAllFlowsInfoReturn" returnType="ns168:ArrayOfWorkflowInfo" soapAction="" xmlns:ns168="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getSubFlowInfo" qname="ns169:getSubFlowInfo" returnQName="getSubFlowInfoReturn" returnType="ns169:ArrayOfSubFlowInfo" soapAction="" xmlns:ns169="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="callFlowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getActType" qname="ns170:getActType" returnQName="getActTypeReturn" returnType="xsd:int" soapAction="" xmlns:ns170="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="flowId" type="xsd:long"/>
   <parameter name="actId" type="xsd:long"/>
  </operation>
  <operation name="getBasicActInfo" qname="ns171:getBasicActInfo" returnQName="getBasicActInfoReturn" returnType="ns171:BasicActInfo" soapAction="" xmlns:ns171="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFlowSnapshot" qname="ns172:getFlowSnapshot" returnQName="getFlowSnapshotReturn" returnType="xsd:base64Binary" soapAction="" xmlns:ns172="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="flowId" type="xsd:long"/>
   <parameter name="imageType" type="xsd:string"/>
  </operation>
  <operation name="getActNodeInfo" qname="ns173:getActNodeInfo" returnQName="getActNodeInfoReturn" returnType="ns173:ArrayOfActNodeInfo" soapAction="" xmlns:ns173="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFlowInfo" qname="ns174:getFlowInfo" returnQName="getFlowInfoReturn" returnType="ns174:WorkflowInfo" soapAction="" xmlns:ns174="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getFlowInfos" qname="ns175:getFlowInfos" returnQName="getFlowInfosReturn" returnType="ns175:ArrayOfWorkflowInfo" soapAction="" xmlns:ns175="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowIds" type="ns175:ArrayOf_xsd_long"/>
  </operation>
  <operation name="killFlow" qname="ns176:killFlow" soapAction="" xmlns:ns176="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="delWarningInfo" qname="ns177:delWarningInfo" soapAction="" xmlns:ns177="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="iid" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="queryWarningInfo" qname="ns178:queryWarningInfo" returnQName="queryWarningInfoReturn" returnType="ns178:ArrayOfWarningInfo" soapAction="" xmlns:ns178="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="stopFlow" qname="ns179:stopFlow" soapAction="" xmlns:ns179="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getCurRunningAct" qname="ns180:getCurRunningAct" returnQName="getCurRunningActReturn" returnType="ns180:ArrayOf_xsd_string" soapAction="" xmlns:ns180="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowIds" type="ns180:ArrayOf_xsd_long"/>
  </operation>
  <operation name="pauseFlow" qname="ns181:pauseFlow" soapAction="" xmlns:ns181="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="resumeFlow" qname="ns182:resumeFlow" soapAction="" xmlns:ns182="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="queryFlow" qname="ns183:queryFlow" returnQName="queryFlowReturn" returnType="ns183:ArrayOfWorkflowInfo" soapAction="" xmlns:ns183="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="filter" type="ns183:WorkflowFilter"/>
  </operation>
  <operation name="startFlow" qname="ns184:startFlow" returnQName="startFlowReturn" returnType="xsd:long" soapAction="" xmlns:ns184="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="SessId" type="xsd:string"/>
   <parameter name="projName" type="xsd:string"/>
   <parameter name="flowName" type="xsd:string"/>
   <parameter name="parameters" type="ns184:ArrayOfWSParamEnvValue"/>
   <parameter name="flowInsName" type="xsd:string"/>
   <parameter name="comment" type="xsd:string"/>
   <parameter name="isForceEfficientFirst" type="xsd:boolean"/>
  </operation>
  <operation name="startFlow" qname="ns185:startFlow" returnQName="startFlowReturn" returnType="xsd:long" soapAction="" xmlns:ns185="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="SessId" type="xsd:string"/>
   <parameter name="projName" type="xsd:string"/>
   <parameter name="flowName" type="xsd:string"/>
   <parameter name="parameters" type="ns185:ArrayOfWSParamEnvValue"/>
   <parameter name="envVarValues" type="ns185:ArrayOfWSParamEnvValue"/>
   <parameter name="config" type="ns185:WorkflwLogConfig"/>
   <parameter name="flowInsName" type="xsd:string"/>
   <parameter name="comment" type="xsd:string"/>
   <parameter name="isForceEfficientFirst" type="xsd:boolean"/>
  </operation>
  <operation name="getAllActInfo" qname="ns186:getAllActInfo" returnQName="getAllActInfoReturn" returnType="ns186:ArrayOfActivityRuntimeInfo" soapAction="" xmlns:ns186="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getAllMonitorActCfg" qname="ns187:getAllMonitorActCfg" returnQName="getAllMonitorActCfgReturn" returnType="ns187:ArrayOfMonitorActConfig" soapAction="" xmlns:ns187="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="announceNotice" qname="ns188:announceNotice" returnQName="announceNoticeReturn" returnType="ns188:WsNoticeQueryResult" soapAction="" xmlns:ns188="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="notice" type="ns188:WsNotice"/>
  </operation>
  <operation name="confirmUserNotice" qname="ns189:confirmUserNotice" soapAction="" xmlns:ns189="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="noticeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="feedback" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="confirmTaskNotice" qname="ns190:confirmTaskNotice" soapAction="" xmlns:ns190="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="noticeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="feedback" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getNotice" qname="ns191:getNotice" returnQName="getNoticeReturn" returnType="ns191:WsNoticeQueryResult" soapAction="" xmlns:ns191="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="noticeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTaskNotices" qname="ns192:getTaskNotices" returnQName="getTaskNoticesReturn" returnType="ns192:ArrayOfWsNoticeQueryResult" soapAction="" xmlns:ns192="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getLoginNotices" qname="ns193:getLoginNotices" returnQName="getLoginNoticesReturn" returnType="ns193:ArrayOfWsNoticeQueryResult" soapAction="" xmlns:ns193="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getTimeNotices" qname="ns194:getTimeNotices" returnQName="getTimeNoticesReturn" returnType="ns194:ArrayOfWsNoticeQueryResult" soapAction="" xmlns:ns194="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="invalidateNotice" qname="ns195:invalidateNotice" soapAction="" xmlns:ns195="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="noticeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="modifyNotifyMethod" qname="ns196:modifyNotifyMethod" returnQName="modifyNotifyMethodReturn" returnType="ns196:WsNoticeQueryResult" soapAction="" xmlns:ns196="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="noticeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="method" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="queryNotice" qname="ns197:queryNotice" returnQName="queryNoticeReturn" returnType="ns197:ArrayOfWsNoticeQueryResult" soapAction="" xmlns:ns197="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="critria" type="ns197:WsQueryNoticeCriteria"/>
  </operation>
  <operation name="getNoticePermission" qname="ns198:getNoticePermission" returnQName="getNoticePermissionReturn" returnType="ns198:WsNoticeAnnouncePermission" soapAction="" xmlns:ns198="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="deleteNotice" qname="ns199:deleteNotice" soapAction="" xmlns:ns199="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="noticeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="queryFlowDefination" qname="ns200:queryFlowDefination" returnQName="queryFlowDefinationReturn" returnType="ns200:ArrayOfWSFlowDefReport" soapAction="" xmlns:ns200="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="criteria" type="ns200:WSFlowDefReportCriteria"/>
  </operation>
  <operation name="queryActInfo" qname="ns201:queryActInfo" returnQName="queryActInfoReturn" returnType="ns201:ArrayOfWSActRunInfoReport" soapAction="" xmlns:ns201="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="criteria" type="ns201:WSActRunInfoReportCriteria"/>
  </operation>
  <operation name="queryActRunStatistic" qname="ns202:queryActRunStatistic" returnQName="queryActRunStatisticReturn" returnType="ns202:ArrayOfWSActRunInfoStatReport" soapAction="" xmlns:ns202="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="criteria" type="ns202:WSActRunInfoStatReportCriteria"/>
  </operation>
  <operation name="queryActUseTimeStatistic" qname="ns203:queryActUseTimeStatistic" returnQName="queryActUseTimeStatisticReturn" returnType="ns203:ArrayOfWSActRunUseTimeReport" soapAction="" xmlns:ns203="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="criteria" type="ns203:WSActRunUseTimeReportCriteria"/>
  </operation>
  <operation name="getActValidTimeCfg" qname="ns204:getActValidTimeCfg" returnQName="getActValidTimeCfgReturn" returnType="ns204:ArrayOfWSActValidTimeCfg" soapAction="" xmlns:ns204="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actRunInfoId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUserPasswordFormat" qname="ns205:getUserPasswordFormat" returnQName="getUserPasswordFormatReturn" returnType="ns205:UserPasswordFormat" soapAction="" xmlns:ns205="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="checkUserPassword" qname="ns206:checkUserPassword" returnQName="checkUserPasswordReturn" returnType="xsd:boolean" soapAction="" xmlns:ns206="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessionId" type="xsd:string"/>
   <parameter name="userLoginName" type="xsd:string"/>
   <parameter name="userPassword" type="xsd:string"/>
  </operation>
  <operation name="getSysConfig" qname="ns207:getSysConfig" returnQName="getSysConfigReturn" returnType="ns207:WSSysConfig" soapAction="" xmlns:ns207="http://webservice.v30.ieai.ideal.com">
   <parameter name="sess" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="queryRunningActForUTIndex" qname="ns208:queryRunningActForUTIndex" returnQName="queryRunningActForUTIndexReturn" returnType="ns208:ArrayOfWsRunningActQueryResult" soapAction="" xmlns:ns208="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="projectName" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="isAll" type="xsd:boolean" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getActivityRelatedRuntimes" qname="ns209:getActivityRelatedRuntimes" returnQName="getActivityRelatedRuntimesReturn" returnType="ns209:WsActivityRelatedRuntimes" soapAction="" xmlns:ns209="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actId" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getErrorTask" qname="ns210:getErrorTask" returnQName="getErrorTaskReturn" returnType="ns210:WsErrorTask" soapAction="" xmlns:ns210="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getCanTakeOperationsOfErrorTask" qname="ns211:getCanTakeOperationsOfErrorTask" returnQName="getCanTakeOperationsOfErrorTaskReturn" returnType="ns211:WsOperationConfiguration" soapAction="" xmlns:ns211="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="operateOnErrorTask" qname="ns212:operateOnErrorTask" soapAction="" xmlns:ns212="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="operation" type="ns212:WsErrorTaskOperation"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="forwardErrorTask" qname="ns213:forwardErrorTask" returnQName="forwardErrorTaskReturn" returnType="ns213:WsErrorTask" soapAction="" xmlns:ns213="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="forwordeeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="delegateErrorTask" qname="ns214:delegateErrorTask" returnQName="delegateErrorTaskReturn" returnType="ns214:WsErrorTask" soapAction="" xmlns:ns214="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="delegateeId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="cancelErrorTaskDelegation" qname="ns215:cancelErrorTaskDelegation" returnQName="cancelErrorTaskDelegationReturn" returnType="ns215:WsErrorTask" soapAction="" xmlns:ns215="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="rejectDelegateeOperationOfErrorTask" qname="ns216:rejectDelegateeOperationOfErrorTask" returnQName="rejectDelegateeOperationOfErrorTaskReturn" returnType="ns216:WsErrorTask" soapAction="" xmlns:ns216="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="acceptDelegatieeOperationOfErrorTask" qname="ns217:acceptDelegatieeOperationOfErrorTask" returnQName="acceptDelegatieeOperationOfErrorTaskReturn" returnType="ns217:WsErrorTask" soapAction="" xmlns:ns217="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="releaseErrorTask" qname="ns218:releaseErrorTask" returnQName="releaseErrorTaskReturn" returnType="ns218:WsErrorTask" soapAction="" xmlns:ns218="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="forceReleaseErrorTask" qname="ns219:forceReleaseErrorTask" returnQName="forceReleaseErrorTaskReturn" returnType="ns219:WsErrorTask" soapAction="" xmlns:ns219="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="acquireErrorTask" qname="ns220:acquireErrorTask" returnQName="acquireErrorTaskReturn" returnType="ns220:WsErrorTask" soapAction="" xmlns:ns220="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="forceAcquireErrorTask" qname="ns221:forceAcquireErrorTask" returnQName="forceAcquireErrorTaskReturn" returnType="ns221:WsErrorTask" soapAction="" xmlns:ns221="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="description" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getErrorTaskPermission" qname="ns222:getErrorTaskPermission" returnQName="getErrorTaskPermissionReturn" returnType="ns222:WsErrorTaskPermission" soapAction="" xmlns:ns222="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="taskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getErrorTaskLog" qname="ns223:getErrorTaskLog" returnQName="getErrorTaskLogReturn" returnType="ns223:ArrayOfWsErrorTaskLog" soapAction="" xmlns:ns223="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getCanForwardOrDelegateToUsers" qname="ns224:getCanForwardOrDelegateToUsers" returnQName="getCanForwardOrDelegateToUsersReturn" returnType="ns224:ArrayOfArrayOfWsUserInfo" soapAction="" xmlns:ns224="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="errorTaskId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getBae" qname="ns225:getBae" returnQName="getBaeReturn" returnType="ns225:ArrayOf_xsd_boolean" soapAction="" xmlns:ns225="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="isShellCmd" qname="ns226:isShellCmd" returnQName="isShellCmdReturn" returnType="xsd:boolean" soapAction="" xmlns:ns226="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="sessId" type="xsd:string"/>
   <parameter name="flowId" type="xsd:long"/>
   <parameter name="actName" type="xsd:string"/>
  </operation>
  <operation name="stopShellCmdProcess" qname="ns227:stopShellCmdProcess" soapAction="" xmlns:ns227="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getShellCmdOutput" qname="ns228:getShellCmdOutput" returnQName="getShellCmdOutputReturn" returnType="ns228:WSShellCmdOutput" soapAction="" xmlns:ns228="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="stdErrStartLine" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="stdOutStartLine" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="maxLines" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="countOflastLineForStdErr" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="countOflastLineForStd" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getShellCmdOutputNLine" qname="ns229:getShellCmdOutputNLine" returnQName="getShellCmdOutputNLineReturn" returnType="ns229:WSShellCmdOutput" soapAction="" xmlns:ns229="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="line" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="inputForShellCmd" qname="ns230:inputForShellCmd" soapAction="" xmlns:ns230="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="requestId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="input" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getMonitorSetting" qname="ns232:getMonitorSetting" returnQName="getMonitorSettingReturn" returnType="ns231:Map" soapAction="" xmlns:ns231="http://xml.apache.org/xml-soap" xmlns:ns232="http://webservice.v30.ieai.ideal.com"/>
  <operation name="setMonitorSetting" qname="ns233:setMonitorSetting" returnQName="setMonitorSettingReturn" returnType="xsd:string" soapAction="" xmlns:ns233="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <parameter name="setting" type="ns234:Map" xmlns:ns234="http://xml.apache.org/xml-soap"/>
  </operation>
  <operation name="saveChActInfo" qname="ns235:saveChActInfo" soapAction="" xmlns:ns235="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessionId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="flowId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="actId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="isIgnore" type="xsd:int" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="dealyTime" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="getUserInfo" qname="ns236:getUserInfo" returnQName="getUserInfoReturn" returnType="ns236:WsUserBasicInfo" soapAction="" xmlns:ns236="http://webservice.v30.ieai.ideal.com">
   <parameter name="SessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="userId" type="xsd:long" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="connect" qname="ns237:connect" soapAction="" xmlns:ns237="http://webservice.v30.ieai.ideal.com">
   <parameter name="sessId" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <operation name="monitorApplyForWeb" qname="ns238:monitorApplyForWeb" soapAction="" xmlns:ns238="http://webservice.v30.ieai.ideal.com">
   <parameter name="mp" type="ns239:MonitorParm" xmlns:ns239="http://heartbeatmonitor.repository.jobscheduling.server.ieai.ideal.com"/>
  </operation>
  <operation name="dbConnectTimeApplyForWeb" qname="ns240:dbConnectTimeApplyForWeb" soapAction="" xmlns:ns240="http://webservice.v30.ieai.ideal.com">
   <parameter name="numRetries" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
   <parameter name="waitTimes" type="xsd:string" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  </operation>
  <parameter name="allowedMethods" value="getErrorTaskPermission confirmUserNotice queryFlow setMonitorSetting getBae getAvailableNodes cancleTaskDelegation autoLogout getProjectLogHistory isTaskManager getRolesFromUser queryTaskHistory getActValidTimeCfg queryActUseTimeStatistic queryActRunStatistic completeTask startFlow getSubFlowInfo removeRole isAdaptorExist addSNToDomain confirmTaskNotice checkOwnPermission isDBNormal setUAPwd getPartInfo skipTask forceReleaseErrorTask getLoginNotices hasTaskAlert downloadAdaptor releaseTask getFlowTaskNames forceLogin getCanTakeOperationsOfErrorTask invalidateNotice setRolePermissionSet getOnlyTaskOwnerWithoutFinishedSkipped operateOnErrorTask updateFlowProperties getActNodeInfo acquireTask forceReleaseTaskOwner saveChActInfo approveDelegatedTask updateAccountInfo cancelErrorTaskDelegation releaseErrorTask isShellCmd getAllFlowsInfo forceAcquireErrorTask killFlow rejectDelegatedTask getAttachment getUserPasswordFormat getAllRole getRole removeUser existProject getUAAdaptorList getErrorTask queryTask performActOnTask removeUsers getUser acquireErrorTask getNotice getDomainInfo getTasksOfJob unfreezeAdaptor getShellCmdOutput deleteAdaptor removeSNFromDomain forwardTask getTaskNotices getBasicActInfo updateTaskProperties modifyNotifyMethod logout getFlowLogConfig freezeServiceNode getFlowInfosByPendingTasks inputForShellCmd getAllActInfo getUAProjectList getRelatedProject checkUserPassword getTasksToAlert isSA delegateErrorTask shutdownSN getSysConfig getServiceNode isNodeStarted delegateTask getFlowSnapshot finishTaskItem getAllNodes setFlowLogConfig setUserPermissionSet getShellCmdOutputNLine removeRoles getMonitorSetting getUserCalendarInfo getTaskDeadline addTaskAttachment getUserPermissionSet queryActInfo getRolePermissionSet delWarningInfo setTaskOwner updateAdaptor getFlowDefinition freezeAdaptor login checkUserPermission getActivityRelatedRuntimes updateUserRelatedInfo downloadProject rejectDelegateeOperationOfErrorTask getFlowInfos setPropertyValue setUserRoles getAllUser getIEAIGroupMembers getFlowParameters isUA updateRole acceptDelegatieeOperationOfErrorTask unfreezeProject createUser getFullActInfo unfreezeServiceNode freezeProject getSelfInfo kickoutSession updateUser getAdaptorLogHistory getAllMonitorActCfg getTaskId connect monitorApplyForWeb isSessionValid deleteNotice getTimeNotices queryRunningActForUTIndex resumeFlow updateProject announceNotice deleteProject getAttachments isUserInRole forwardErrorTask setRoleUsers getCurrServiceNode getTaskDetail getErrorTaskLog setSAPwd stopFlow createRole queryNotice queryWarningInfo getUsersFromRole getActType getRelatedAdaptor deleteTaskAttachment getCurRunningAct isProjectExist pauseFlow getTaskForwardCandidates getUserInfo getFlowInfo getAllClientSessions stopShellCmdProcess getAdaptorVersionHistory getPendingTasks getNoticePermission getTaskDelegateCandidates getCanForwardOrDelegateToUsers setUserSelfInfo getProjectVersionHistory setTaskAttachment queryFlowDefination getCanStartProjects dbConnectTimeApplyForWeb"/>
  <parameter name="wsdlPortType" value="IEAIService"/>
  <parameter name="typeMappingVersion" value="1.1"/>
  <parameter name="wsdlServicePort" value="IEAIService"/>
  <parameter name="className" value="com.ideal.ieai.server.webservice.IEAIService"/>
  <parameter name="wsdlTargetNamespace" value="http://webservice.v30.ieai.ideal.com"/>
  <parameter name="wsdlServiceElement" value="IEAIServiceService"/>
  <parameter name="schemaUnqualified" value="http://xml.apache.org/xml-soap,http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns241:WsShortTime" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsShortTime" xmlns:ns241="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns242:WsErrorTaskLog" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsErrorTaskLog" xmlns:ns242="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns243:WsActivityRelatedRuntimes" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsActivityRelatedRuntimes" xmlns:ns243="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns244:WorkflwLogConfig" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WorkflwLogConfig" xmlns:ns244="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns245:WSShellCmdOutput" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSShellCmdOutput" xmlns:ns245="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns246:TaskHistory" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.TaskHistory" xmlns:ns246="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns247:PrjAdpPermission" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.PrjAdpPermission" xmlns:ns247="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns248:MonitorActConfig" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.MonitorActConfig" xmlns:ns248="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns249:ActivityRuntimeInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.ActivityRuntimeInfo" xmlns:ns249="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns250:JDBCCfg" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.JDBCCfg" xmlns:ns250="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns251:WSActValidTimeCfg" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActValidTimeCfg" xmlns:ns251="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns252:WSActRunInfoStatReportCriteria" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActRunInfoStatReportCriteria" xmlns:ns252="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns253:TaskQueryCriteria" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.TaskQueryCriteria" xmlns:ns253="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns254:ProjectHistoryInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.ProjectHistoryInfo" xmlns:ns254="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns255:WsClientSession" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsClientSession" xmlns:ns255="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns256:UserPasswordFormat" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.UserPasswordFormat" xmlns:ns256="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns257:SpecialWorkDayInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.SpecialWorkDayInfo" xmlns:ns257="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns258:WSTimeoutRuleState" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSTimeoutRuleState" xmlns:ns258="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns259:TaskQueryResult" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.TaskQueryResult" xmlns:ns259="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns260:ServiceNode" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.ServiceNode" xmlns:ns260="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns261:AdaptorInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.AdaptorInfo" xmlns:ns261="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns262:Address" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.Address" xmlns:ns262="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns263:WsDelegatingDetail" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsDelegatingDetail" xmlns:ns263="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns264:WsRunningActQueryResult" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsRunningActQueryResult" xmlns:ns264="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns265:WsFullActInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsFullActInfo" xmlns:ns265="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns266:WsFlowInstanceFilter" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsFlowInstanceFilter" xmlns:ns266="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns267:WsErrorTaskOperation" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsErrorTaskOperation" xmlns:ns267="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns268:ActNodeInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.ActNodeInfo" xmlns:ns268="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns269:WSWorkflowDef" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSWorkflowDef" xmlns:ns269="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns270:WSFlowDefReportCriteria" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSFlowDefReportCriteria" xmlns:ns270="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns271:AdaptorHistoryInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.AdaptorHistoryInfo" xmlns:ns271="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns272:WsNotice" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsNotice" xmlns:ns272="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns273:WsNoticeQueryResult" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsNoticeQueryResult" xmlns:ns273="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns274:WorkflowInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WorkflowInfo" xmlns:ns274="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns275:TaskAttachment" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.TaskAttachment" xmlns:ns275="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns276:ProjectInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.ProjectInfo" xmlns:ns276="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns277:WorkflowFilter" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WorkflowFilter" xmlns:ns277="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns278:BasicActInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.BasicActInfo" xmlns:ns278="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns279:TaskInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.TaskInfo" xmlns:ns279="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns280:WsOperationConfiguration" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsOperationConfiguration" xmlns:ns280="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns281:WSActRunInfoReportCriteria" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActRunInfoReportCriteria" xmlns:ns281="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns282:WarningInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WarningInfo" xmlns:ns282="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns283:WsTaskItem" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsTaskItem" xmlns:ns283="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns284:WSActRunUseTimeReport" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActRunUseTimeReport" xmlns:ns284="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns285:WsUserInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsUserInfo" xmlns:ns285="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns286:WSActRunInfoStatReport" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActRunInfoStatReport" xmlns:ns286="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns287:WSActRunInfoReport" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActRunInfoReport" xmlns:ns287="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns288:WsErrorTask" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsErrorTask" xmlns:ns288="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns289:TaskOperation" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.TaskOperation" xmlns:ns289="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns290:WsRoleInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsRoleInfo" xmlns:ns290="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns291:WeeklyDayInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WeeklyDayInfo" xmlns:ns291="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns292:DomainInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.DomainInfo" xmlns:ns292="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns293:Load" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.Load" xmlns:ns293="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns294:WSParamEnvValue" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSParamEnvValue" xmlns:ns294="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns295:WorkingTimeInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WorkingTimeInfo" xmlns:ns295="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns296:WsActivityEntry" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsActivityEntry" xmlns:ns296="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns297:WSProjectDef" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSProjectDef" xmlns:ns297="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns298:WsQueryNoticeCriteria" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsQueryNoticeCriteria" xmlns:ns298="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns299:HolidayInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.HolidayInfo" xmlns:ns299="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns300:WSSysConfig" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSSysConfig" xmlns:ns300="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns301:WSActRunUseTimeReportCriteria" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSActRunUseTimeReportCriteria" xmlns:ns301="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns302:WsAppLogFilter" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsAppLogFilter" xmlns:ns302="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns303:CalendarInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.CalendarInfo" xmlns:ns303="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns304:WsNoticeOperationRecord" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsNoticeOperationRecord" xmlns:ns304="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns305:PartInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.PartInfo" xmlns:ns305="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns306:WsErrorTaskPermission" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsErrorTaskPermission" xmlns:ns306="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns307:WsNoticeAnnouncePermission" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsNoticeAnnouncePermission" xmlns:ns307="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns308:SubFlowInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.SubFlowInfo" xmlns:ns308="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns309:WSFlowDefReport" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSFlowDefReport" xmlns:ns309="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns310:DelegationRecord" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.DelegationRecord" xmlns:ns310="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns311:WSTaskProperty" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WSTaskProperty" xmlns:ns311="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns312:WsUserBasicInfo" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.WsUserBasicInfo" xmlns:ns312="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns313:PermissionSet" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.webservice.PermissionSet" xmlns:ns313="http://webservice.v30.ieai.ideal.com"/>
  <typeMapping deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory" encodingStyle="http://schemas.xmlsoap.org/soap/encoding/" qname="ns314:MonitorParm" serializer="org.apache.axis.encoding.ser.BeanSerializerFactory" type="java:com.ideal.ieai.server.jobscheduling.repository.heartbeatmonitor.MonitorParm" xmlns:ns314="http://heartbeatMonitor.repository.jobscheduling.server.ieai.ideal.com"/>
  <arrayMapping innerType="ns315:WsUserInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WsUserInfo[][]" qname="ns315:ArrayOfArrayOfWsUserInfo" xmlns:ns315="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns316:ProjectInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.ProjectInfo[]" qname="ns316:ArrayOfProjectInfo" xmlns:ns316="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="xsd:long" languageSpecificType="java:long[]" qname="ns317:ArrayOf_xsd_long" xmlns:ns317="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <arrayMapping innerType="ns318:WSActRunInfoStatReport" languageSpecificType="java:com.ideal.ieai.server.webservice.WSActRunInfoStatReport[]" qname="ns318:ArrayOfWSActRunInfoStatReport" xmlns:ns318="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns319:WSActRunInfoReport" languageSpecificType="java:com.ideal.ieai.server.webservice.WSActRunInfoReport[]" qname="ns319:ArrayOfWSActRunInfoReport" xmlns:ns319="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns320:WSParamEnvValue" languageSpecificType="java:com.ideal.ieai.server.webservice.WSParamEnvValue[]" qname="ns320:ArrayOfWSParamEnvValue" xmlns:ns320="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns321:WSTimeoutRuleState" languageSpecificType="java:com.ideal.ieai.server.webservice.WSTimeoutRuleState[]" qname="ns321:ArrayOfWSTimeoutRuleState" xmlns:ns321="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="xsd:int" languageSpecificType="java:int[]" qname="ns322:ArrayOf_xsd_int" xmlns:ns322="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <arrayMapping innerType="ns323:ServiceNode" languageSpecificType="java:com.ideal.ieai.server.webservice.ServiceNode[]" qname="ns323:ArrayOfServiceNode" xmlns:ns323="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns324:WsTaskItem" languageSpecificType="java:com.ideal.ieai.server.webservice.WsTaskItem[]" qname="ns324:ArrayOfWsTaskItem" xmlns:ns324="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns325:SpecialWorkDayInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.SpecialWorkDayInfo[]" qname="ns325:ArrayOfSpecialWorkDayInfo" xmlns:ns325="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns326:Address" languageSpecificType="java:com.ideal.ieai.server.webservice.Address[]" qname="ns326:ArrayOfAddress" xmlns:ns326="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns327:WeeklyDayInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WeeklyDayInfo[]" qname="ns327:ArrayOfWeeklyDayInfo" xmlns:ns327="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns328:WorkflowInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WorkflowInfo[]" qname="ns328:ArrayOfWorkflowInfo" xmlns:ns328="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns329:ActNodeInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.ActNodeInfo[]" qname="ns329:ArrayOfActNodeInfo" xmlns:ns329="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns330:SubFlowInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.SubFlowInfo[]" qname="ns330:ArrayOfSubFlowInfo" xmlns:ns330="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns331:ActivityRuntimeInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.ActivityRuntimeInfo[]" qname="ns331:ArrayOfActivityRuntimeInfo" xmlns:ns331="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns332:AdaptorInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.AdaptorInfo[]" qname="ns332:ArrayOfAdaptorInfo" xmlns:ns332="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns333:WSFlowDefReport" languageSpecificType="java:com.ideal.ieai.server.webservice.WSFlowDefReport[]" qname="ns333:ArrayOfWSFlowDefReport" xmlns:ns333="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns334:WsUserInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WsUserInfo[]" qname="ns334:ArrayOfWsUserInfo" xmlns:ns334="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns335:WarningInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WarningInfo[]" qname="ns335:ArrayOfWarningInfo" xmlns:ns335="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="xsd:boolean" languageSpecificType="java:boolean[]" qname="ns336:ArrayOf_xsd_boolean" xmlns:ns336="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <arrayMapping innerType="ns337:WsClientSession" languageSpecificType="java:com.ideal.ieai.server.webservice.WsClientSession[]" qname="ns337:ArrayOfWsClientSession" xmlns:ns337="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns338:WSWorkflowDef" languageSpecificType="java:com.ideal.ieai.server.webservice.WSWorkflowDef[]" qname="ns338:ArrayOfWSWorkflowDef" xmlns:ns338="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns339:AdaptorHistoryInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.AdaptorHistoryInfo[]" qname="ns339:ArrayOfAdaptorHistoryInfo" xmlns:ns339="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns340:MonitorActConfig" languageSpecificType="java:com.ideal.ieai.server.webservice.MonitorActConfig[]" qname="ns340:ArrayOfMonitorActConfig" xmlns:ns340="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns341:TaskAttachment" languageSpecificType="java:com.ideal.ieai.server.webservice.TaskAttachment[]" qname="ns341:ArrayOfTaskAttachment" xmlns:ns341="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns342:WorkingTimeInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WorkingTimeInfo[]" qname="ns342:ArrayOfWorkingTimeInfo" xmlns:ns342="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns343:WsUserBasicInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WsUserBasicInfo[]" qname="ns343:ArrayOfWsUserBasicInfo" xmlns:ns343="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="xsd:string" languageSpecificType="java:java.lang.String[]" qname="ns344:ArrayOf_xsd_string" xmlns:ns344="http://webservice.v30.ieai.ideal.com" xmlns:xsd="http://www.w3.org/2001/XMLSchema"/>
  <arrayMapping innerType="ns345:WsNoticeQueryResult" languageSpecificType="java:com.ideal.ieai.server.webservice.WsNoticeQueryResult[]" qname="ns345:ArrayOfWsNoticeQueryResult" xmlns:ns345="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns346:WSActValidTimeCfg" languageSpecificType="java:com.ideal.ieai.server.webservice.WSActValidTimeCfg[]" qname="ns346:ArrayOfWSActValidTimeCfg" xmlns:ns346="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns347:PrjAdpPermission" languageSpecificType="java:com.ideal.ieai.server.webservice.PrjAdpPermission[]" qname="ns347:ArrayOfPrjAdpPermission" xmlns:ns347="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns348:WsErrorTaskLog" languageSpecificType="java:com.ideal.ieai.server.webservice.WsErrorTaskLog[]" qname="ns348:ArrayOfWsErrorTaskLog" xmlns:ns348="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns349:WSActRunUseTimeReport" languageSpecificType="java:com.ideal.ieai.server.webservice.WSActRunUseTimeReport[]" qname="ns349:ArrayOfWSActRunUseTimeReport" xmlns:ns349="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns350:WsRoleInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.WsRoleInfo[]" qname="ns350:ArrayOfWsRoleInfo" xmlns:ns350="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns351:HolidayInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.HolidayInfo[]" qname="ns351:ArrayOfHolidayInfo" xmlns:ns351="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns352:ProjectHistoryInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.ProjectHistoryInfo[]" qname="ns352:ArrayOfProjectHistoryInfo" xmlns:ns352="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns353:WsActivityEntry" languageSpecificType="java:com.ideal.ieai.server.webservice.WsActivityEntry[]" qname="ns353:ArrayOfWsActivityEntry" xmlns:ns353="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns354:WSTaskProperty" languageSpecificType="java:com.ideal.ieai.server.webservice.WSTaskProperty[]" qname="ns354:ArrayOfWSTaskProperty" xmlns:ns354="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns355:TaskInfo" languageSpecificType="java:com.ideal.ieai.server.webservice.TaskInfo[]" qname="ns355:ArrayOfTaskInfo" xmlns:ns355="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns356:TaskOperation" languageSpecificType="java:com.ideal.ieai.server.webservice.TaskOperation[]" qname="ns356:ArrayOfTaskOperation" xmlns:ns356="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns357:WsRunningActQueryResult" languageSpecificType="java:com.ideal.ieai.server.webservice.WsRunningActQueryResult[]" qname="ns357:ArrayOfWsRunningActQueryResult" xmlns:ns357="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns358:WsNoticeOperationRecord" languageSpecificType="java:com.ideal.ieai.server.webservice.WsNoticeOperationRecord[]" qname="ns358:ArrayOfWsNoticeOperationRecord" xmlns:ns358="http://webservice.v30.ieai.ideal.com"/>
  <arrayMapping innerType="ns359:TaskHistory" languageSpecificType="java:com.ideal.ieai.server.webservice.TaskHistory[]" qname="ns359:ArrayOfTaskHistory" xmlns:ns359="http://webservice.v30.ieai.ideal.com"/>
 </service>
 <transport name="http">
  <requestFlow>
   <handler type="URLMapper"/>
   <handler type="java:org.apache.axis.handlers.http.HTTPAuthHandler"/>
  </requestFlow>
  <parameter name="qs:list" value="org.apache.axis.transport.http.QSListHandler"/>
  <parameter name="qs:wsdl" value="org.apache.axis.transport.http.QSWSDLHandler"/>
  <parameter name="qs.list" value="org.apache.axis.transport.http.QSListHandler"/>
  <parameter name="qs.method" value="org.apache.axis.transport.http.QSMethodHandler"/>
  <parameter name="qs:method" value="org.apache.axis.transport.http.QSMethodHandler"/>
  <parameter name="qs.wsdl" value="org.apache.axis.transport.http.QSWSDLHandler"/>
 </transport>
 <transport name="local">
  <responseFlow>
   <handler type="LocalResponder"/>
  </responseFlow>
 </transport>
</deployment>
