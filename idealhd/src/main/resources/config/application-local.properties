
spring.application.name=entegor

server.port=8888
####springboot\u4E0Bhttps\u542F\u52A8\u65B9\u5F0F
server.ssl.enabled=false
server.ssl.key-store-type=JKS
server.ssl.key-store=classpath:itomcat.keystore
server.ssl.key-store-password=idealinfo
####springboot\u4E0Bhttps\u542F\u52A8\u65B9\u5F0F
server.servlet.context-path=/
#a

spring.mvc.view.prefix=/page/

spring.mvc.view.suffix=.jsp

#server.servlet.init-parameters.development=true
server.servlet.jsp.init-parameters.development=true
spring.thymeleaf.enabled=false


spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
spring.server.entegor.allow.urls=/acceptEmPortState.do,/productmanage/queryStoreListPageTest.do,/productmanage/pushGenericFileServer.do,/productmanage/sendNotifyforCD.do,/productmanage/updateIseorderByid.do,/productmanage/pullGenericFile.do,/productmanage/pushGenericFile.do,/V1/updateCHGState.do,/V1/updateSequentState.do,/V1/updateArtifactsStatus.do,/V1/updateConfigStatusByItemId.do,/productmanage/initProductKg.do,/accessCI_CONFIGFILEKg.do,/sus/api/execFJRCStart.do,/sus/api/flowTaskApproved.do,/sus/api/queryRiseTask.do,/productmanage/pullFile.do,/productmanage/uploadFile.do,/approveNotify.do,/riseChange.do,/riseImport.do,/riseExport.do,/sus/api/saveSequential.do,/sus/api/saveProjSys.do,/updateRecoverActStatus.do,/performShutdownPlan.do,/opm/agentapi/startLogCenterDataCollect.do,/dmoperation/stardard/evertoken.do,/cmdb/V1/queryCMDBTypeList.do,/cmdb/V1/queryCmdbTypeAttributeList.do,/cmdb/V1/getCmdbListByTypeRelation.do,/cmdb/V1/queryCmdbInfoListPage.do,/getswitchruninsInterface.do,/updateOldCodeData.do,/opm/agentproxyapi/getAgentProxyRelation.do,/opm/agentproxyapi/getProxyInfo.do,/opm/agentapi/agentproxyapi/getProxyInfo.do,/opm/agentapi/agentproxyapi/getAgentInfo.do,/opm/agentproxyapi/getAgentInfo.do,/scriptUploadFiles.do,/switch/getSwitchFlow.do,/switch/startSwitchFlow.do,/switch/getSwitchResult.do,/sxTokenLogin.do,/workOrderQuantities.do,/topFiveWorkOrderQuantities.do,/getAutomationUsageRate.do,/queryDeploymentTime.do,/queryDeploymentTimeForEcharts.do,/leftPanelQuery.do,/queryCICD_DeployReport.do,/getCibSwitchInfoListForMobile.do,/captchaImage.do,/oneClickOfflineInterface.do,/uninstallInterface.do,/wfyh/*,/execShell/query.do,/execShell/exec.do,/execShell/token.do,/scriptdownload.do,/login2.do,/updataMethodRunTimeLogStatus.do,/startselfHealing.do,/getSelfhealing.do,/addAlarm.do,/login.do,/changePasswordPage.do,/updatePassword.do,/scriptplatform.do,/dmoperation/V1/token.do,/dmoperation/stardard/getAgentInstallResult.do,/dmoperation/stardard/queryDayAndEXecutionCount.do,/dmoperation/stardard/queryDayAndEXecutionDetail.do,/dmoperation/stardard/queryDayAndEXecutionPrj.do,/emerplan/V1/token.do,/emerplan/V1/emerPlanInfo.do,/dmtimetask/V1/token.do,/dmtimetask/V1/getToken.do,/getHcToken.do,/sendNumber.do,/about/equipments/equipment.do,/CollectResult/cpid.do,/infoCollection/token.do,/getScriptToken.do,/saveCheckResult.do,/getComputerStatus.do,/integratePushApi.do,/agencytasksendexcel.do,/saveordeluser.do,/initButtonAndMenu.do,/cibinitscreen.do,/restfullDbaasGetDbpwd.do,/speakerAlarm.do,/hcCheckUser.do,/toHcLogin.do,/susfinishAndOut.do,/azfinishAndOut.do,/getProcData.do,/saveCibAgentInfo.do,/api_gfcheckhc/V1/getToken.do,/api_gfcheckhc/V1/token.do,/updateIthreshold.do,/updateCibItsmMessage.do,/api_suppercheckhc/V1/token.do,/pubUmsRecordJson.do,/pubAomsRecordJson.do,/pubAomsOpmJson.do,/getEmToken.do,/getEmPlanNames.do,/emStartPlan.do,/getEmMonitorInfo.do,/itilopration/token.do,/itilopration/itilApproved.do,/deleteEntegorConfigFromOther.do,/reloadConfigFromOther.do,/opm/agentapi/getAgentInfo.do,/opm/agentapi/bindAgentSystem.do,/opm/agentapi/agentOffLine.do,/alarmInfo/getRemoteAlarmInfoData.do,/ciblogin.do,/rest/openapi/notification/common/alarm.do,/getTcAlarmToken.do,/verificationalarm.do,/api/users.do,/token/getToken.do,/token/verify.do,/getOnlineUsersData.do,/kikoutUsersBuSession.do,/monitorDirPage.do,/order/createOrder.do,/order/getOrderStatus.do,/order/createOrderNew.do,/order/getOrderStatus.do,/dmoperation/stardard/queryDayAndExecStepTimeOutCount.do,/dmoperation/stardard/queryDayAndExecStepTimeOutDetail.do,/dmoperation/stardard/queryDayAndExecStepTimeOutPrj.doz,/sysChange/saveSysChangeSyncIps.do,/sus/itsm/ultrapp.do,/artifacts/queryProjectListPage.do,/artifacts/queryStoreListPage.do,/artifacts/pushGenericFileAPI.do,/artifacts/platform.do,/sus/OpenApi/saveNJExecSql.do,/dmoperation/stardard/applicationStandardTaskNew.do,/itsmCCMonitor.do,/dmoperation/stardard/standardOperSysName.do,/sus/OpenApi/updateNJExecSqlStart.do,*.do,/getAgentForStateList.do,/getAgentStatusList.do,/performShutdownPlan.do,/dmoperation/V1/getBhToken.do,/dmoperation/stardard/applicationStandardTaskForBh.do,/klOauth/UserService,/smsApprovalReply.do,/drcbUnifiedInterface.do,/positionBegin.do,/lookMonitorCreatDataTotalByMobile,/getJobByPrjId_jobDetailShowByMobile,/lookMonitorCreatDataByMobile

#web.static-path=file:///C:/workspace_brance/entegor_v8_BLD_133763/idealhd/hd

#spring.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,\
#  classpath:/static/,classpath:/public/,${web.static-path}


server.tongweb.license.type = file
server.tongweb.license.path=classpath:/license.dat

#server.servlet.context-path:/aoms
#
server.tongweb.additional-tld-skip-patterns=server.bes.additional-tld-skip-patterns=serializer.jar,orai18n-*.jar,gdk_custom.jar,idb.jar,xbean.jar,classes12.jar,p6psy.jar,json-lib-2.4.jar,ezmorph-1.0.6.jar,\
commons-cli.jar,jts1_0.jar,carol.jar,jonas_timer.jar,com.ibm.*.jar,connector.jar,jms.jar,dhbcore.jar,rmm.jar,jndi.jar,ldap.jar,fscontext.jar,regexp-1.3.jar,\
  maven-scm-*jar,plexus-utils-1.5.6.jar,commons-vfs2-2.0.jar,jersey-*.jar,swagger-*.jar,dubbo-*.jar,log4j.jar,oraclepki.jar,xml-apis.jar,xercesImpl.jar


server.bes.additional-tld-skip-patterns=serializer.jar,orai18n-*.jar,gdk_custom.jar,idb.jar,xbean.jar,classes12.jar,p6psy.jar,json-lib-2.4.jar,ezmorph-1.0.6.jar,\
commons-cli.jar,jts1_0.jar,carol.jar,jonas_timer.jar,com.ibm.*.jar,connector.jar,jms.jar,dhbcore.jar,rmm.jar,jndi.jar,ldap.jar,fscontext.jar,regexp-1.3.jar,\
  maven-scm-*jar,plexus-utils-1.5.6.jar,commons-vfs2-2.0.jar,jersey-*.jar,swagger-*.jar,dubbo-*.jar,log4j.jar,oraclepki.jar,xml-apis.jar,xercesImpl.jar

server.tomcat.additional-tld-skip-patterns=serializer.jar,orai18n-*.jar,gdk_custom.jar,idb.jar,xbean.jar,classes12.jar,p6psy.jar,json-lib-2.4.jar,ezmorph-1.0.6.jar,\
commons-cli.jar,jts1_0.jar,carol.jar,jonas_timer.jar,com.ibm.*.jar,connector.jar,jms.jar,dhbcore.jar,rmm.jar,jndi.jar,ldap.jar,fscontext.jar,regexp-1.3.jar,\
  maven-scm-*jar,plexus-utils-1.5.6.jar,commons-vfs2-2.0.jar,jersey-*.jar,swagger-*.jar,dubbo-*.jar,log4j.jar,oraclepki.jar,xml-apis.jar,xercesImpl.jar