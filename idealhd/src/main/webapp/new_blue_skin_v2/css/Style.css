/*IE Google Slider*/
body{
	}
/*.lbIconsMenu{
	width:60px !important;
	background-position:15px !important;
	background-size:100% !important;
}*/
.normalRadio .x-form-checkboxgroup-body{
	border:1px solid #ccc;
	display:flex;
	justify-content: right;
	position: relative;
	top: 17px;
	left:75px;
	width:90.2% !important
}
.showRadio .x-form-checkboxgroup-body{
	border:1px solid #ccc;
	display:flex;
	justify-content: right;
	position: relative;
	top: 17px;
	left:75px;
	width:80% !important
}
.green_btn{
	background-color:#70ad47;
	background-image: -webkit-linear-gradient(top, #70ad47 0%, #70ad47 100%);
  	background-image: -moz-linear-gradient(top, #70ad47 0%, #70ad47 100%);
  	background-image: -o-linear-gradient(top, #70ad47 0%, #70ad47 100%);
  	background-image: linear-gradient(to bottom, #70ad47 0%, #70ad47 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #70ad47;
	margin:5px 7px;
	cursor:pointer
	}
.green_btn:hover{
	background-color:#8dbd6c;
	background-image: -webkit-linear-gradient(top, #8dbd6c 0%, #8dbd6c 100%);
  	background-image: -moz-linear-gradient(top, #8dbd6c 0%, #8dbd6c 100%);
  	background-image: -o-linear-gradient(top, #8dbd6c 0%, #8dbd6c 100%);
  	background-image: linear-gradient(to bottom, #8dbd6c 0%, #8dbd6c 100%);
  	border:1px solid #8dbd6c;
  	color:#fff;
	}
.green_btn:active{
	background-color:#a9ce91;
	background-image: -webkit-linear-gradient(top, #a9ce91 0%, #a9ce91 100%);
  	background-image: -moz-linear-gradient(top, #a9ce91 0%, #a9ce91 100%);
  	background-image: -o-linear-gradient(top, #a9ce91 0%, #a9ce91 100%);
  	background-image: linear-gradient(to bottom, #a9ce91 0%, #a9ce91 100%);
  	border:1px solid #a9ce91;
  	color:#ffffff;
	}
.red_btn{
	background-color:#ff0000;
	background-image: -webkit-linear-gradient(top, #ff0000 0%, #ff0000 100%);
  	background-image: -moz-linear-gradient(top, #ff0000 0%, #ff0000 100%);
  	background-image: -o-linear-gradient(top, #ff0000 0%, #ff0000 100%);
  	background-image: linear-gradient(to bottom, #ff0000 0%, #ff0000 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #ff0000;
	margin:5px 7px;
	cursor:pointer
	}
.red_btn:hover{
	background-color:#ff3333;
	background-image: -webkit-linear-gradient(top, #ff3333 0%, #ff3333 100%);
  	background-image: -moz-linear-gradient(top, #ff3333 0%, #ff3333 100%);
  	background-image: -o-linear-gradient(top, #ff3333 0%, #ff3333 100%);
  	background-image: linear-gradient(to bottom, #ff3333 0%, #ff3333 100%);
  	border:1px solid #ff3333;
  	color:#fff;
	}
.red_btn:active{
	background-color:#ff6666;
	background-image: -webkit-linear-gradient(top, #ff6666 0%, #ff6666 100%);
  	background-image: -moz-linear-gradient(top, #ff6666 0%, #ff6666 100%);
  	background-image: -o-linear-gradient(top, #ff6666 0%, #ff6666 100%);
  	background-image: linear-gradient(to bottom, #ff6666 0%, #ff6666 100%);
  	border:1px solid #ff6666;
  	color:#ffffff;
	}
	
	
.grid_panel_body{
	background-color:#ffffff;
	height: calc(100% - 50px);
	width:100%;
	margin-top:50px;
}
.grid_panel_box{
	height: calc(100% - 50px);
	display: flex;
	align-items: center;
	justify-content: center;
	padding-right:0px;
	padding-left:0px;
	
}
.grid_panel_header{
	width:100%;
	height:50px;
	background-color:#f5f7fa;
	display:flex;
	justify-content:center;
	align-items:center;
}
.grid_panel_header h1{
	font-size: 24px;
	font-weight:normal;
}
.grid_cn{
	max-width: 1401px;
	font-size: 0px;
}
.grid_panel{
	width: 176px;
	height: 176px;
	border:1px solid #f6f6f7;
	padding: 0px;
	display:inline-flex;
	justify-content: center;
	align-items: center;
	flex-direction:column;
	cursor: pointer;
	margin-left: -1px;
	margin-top: -1px;
}
.grid_icon_bg{
	width: 80px;
	height: 80px;
	background-size: 100%;
	border-radius: 100%;
	box-shadow: #bbdcfc 0 15px 15px -5px;
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #3495f7 0%, #218bf6 100%);
  	background-image: -moz-linear-gradient(top, #3495f7 0%, #218bf6 100%);
  	background-image: -o-linear-gradient(top, #3495f7 0%, #218bf6 100%);
  	background-image: linear-gradient(to bottom, #3495f7 0%, #218bf6 100%);
	display: flex;
	justify-content: center;
	align-items: center;
}
.grid_icon{
	width: 36px;
	height: 36px;
	background-size: 300% 100% !important;
	background-position:0px 0px !important;
}
.grid_panel h1{
	color: #46545d;
	font-size: 16px;
	font-weight:normal;
}

.grid_panel:hover{
	background-color: #007af5;
}
.grid_panel:hover .grid_icon_bg{
	background-color:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
	box-shadow: #bbdcfc 0 0px 0px 0px;
}
.grid_panel:hover h1{
	color: #ffffff;
}
.grid_panel:hover .grid_icon_bg .grid_icon{
	background-position:-180px 0px !important;
}
@media all and (min-width:1280px) and (max-width:1366px){
	.grid_panel_header h1{
		font-size: 18px;
	}
	.grid_panel_box{
		height: calc(100% - 30px);
	}
	.grid_panel_header{
		height:30px;
	}
	.grid_cn{
		max-width: 1049px;
	}
	.grid_panel{
		width: 132px;
		height: 132px;
	}
	.grid_icon_bg{
		width: 58px;
		height: 58px;
	}
	.grid_panel h1{
		font-size: 14px;
	}
	.grid_icon{
		width:30px;
		height:30px;
	}
	.grid_panel:hover .grid_icon_bg .grid_icon{
		background-position:-240px 0px !important;
	}
}
@media all and (min-width:1400px) and (max-width:1600px){
	.grid_panel{
		width: 152px;
		height: 152px;
	}
	.grid_icon_bg{
		width: 64px;
		height: 64px;
	}
	.grid_cn{
		max-width: 1209px;
	}
	.grid_icon{
		width:30px;
		height:30px;
	}
	.grid_panel:hover .grid_icon_bg .grid_icon{
		background-position:-240px 0px !important;
	}
}

.lbaaaaq{
	border-radius:20px;
	width:160px;
	height:30px;
	background:#f5f6f8;
	border:none;
	color:#bbbbbb;
	padding-left:15px;
	outline: none;
}
.buttonLeftDiv{
	position: relative;
    width:0px;
    height:54px;
    padding-left:0px;
    padding-right:0px;
}
.buttonLeftDiv:before{
	content: "";
    position: absolute;
    left:18px;
    top: 17px;
    width:1px;
    height: 20px;
    background-color:#d9d9d9;
}
.buttonRightDiv{
	position: relative;
    width:0px;
    height:54px;
    padding-left:0px;
    padding-right:0px;
}
.buttonRightDiv:before{
	content: "";
    position: absolute;
    left:0px;
    top: 17px;
    width:1px;
    height: 20px;
    background-color:#d9d9d9;
}
.left_arrow{
	width: 1em; 
	height: 1em;
	vertical-align: middle;
	fill: currentColor;
	overflow: hidden;
	margin:20px 0 0 1px;
	font-size: 14px;
	color:#99a6bb;
}
.right_arrow{
	width: 1em; 
	height: 1em;
	vertical-align: middle;
	fill: currentColor;
	overflow: hidden;    
	margin:18px 0 0 1px;
	font-size: 17px;
	color:#99a6bb;
	}
.hd_top{
	width:100%;
	height:50px;
	background-color:#ffffff;
	-webkit-box-shadow: #e1e3f4 0 8px 8px -8px;
 	-moz-box-shadow: #e1e3f4 0 8px 8px -8px; 
  	box-shadow: #e1e3f4 0 8px 8px -8px;
	}
.hd_top a{
	text-decoration:none;
}
.hd_top a:hover{
	text-decoration:underline;
}
.Emergency_Body_pf{ 
	width:700px;
	height:auto;
	color:#4d4d4d;
	margin:8px 0;
	float:left;
	}
.Emergency_top_pf{ 
	background-image:url(../images/Emergency_top_pf.png); 
	width:700px; 
	height:58px;
	float:left;
	}
.Emergency_top_pf span{ 
	padding:15px 0 0 15px; 
	display:block;
	font-size:16px;
	color:#46545d;
	font-weight:bold;
	}
.Emergency_top_pf span a{
	color:#4d4d4d;
	text-decoration:none;
	font-family:Microsoft Yahei , Arial ;
	font-size:14px;
	margin:3px 0 0 0;
	display:block;
	}
.Emergency_middle_pf{ 
	background-image:url(../images/Emergency_middle_pf.png); 
	width:700px; 
	float:left;
	padding:10px 0 0 0;
	}
.Emergency_middle_pf input.graph_inputselect{
	width:94%;
}
.graph_inputselect:focus{
	outline-style: none;
	border-color:#007af5
}
.graph_select:focus{
	outline-style: none;
	border-color:#007af5
}
.Emergency_bottom_pf{ 
	background-image:url(../images/Emergency_bottom_pf.png); 
	width:700px; 
	height:25px;
	float:left;
	}
.Emergency_Body2_pf{ 
	width:379px;
	height:auto;
	color:#46545d;
	margin:8px 10px;
	float:left;
	overflow:hidden;
	}	
.Emergency_top2_pf{ 
	background-image:url(../images/Emergency_top_pf02.png); 
	width:379px; 
	height:58px;
	float:left;
	font-size:16px;
	font-weight:bold;
	}
.Emergency_top2_pf span{ 
	padding:10px 0 0 15px; 
	display:block;
	font-size:13px;
	}
.Emergency_top2_pf span a{
	color:#4d4d4d;
	text-decoration:none;
	font-family:Microsoft Yahei , Arial ;
	margin:3px 0 0 0;
	display:block;
	}
.Emergency_middle2_pf{ 
	background-image:url(../images/Emergency_middle2_pf02.png); 
	width:379px;  
	float:left;
	}
.Emergency_bottom2_pf{ 
	background-image:url(../images/Emergency_bottom_pf02.png); 
	width:379px; 
	height:25px;
	float:left;
	}
.Emergency_Content_pf{
	width:700px;
	}
.Emergency_Content_pf ul li{ 
	padding:0 10px; 
	height:36px; 
	line-height:36px;
	margin:0 0 5px 0;
	}
.Change_Left_SUS.Emergency_Content_pf ul li .System_info{ 
	float:left; 
	width:637px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.hd_t_td1{
	width:124px;
	height:50px;
	}
.hd_t_td1 img{
	margin:-3px 0 0 0;
}
.unfold{
	width:124px;
	height:50px;
	padding:0 20px;
	border-right:1px solid #d9d9d9;
	}
.unfold .tp_logo{
	background-image: url(../images/tp_logo.png);
	width:124px;
	height:50px;
}
.extend{
	width:65px;
	background:#5168fc;
	height:50px;
}
.extend .tp_logo{
	background-image: url(../images/tp_logozs_extend.png);
	width:68px;
	height:68px;
}
.menu_btn{
	background-image:url(../images/menu_btn.png);
	width:63px;
	height:68px;
	float:left;
	border-right:1px solid #dddfeb;
	border-left:0px solid #dddfeb;
}
.menu_btn:hover{
	background-color:#efefef;
	cursor:pointer;
}
.menu1_btn{
	float:left;
	margin:0 23px;
}
.menu_r_line{
	width:1px;
	height:30px;
	background-color:#d9d9d9;
	float:left;
	margin:10px 0 0 0;
}
.icon_space{
	float:left;
	padding:0 15px;
}
.hd_t_td2{
	width:auto;
	height:53px;
	}
.hd_td2_1 span{
	font-family:"Microsoft Yahei";
	color:#46545d;
	font-size:18px;
	padding:0 0 0 20px;
	line-height:50px;
	}
.Environment_Color{
	font-size:14px;
	color:#ffffff;
	background-color:#ff001c;
	border-radius:30px;
	padding:2px 12px;
	font-family:'Microsoft Yahei'; 
	margin:0 0 0 15px;
	}
.tp_search_bg{
	background-image:url(../images/tp_search_bg.png);
	width:198px;
	height:23px;
	text-align:left;
	}
.tp_search_bg input{
	background-color:#5b5e6e;
	height:20px;
	width:160px;
	border:0;
	margin:0 0 0 10px;
	}
.tp_border{
	padding:0 15px;
	border-right:1px solid #d9d9d9;
	float:left;
	height:30px;
	line-height:30px;
}
.tp_border2{
	float:left;
	width:30px;
	line-height:30px;
	background-color:#f5f6f8;
	border:0px solid #f5f6f8;
	border-radius:100%;
	margin:0 5px;
}
.tp_border3{
	float:left;
	width:30px;
	line-height:30px;
	border:0px solid #f5f6f8;
	background-color:#f5f6f8;
	border-radius:100%;
	margin:0 5px;
}
.tp_user_text{
	color:#46545d;
}
.tp_user{
	color:#46545d;
	font-size:14px;
	}
.tp_border a{ 
	color:#46545d
	}
.tp_user #theClock{
	font-size:14px;
	color:#46545d
	}
.top_C_Font{
	font-size:35px;
	font-weight:bold;
	font-family:"Microsoft Yahei"; 
	margin:0 0 0 30px;
	}
.tp_userImg{
	background-image:url(../images/tp_user.png); 
	width:30px; 
	height:30px;
	margin:-2px 5px 0 0
	}
.tp_userImg:hover{
	background-image:url(../images/tp_user_hover.png);
}
.tp_border2 .tp_message{
	background-image:url(../images/message.png); 
	width:31px; 
	height:31px;
	margin-left:0px;
	}
.tp_border2:hover{
	background-color:#007af5;
	border:0px solid #007af5;
}
.tp_border2:hover .tp_message{
	background-image:url(../images/message_hover.png); 
	width:31px; 
	height:31px;
}
.tp_border3 .tp_exit{
	background-image:url(../images/tp_exit.png); 
	width:18px; 
	height:18px;
	margin-left:6px;
	margin-top:-3px;
	}
.tp_border3:hover{
	background-color:#007af5;
	border:0px solid #007af5;
}
.tp_border3:hover .tp_exit{
	background-image:url(../images/tp_exit_hover.png); 
	width:18px; 
	height:18px;
}
.tp_border4 .tp_help{
	background-image:url(../images/help.png);
	width:31px;
	height:31px;
	margin-left:0px;
}
.tp_border4:hover{
	background-color:#007af5;
	border:0px solid #007af5;
}
.tp_border4:hover .tp_help{
	background-image:url(../images/help_hover.png);
	width:31px;
	height:31px;
}
.tp_exit_text{
	margin:0 0 0 0;
}
.statebar {
	background-color:#fff;
	width: 100%;
	color: #373d48;
	padding-top:0px; 
	border-top:1px solid #d9d9d9;
	height:30px;
	line-height:30px;
}
.statebar1 {
	background-color:#fff;
	width: 100%;
	color: #373d48;
	padding-top:0px; 
	height:30px;
	line-height:30px;
}
.statebar td{
	padding-top:0px; 
}
.stateuser{
	background: url(../images/iconUser.gif) no-repeat 8px 4px;
	padding: 2px 3px 2px 32px;
	text-align: left;
	vertical-align: top;
}

.statebar_td2 span{padding:0 5px;}
.statebar_td2 span img{ padding:0 6px;}

.statebar_td2 a{text-decoration:none; color:#0081cc; padding:0 5px;}
.statebar_td2 a:hover{text-decoration:underline}

.header{
	width:100%;
	height:52px;
	position:relative;
	padding:0px;
	margin:0px;
}

.header{
	width:100%;
	height:69px;
	background:url(../images/topBg.jpg) repeat-x;
	clear:both;
}
.header .leftBg{
	width:226px;
	height:69px;
	background:url(../images/topLeftBg.jpg) no-repeat;
	float:left;
	position:relative;
}
.header .rightBg{
	width:190px;
	height:36px;
	background:url(../images/logo_eb.gif) no-repeat;
	float:right;
	position:relative;
}
.header .rightMenu{
	width:400px;
	height:20px;
	float:right;
	margin-top:40px;
	padding-top:5px;
}
.leftMenuLink{
	color:#000;
	text-decoration:none;
	font-size:12px;
	font-family:Arial;
	margin:0px 20px;
	position:absolute;
	right:0px;
	bottom:7px;

}

tr.x-grid-record-seablue .x-grid-td {
  background: rgb(77, 175, 240);
}

.empty-box {
	text-align: center;
}

.empty-box-wrapper {
	margin-top: 100px;
}

.empty-box .empty-box-wrapper .empty-box-content h3 {
	font-size: 18px;
	text-align: center;
	font-weight: 500;
	margin: 0;
	line-height: 40px;
	font-family:"Microsoft Yahei";
	color:#46545d;
	margin:20px 0 0 0;
}

.empty-box .empty-box-wrapper .empty-box-content p {
	font-size: 14px;
	color: #929ea8;
	font-family:"Microsoft Yahei";
}

/*2015-10-20 wangying*/
.Gray_Btn{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
}
.Gray_Btn:hover{
	background-color:#0067ce;
	background-image: -webkit-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -moz-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -o-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: linear-gradient(to bottom, #0067ce 0%, #0067ce 100%);
  	border:1px solid #0067ce;
  	color:#fff;
	}
.Gray_Btn:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}
.Common_Btn{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
	cursor:pointer
	}
.Common_Btn:hover{
	background-color:#0067ce;
	background-image: -webkit-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -moz-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -o-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: linear-gradient(to bottom, #0067ce 0%, #0067ce 100%);
  	border:1px solid #0067ce;
  	color:#fff;
	}
.Common_Btn:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}
.Common_Btn_small{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 10px 8px 10px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
	cursor:pointer
	}
.Common_Btn_small:hover{
	background-color:#0067ce;
	background-image: -webkit-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -moz-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -o-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: linear-gradient(to bottom, #0067ce 0%, #0067ce 100%);
  	border:1px solid #0067ce;
  	color:#fff;
	}
.Common_Btn_small:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}	
.Common_Btn2{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
	cursor:pointer
	}
.Common_Btn2:hover{
	background-color:#0067ce;
	background-image: -webkit-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -moz-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -o-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: linear-gradient(to bottom, #0067ce 0%, #0067ce 100%);
  	border:1px solid #0067ce;
  	color:#fff;
	}	
.Common_Btn2:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}
	
.Common_Btn3{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
	cursor:pointer
	}
.Common_Btn3_cease{
	background-image:url(../images/Btn_cease.png); 
	background-repeat:repeat-x; 
	width:70px; 
	height:22px;
	color:#ffffff;
	text-align:center;
	line-height:22px;
	border:0;
	cursor:pointer;
	}
.Common_Btn4{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
	cursor:pointer
	}
.Common_Btn4:hover{
	background-color:#0067ce;
	background-image: -webkit-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -moz-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -o-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: linear-gradient(to bottom, #0067ce 0%, #0067ce 100%);
  	border:1px solid #0067ce;
  	color:#fff;
	}
.Common_Btn4:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}
.Common_Btn3_stop{
	background-image:url(../images/Common_Btn3_stop.png);
	width:70px;
	height:22px;
	border:none; 
	color:#ffffff;
	background-color:transparent;
	font-family:"Microsoft Yahei";
	font-size:12px;
}

.computer_List{text-decoration:underline; color:#2681df;}

.page_Btn{
	background-color:Red;
	width:50px;
	height:50px;
}
.main_left_menu{
	background-color:#e6e6e6;
	border-bottom:1px solid #fff;
	border-right:1px solid #fff;
	color:#000000;
	font-family:"Microsoft Yahei";
	font-size:12px;
	cursor:pointer;
	height:40px;
	line-height:40px;
}

.main_left_menu .x-tree-node-text{
	line-height:20px;
}
.main_left_menu:hover{
	background-color:#c9caca;
	color:#000;
}
.left_menu_select .x-grid-td{
	background:#c00017;
	background-image: -webkit-linear-gradient(top, #d8041e 0%, #c00017 100%);
  	background-image: -moz-linear-gradient(top, #d8041e 0%, #c00017 100%);
  	background-image: -o-linear-gradient(top, #d8041e 0%, #c00017 100%);
  	background-image: linear-gradient(to bottom, #d8041e 0%, #c00017 100%);
	border:0px
	}
.left_menu_over .x-grid-td{
	background-color:#eb6663;
	color:#ffffff;
}
.left_menu_select .x-tree-node-text{color:#fff;}
.main_left_menu .x-grid-td{}
.status-label {
	width: 10px;
	height: 10px;
	float: left;
	margin-top: 4px;
	margin-right: 5px;
	background: #bfc1c0;
} 

.status-label.green {
	background: #05c464;
}

.status-label.gray {
	background: #bbbbbb;
}

.status-label.yellow {
	background: #ffbe24;
}

.status-label.blue {
	background: #409dfa;
}

.status-label.darkblue {
	background: #63e3ff;
}

.status-label.purple {
	background: #af69ff;
}

.status-label.red {
	background: #ff001c;
}

.Two-center_01 {
	width: 250px;
	height: 204px;
	border: 1px solid #dcdddd;
	background-color: #fff;
	font-size: 12px;
}

.T-Center-Part1 {
	width: 230px;
	height: 154px;
	padding: 10px;
}

.T-Center-Part1 ul {
	padding: 0;
	margin: 0;
}

.T-Center-Part1 ul li {
	list-style: none;
}
.data_center_Part{
	height: 110px;
	border: 1px solid #dcdddd;
	background-color: #fff;
	font-size: 14px;
	width:21%; float:left;
	margin:10px;
	position: relative;
}
.data_center_Part_operation{
	height: 95px;
	border: 1px solid #dcdddd;
	background-color: #fff;
	font-size: 10px;
	width:9.9%; float:left;
	margin:5px;
	position: relative;
}
.C-Part1_Bg {
	width: 62px;
	height: 62px;
	margin: auto;
}
.C-Part1_Bg div {
	background-image: url(../images/Two_C_img_gray.png);
	width: 62px;
	height: 62px;
}

.C-Part1_Bg_operation {
	width: 40px;
	height: 40px;
	margin: auto;
}
.C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_gray.png);
	width: 40px;
	height: 40px;
}

.C-Part2_Bg {
	width: 62px;
	height: 62px;
	margin: auto;
}

/* .C-part1_Cn {
	margin: 10px 0 0 0;
} */


.C-part1_Cn span {
	display:block;
	float:left;
	width:48%;
	padding:5px 0 0 0;
	color:#595757;
}

.C-part1_Cn span i{
	display: inline-block;
	*display: inline;
	zoom: 1; 
	height: 10px;
	width: 10px;
	background: #bfc1c0;
	margin:0 5px;
}

.C-part1_Cn span i.gray{
	background: #bfc1c0;
}
.C-part1_Cn span i.green{
	background: #24ab5f;
}
.C-part1_Cn span i.red{
	background: #fe6869;
}

.T-Center-Part2 {
	width: 100%;
	text-align: center;
	color: #fff;
	word-break:break-all;
	word-wrap:break-word;
	height:35px;
	vertical-align:middle;
	line-height:1.5;
	overflow:hidden;
}
.T-Center-PartA{
	height:35px;
	overflow:hidden;
}
.T-Center-PartB{
	height:35px;
	width:100%;
	display: table-cell;
	vertical-align:middle;
	padding:0 5px;
}

.T-Center-operation-Part2 {
	width: 100%;
	text-align: center;
	color: #fff;
	word-break:break-all;
	word-wrap:break-word;
	height:28px;
	vertical-align:middle;
	line-height:1.5;
	overflow:hidden;
}
.T-Center-operation-PartA{
	height:34px;
	overflow:hidden;
}
.T-Center-operation-PartB{
	height:34px;
	width:100%;
	display: table-cell;
	vertical-align:middle;
	padding:0 5px;
}


.datacenter-view .Two-center_01 {
    float: left;
    margin: 28px;
    margin-right: 0;
}
.data_center_Part.x-item-over{
    border:0px solid #efefef;
    background: #f6f7ff;
    cursor:pointer;
}

.data_center_Part.x-item-selected {
	background: #e3ebff;
	border: 1px solid #e3ebff;
}

.data_center_Part img.selected {
	position: absolute;
	top: 2px;
	right:2px;
	float:right;
	display: none;
	background-image: url(../images/selected.png);
	width:35px;
	height:35px;
}

.data_center_Part.x-item-selected img.selected{
	display: block;
}

.data_center_Part.red {
	border:2px solid #fe6869;
}
.data_center_Part.green {
	border: 1px solid #05c464;
}
.data_center_Part.yellow {
	border: 1px solid #ffbe24;
}
.data_center_Part.blue {
	border: 0px solid #5ab4d7;
}
.data_center_Part.darkblue {
	border: 0px solid #7a85a2;
}
.data_center_Part.purple {
	border: 0px solid #bc82bb;
}
.data_center_Part.gray {
	border: 0px solid #bfc1c0;
}

/*data_center_operation*/
.data_center_Part_operation.x-item-over{
    border:1px solid #dddddd;
    background: #f6f7ff;
    cursor:pointer;
}
.data_center_Part_operation.x-item-selected {
	background: #e3ebff;
	border: 1px solid #99bbe8;
}
.data_center_Part_operation img.selected {
	position: absolute;
	top: 2px;
	right:2px;
	float:right;
	display: none;
}
.data_center_Part_operation.x-item-selected img.selected{
	display: block;
}
.data_center_Part_operation.red {
	border:1px solid #fe6869;
}
.data_center_Part_operation.green {
	border: 1px solid #05c464;
}
.data_center_Part_operation.yellow {
	border: 1px solid #ffbe24;
}
.data_center_Part_operation.blue {
	border: 1px solid #13b1f5;
}
.data_center_Part_operation.darkblue {
	border: 1px solid #7a85a2;
}
.data_center_Part_operation.purple {
	border: 1px solid #a965ca;
}
.data_center_Part_operation.gray {
	border: 1px solid #bfc1c0;
}
.data_center_Part_operation.green .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_green.png);
}
.data_center_Part_operation.yellow .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_yellow.png);
}
.data_center_Part_operation.blue .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_blue.png);
}
.data_center_Part_operation.purple .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_purple.png);
}
.data_center_Part_operation.green .T-Center-operation-Part2 {
	background-color: #05c464;
	background-image: -webkit-linear-gradient(top, #05c464 0%, #05c464 100%);
  	background-image: -moz-linear-gradient(top, #05c464 0%, #05c464 100%);
  	background-image: -o-linear-gradient(top, #05c464 0%,#05c464 100%);
  	background-image: linear-gradient(to bottom, #05c464 0%, #05c464 100%);
}
.data_center_Part_operation.yellow .T-Center-operation-Part2 {
	background-color: #ffbe24;
	background-image: -webkit-linear-gradient(top, #ffbe24 0%, #ffbe24 100%);
  	background-image: -moz-linear-gradient(top, #ffbe24 0%, #ffbe24 100%);
  	background-image: -o-linear-gradient(top, #ffbe24 0%,#ffbe24 100%);
  	background-image: linear-gradient(to bottom, #ffbe24 0%, #ffbe24 100%);
}
.data_center_Part_operation.blue .T-Center-operation-Part2 {
	background-color: #13b1f5;
	background-image: -webkit-linear-gradient(top, #6ad1fe 0%, #13b1f5 100%);
  	background-image: -moz-linear-gradient(top, #6ad1fe 0%, #13b1f5 100%);
  	background-image: -o-linear-gradient(top, #6ad1fe 0%,#13b1f5 100%);
  	background-image: linear-gradient(to bottom, #6ad1fe 0%, #13b1f5 100%);
}
.data_center_Part_operation.purple .T-Center-operation-Part2 {
	background-color: #a965ca;
	background-image: -webkit-linear-gradient(top, #c47de6 0%, #a965ca 100%);
  	background-image: -moz-linear-gradient(top, #c47de6 0%, #a965ca 100%);
  	background-image: -o-linear-gradient(top, #c47de6 0%,#a965ca 100%);
  	background-image: linear-gradient(to bottom, #c47de6 0%, #a965ca 100%);
}
.data_center_Part_operation.red .T-Center-operation-Part2 {
	background-color: #fb594e;
	background-image: -webkit-linear-gradient(top, #fc8981 0%, #fb594e 100%);
  	background-image: -moz-linear-gradient(top, #fc8981 0%, #fb594e 100%);
  	background-image: -o-linear-gradient(top, #fc8981 0%,#fb594e 100%);
  	background-image: linear-gradient(to bottom, #fc8981 0%, #fb594e 100%);
}
.data_center_Part_operation.red .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_red.png);
}
.data_center_Part_operation .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_green.png);
}
.data_center_Part_operation.yellow .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_yellow.png);
}
.data_center_Part_operation.blue .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_blue.png);
}
.data_center_Part_operation.darkblue .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_darkblue.png);
}
.data_center_Part_operation.purple .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_purple.png);
}
.data_center_Part_operation.gray .C-Part1_Bg_operation div {
	background-image: url(../images/Two_C_img__operation_gray.png);
}


.data_center_Part.red .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_red.png);
}
.data_center_Part.green .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_green.png);
}
.data_center_Part.yellow .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_yellow.png);
}
.data_center_Part.blue .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_blue.png);
}
.data_center_Part.darkblue .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_darkblue.png);
}
.data_center_Part.purple .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_purple.png);
}

.data_center_Part.gray .C-Part1_Bg div {
	background-image: url(../images/Two_C_img_gray.png);
}

.data_center_Part.red .T-Center-Part2 {
	/*background-color: #fe6869;*/
}

.data_center_Part.green .T-Center-Part2 {
	background-color: #05c464;
	background-image: -webkit-linear-gradient(top, #05c464 0%, #05c464 100%);
  	background-image: -moz-linear-gradient(top, #05c464 0%, #05c464 100%);
  	background-image: -o-linear-gradient(top, #05c464 0%,#05c464 100%);
  	background-image: linear-gradient(to bottom, #05c464 0%, #05c464 100%);
}
.data_center_Part.yellow .T-Center-Part2 {
	background-color: #ffbe24;
	background-image: -webkit-linear-gradient(top, #ffbe24 0%, #ffbe24 100%);
  	background-image: -moz-linear-gradient(top, #ffbe24 0%, #ffbe24 100%);
  	background-image: -o-linear-gradient(top, #ffbe24 0%,#ffbe24 100%);
  	background-image: linear-gradient(to bottom, #ffbe24 0%, #ffbe24 100%);
}
.data_center_Part.blue .T-Center-Part2 {
	/*background-color: #5ab4d7;*/
}
.data_center_Part.darkblue .T-Center-Part2 {
	/*background-color: #7a85a2;*/
}
.data_center_Part.purple .T-Center-Part2 {
	/*background-color: #bc82bb;*/
}

.data_center_Part.gray .T-Center-Part2 {
	/*background-color: #bfc1c0;*/
}


#Layer1 {
	position:absolute;
	left:288px;
	top:75px;
	width:0px;
	height:1px;
	z-index:1;
}
.wrapper {
  position: relative;
    margin: 15px 25px;
    height: 115px;
    background: red;
    text-align: center;
    border-radius:4px;
    -webkit-border-radius:4px;
    -moz-border-radius:4px;
    color:#fff;
    font-family:"Microsoft Yahei";
}

.wrapper.green {
	background: rgb(36, 171, 94);
}

.wrapper.blue {
	background: rgb(45, 138, 234);
}
.wrapper.pink {
	background: rgb(255, 103, 105);
}

.wrapper.yellow {
	background: rgb(255, 169, 60);
}
.percent {
	width:100%;
	text-align:right;
    margin: 5px 0 0 -10px;
    padding: 0;
}
.content {
	width: 138px;
    margin: 0 auto;
    padding:15px 0 0 0;
}
#d_1_lef{
	font-size:18px;
}
#d_1_south{
	font-size:14px;
	padding:5px 0;
}
.content p{
	padding:0;
	margin:0;
}
.content img{
	float:left;
	
}
.content .content_R{
	float:left;
	margin:0 0 0 30px;
	
}
.hostState_Title{
	font-family:"Microsoft Yahei";
	font-size:14px;
	padding:5px 0 0 20px;
	color:#595757;
}

.change_pwd{
	margin:100px auto;
	}
.shell_info{text-align:center;}
.switch_span a{color:#007af5;text-decoration:none;font-size:14px;}
.switch_span a:hover{text-decoration:underline;font-size:14px;}

.x-grid-row-selected .x-grid-td a{color:#007af5;line-height:16px;}
.x-grid-row-over .x-grid-td a{color:#007af5;line-height:16px;}
.x-grid-row-yellow-poc .x-grid-td {background-color:#fbdba6;}
.x-grid-cell a{color:#007af5;text-decoration:none;font-size:14px;line-height:16px;}

.monitor_desc{
	width:98%;
	height:200px;
	border:1px solid #dddfeb; 
	font-size:13px;padding:5px;
	font-family:"Microsoft Yahei";
	background-color:#fff;
	color:#303340;
	overflow:auto}
.Monitor_Btn{font-family:"Microsoft Yahei";cursor:pointer;background-color:transparent;font-size:13px;}
.errorUT_Font{padding:10px 0;font-size:14px;}

/*闂備礁鎲￠悷銉︾附閺冨牊鍤堥柟杈剧悼閻わ拷闂佸憡鍔﹂崢鍓х不婵犳艾绠归弶鍫㈠亾鐎氾拷*/
.progressbar {
  text-align: center;
  line-height: 62px; 
  width: 62px; display: block; 
  height: 62px; 
  font-size: 14px; 
  font-family: "Microsoft Yahei";
  font-size: 11px;
}

.progressbar_operation {
  text-align: center;
  line-height: 40px; 
  width: 40px; display: block; 
  height: 40px; 
  font-size: 14px; 
  font-family: "Microsoft Yahei";
  font-size: 11px;
}
.progressbar-0 {background-position: 0px 0px;}
.progressbar-1 {background-position: -66px 0px;}
.progressbar-2 {background-position: -132px 0px;}
.progressbar-3 {background-position: -198px 0px;}
.progressbar-4 {background-position: -264px 0px;}
.progressbar-5 {background-position: -330px 0px;}
.progressbar-6 {background-position: -396px 0px;}
.progressbar-7 {background-position: -462px 0px;}
.progressbar-8 {background-position: -528px 0px;}
.progressbar-9 {background-position: -594px 0px;}
.progressbar-10 {background-position: -660px 0px;}
.progressbar-11 {background-position: -726px 0px;}
.progressbar-12 {background-position: -792px 0px;}
.progressbar-13 {background-position: -858px 0px;}
.progressbar-14 {background-position: -924px 0px;}
.progressbar-15 {background-position: -990px 0px;}
.progressbar-16 {background-position: -1056px 0px;}
.progressbar-17 {background-position: -1122px 0px;}
.progressbar-18 {background-position: -1188px 0px;}
.progressbar-19 {background-position: -1254px 0px;}
.progressbar-20 {background-position: -1320px 0px;}
.progressbar-21 {background-position: -1386px 0px;}
.progressbar-22 {background-position: -1452px 0px;}
.progressbar-23 {background-position: -1518px 0px;}
.progressbar-24 {background-position: -1584px 0px;}
.progressbar-25 {background-position: -1650px 0px;}
.progressbar-26 {background-position: -1716px 0px;}
.progressbar-27 {background-position: -1782px 0px;}
.progressbar-28 {background-position: -1848px 0px;}
.progressbar-29 {background-position: -1914px 0px;}
.progressbar-30 {background-position: -1980px 0px;}
.progressbar-31 {background-position: -2046px 0px;}
.progressbar-32 {background-position: -2112px 0px;}
.progressbar-33 {background-position: -2178px 0px;}
.progressbar-34 {background-position: -2244px 0px;}
.progressbar-35 {background-position: -2310px 0px;}
.progressbar-36 {background-position: -2376px 0px;}
.progressbar-37 {background-position: -2442px 0px;}
.progressbar-38 {background-position: -2508px 0px;}
.progressbar-39 {background-position: -2574px 0px;}
.progressbar-40 {background-position: -2640px 0px;}
.progressbar-41 {background-position: -2706px 0px;}
.progressbar-42 {background-position: -2772px 0px;}
.progressbar-43 {background-position: -2838px 0px;}
.progressbar-44 {background-position: -2904px 0px;}
.progressbar-45 {background-position: -2970px 0px;}
.progressbar-46 {background-position: -3036px 0px;}
.progressbar-47 {background-position: -3102px 0px;}
.progressbar-48 {background-position: -3168px 0px;}
.progressbar-49 {background-position: -3234px 0px;}
.progressbar-50 {background-position: -3300px 0px;}
.progressbar-51 {background-position: -3366px 0px;}
.progressbar-52 {background-position: -3432px 0px;}
.progressbar-53 {background-position: -3498px 0px;}
.progressbar-54 {background-position: -3564px 0px;}
.progressbar-55 {background-position: -3630px 0px;}
.progressbar-56 {background-position: -3696px 0px;}
.progressbar-57 {background-position: -3762px 0px;}
.progressbar-58 {background-position: -3828px 0px;}
.progressbar-59 {background-position: -3894px 0px;}
.progressbar-60 {background-position: -3960px 0px;}
.progressbar-61 {background-position: -4062px 0px;}
.progressbar-62 {background-position: -4092px 0px;}
.progressbar-63 {background-position: -4158px 0px;}
.progressbar-64 {background-position: -4224px 0px;}
.progressbar-65 {background-position: -4290px 0px;}
.progressbar-66 {background-position: -4356px 0px;}
.progressbar-67 {background-position: -4422px 0px;}
.progressbar-68 {background-position: -4488px 0px;}
.progressbar-69 {background-position: -4554px 0px;}
.progressbar-70 {background-position: -4620px 0px;}
.progressbar-71 {background-position: -4686px 0px;}
.progressbar-72 {background-position: -4752px 0px;}
.progressbar-73 {background-position: -4818px 0px;}
.progressbar-74 {background-position: -4884px 0px;}
.progressbar-75 {background-position: -4950px 0px;}
.progressbar-76 {background-position: -5016px 0px;}
.progressbar-77 {background-position: -5082px 0px;}
.progressbar-78 {background-position: -5148px 0px;}
.progressbar-79 {background-position: -5214px 0px;}
.progressbar-80 {background-position: -5280px 0px;}
.progressbar-81 {background-position: -5346px 0px;}
.progressbar-82 {background-position: -5412px 0px;}
.progressbar-83 {background-position: -5478px 0px;}
.progressbar-84 {background-position: -5544px 0px;}
.progressbar-85 {background-position: -5610px 0px;}
.progressbar-86 {background-position: -5676px 0px;}
.progressbar-87 {background-position: -5742px 0px;}
.progressbar-88 {background-position: -5808px 0px;}
.progressbar-89 {background-position: -5874px 0px;}
.progressbar-90 {background-position: -5940px 0px;}
.progressbar-91 {background-position: -6006px 0px;}
.progressbar-92 {background-position: -6072px 0px;}
.progressbar-93 {background-position: -6138px 0px;}
.progressbar-94 {background-position: -6204px 0px;}
.progressbar-95 {background-position: -6270px 0px;}
.progressbar-96 {background-position: -6336px 0px;}
.progressbar-97 {background-position: -6402px 0px;}
.progressbar-98 {background-position: -6468px 0px;}
.progressbar-99 {background-position: -6534px 0px;}
.progressbar-100 {background-position: -6600px 0px;}
.panel_center{margin:0 0 0 10px;}
.status_common_m{
	width:90px;
	height:auto;
	text-align:center;
	font-size:12px;
	font-family:"";
	color:#595757;
	float:left;
	margin:0 0px;
	position:relative;
	z-index:1;
	}
.status_common_m p{
	padding:5px 0;
	margin:0;
}
.status_common{
	width:90px; 
	height:90px; 
	margin:0 0 0 0;
	padding:0px 0 0 0;
	line-height:1.5;
	text-align:center;
	}
.status_arrow{
	background-image:url(../images/status_arrow.png);
	width:59px;
	height:90px;
	float:left;
	position:relative;
	z-index:0;
	}
.status_line{
	width:auto;
	height:100;
	background-image:url(../images/status_line.png);
	background-repeat:repeat-x;
	/*background:url(../images/status_line.png) repeat center center;*/
	overflow:hidden;
	float:left;
	display:block;
}
.status_pause_m_img{ 
	background-image:url(../images/status_pause_m.png);
	cursor:pointer;
	}
.status_gray_m_img{ 
	background-image:url(../images/status_gray_m.png);
	cursor:pointer;
	}
.status_green_m_img{
	background-image:url(../images/status_green_m.png);
	cursor:pointer;
	}
.status_green_s_img{
	background-image:url(../images/status_green_s.png);
	cursor:pointer;
}
.status_blue_s_img{
	background-image:url(../images/status_blue_s.gif);
	cursor:pointer;
}
.status_gray_s_img{
	background-image:url(../images/status_gray_s.png);
	cursor:pointer;
	}
.status_orange_s_img{
	background-image:url(../images/status_orange_s.gif);
	cursor:pointer;
	}
.status_red_s_img{
	background-image:url(../images/status_red_s.gif);
	cursor:pointer;
	}
.status_purple_s_img{
	background-image:url(../images/status_purple_s.gif);
	cursor:pointer;
	}
.status_common span{ display:block; color:#FFF;}
.status_common div{padding:26px 0 0 0;}
.status_gray_m_img span{
	color:#595757; 
	display:block;width:80px;white-space:nowrap;word-break:keep-all;
	text-overflow:ellipsis;overflow:hidden;text-align:center;
}
.status_green_m_img span{
	color:#595757;
	display:block;width:80px;white-space:nowrap;word-break:keep-all;
	text-overflow:ellipsis;overflow:hidden;text-align:center;
}

.process_steps{
	padding:0; margin:25px 5px 0 5px; 
	height:auto;width:70px;
	white-space:nowrap;word-break:keep-all;
	text-overflow:ellipsis;
	overflow:hidden;
	cursor:pointer;
}
.process_on{
	margin:63px 0 0 0;
	
}

.monitorGraph{
	border-bottom:1px solid #727171;
	margin:0 10px;
	padding:0 0px;
}
.instanceName{
	font-size:14px;
	color:#3e3a39;
	font-family:"Microsoft Yahei";
	line-height:16px;
	padding:10px 0;
	width:100%;
	float:left;
	}
/*.instanceName_img{background-image:url(../images/instanceName.png);
		width:10px; height:16px;display:block;float:left}*/
.status_dc{ width:70px;height:26px;position:absolute;z-index:1;}
.maindc{background-image:url(../images/maindc_shape.png);width:26px;height:26px;
display:block;position:relative;z-index:0;margin:-10px 0 0 35px;}
.predc{background-image:url(../images/predc_shape.png);width:26px;height:26px;
display:block;position:relative;z-index:0;margin:-10px 0 0 35px;
}

.clearfix:after 
{ 
	content:"."; 
	display:block; 
	height:0; 
	clear:both; 
	visibility:hidden; 
} 
.clearfix 
{
	display:inline-block;
}
/* Hide from IE Mac */ 
.clearfix {
	display:block;
	clear:both; 
	padding:0 0 0 0;
} 
/* End hide from IE Mac */ 

.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.clearfix {
	display: inline-block;
}
/* Hides from IE-mac \*/
* html .clearfix { height: 1%;}
.clearfix {display: block;}
/* End hide from IE-mac */

#selectedSys {
	height: 66px;
	width: 100%;
	position: absolute;
	top: 0;
	padding-left:5px;
	padding-top: 5px;
	display: none;
}
#selectedSys #selectedSysWrapper {
	border: 1px solid gray;
	background: white;
	height: 54px;
	margin-right: 230px;
	overflow-y: scroll;
}
#selectedSys #selectedSysWrapper .choices{
	padding: 5px 0 0 0;
	margin: 0;
}
#selectedSys #selectedSysWrapper .choices .searchChoice{
	background: #6aba49;
	border: none;
	color: #fff;
	font-weight: bold;
	margin: 0 0 5px 5px;
	padding: 6px 30px 6px 10px;
	line-height: 13px;
	position: relative;
	cursor: default;
	float: left;
	list-style: none;
}
#selectedSys #selectedSysWrapper .choices .searchChoice .searchChoiceClose{
	display: block;
	left: auto;
	right: 5px;
	top: 6px;
	position: absolute;
	width: 12px;
	height: 13px;
	font-size: 1px;
	background: url(../images/choice.png) right -44px no-repeat;
	outline: none;
}
.hd_t_td2 {
	position: relative;
}

.Baseline {
	width: 100%;
	height: 40px;
	background-color:#fff;
	border-bottom:1px solid #d9d9d9;
}

.Oval_Content {
	height: 30px;
	display: inline-block;
	*display: inline;
	zoom: 1; 
	padding: 0 0px;
	cursor: pointer;
	float:left;
	margin:0px 0 0 0px;
}

.font-color-deep {
	color: red;
}
.selectedCount.font-color-deep {
	color: #f01024;
	font-size:16px;
	
}

.Oval_Left {
	width: 8px;
	height: 40px;
	float: left
}

.Oval_Center {
	background-repeat: repeat-x;
	height: 40px;
	font-size: 14px;
	font-family: "Microsoft YaHei";
	line-height: 39px;
	padding: 0 0 0 10px;
	float: left;
	color:#303340;
}

.Oval_Right {
	width: 8px;
	height: 40px;
	float: left
}

.Oval_tab {
	background-color: #FFF;
	padding: 0 10px;
}

.Oval_up {
	background-image: url(../images/Oval_up.png);
	width: 12px;
	height: 12px;
	float: right;
	cursor: pointer;
	margin:14px 10px 0 0;
}
.Oval_Info {
	float:left;
	margin:0 0 0 15px;
}
.Oval_down {
	background-image: url(../images/Oval_down.png);
	width: 12px;
	height: 12px;
	float: right;
	cursor: pointer;
	margin:14px 10px 0 0;
}
.Oval_Font{ 
	font-size:14px; 
	line-height:40px; 
	float:left;
	font-family: "Microsoft Yahei";
	color:#929ea8;
	}
.Oval_Font i{ padding: 0 3px;}
.check_box {
	width: 12px;
	height: 12px;
	float: left;
	cursor: pointer;
	margin: 14px 0 0 10px;
	text-align: center;
	background-image: url(../images/checkbox_02.gif);
	background-repeat: no-repeat;
	background-position: 0 0;
	position:absolute;
	left:0;
	}
.on_check {
	background-position: 0 -12px;
	}
.on_check_not_all{
	background-position: 0 -24px;
	}
.selectAllChild {
	opacity: 0;
	cursor: pointer;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	}
.Fill_Color{
	background-color:#fff;
}


.panel {
color: #929ea8;
margin-bottom: 20px;
background-color: #ffffff;
border: 1px solid transparent;
border-radius: 0;
}
.panel-default {
border-color: #ffffff;
border-radius:4px
}
.panel-default > .panel-heading {
color: #46545d;
background-color: #ffffff;
border-color: #d9d9d9;
}
.panel-heading {
padding: 10px 0 10px 10px;
border-bottom: 1px solid transparent;
border-top-right-radius: 3px;
border-top-left-radius: 3px;
}
.panel-heading h3 {
margin: 0;
line-height:20px;
font-size: 14px;
font-weight:normal;
}
.row.no-space {
margin-right: 0;
margin-left: 0;
}
.row.vertical-border > [class*=col-]:nth-of-type(1) {
border-left: none;
}
.row.vertical-border > [class*=col-] {
border-left: 0px solid #ececec;
}
.row.no-space > [class*=col-] {
padding-right: 0;
padding-left: 0;
}
.col-sm-6 {
width: 49%;
float: left;
position: relative;
min-height: 1px;
}
.panel-body {
padding: 10px 0 5px 10px;
}
.benefits-list li {
margin-bottom: 12px;
font-size: 13px;
color: #46545d;
}

.panel h2 {
	margin: 0;
	font-size: 16px;
	font-weight:normal;
}

.Oval_Content.on .Oval_Left {
	background-image: url(../images/Oval_Left_select.png);
	width: 8px;
	height: 30px;
	float: left
}
.Oval_Content.on .Oval_Center {
	background-image: url(../images/Oval_Center_select.png);
	background-repeat: repeat-x;
	height: 30px;
	font-size: 14px;
	font-family: "Microsoft Yahei";
	line-height: 30px;
	padding: 0 15px;
	float: left;
	color:#626b7d;
}
.Oval_Content.on .Oval_Right {
	background-image: url(../images/Oval_Right_select.png);
	width: 8px;
	height: 30px;
	float: left
}
/* CSS Document */
.Graph_line{font-family:"Microsoft Yahei"; font-size:12px; color:#231815; float:left; display:block; background-color:#fff}
.CMB_Start_state{ width:105px; float:left;background-image:url(../images/graph_line.png); background-repeat:repeat-x;}
.CMB_Start_Bg{ background-image:url(../images/CMB_Start_state.png); width:105px; height:127px;background-position:0 -127px}
.CMB_Start_state p{ width:105px;word-wrap:break-word; word-break:normal; text-align:center;text-overflow:ellipsis;padding:0;margin:0;height:30px; line-height:30px;}
.CMB_state{float:left;background-image:url(../images/graph_line.png); background-repeat:repeat-x;}
.CMB_state p{ 
	font-family:"Microsoft Yahei";
	padding:0 0 0 0;
	color:#231815;
	margin:0; 
	height:30px; 
	line-height:30px;
	word-wrap:break-word; 
	word-break:normal;
	/* Add by li_yang 闂備礁鎼�氼剚鏅舵禒瀣︽慨妯垮煐閸庢垵霉閿濆洤鍔嬫繛鍫秮閺屾稑鈻庡▎鎰伓*/
	white-space: nowrap;
	overflow: hidden; 
	/* end by li_yang */
	text-align:center;
	text-overflow:ellipsis
}
.CMB_state span{ display:block;}

.CMB_Common1{width:125px; height:105px;color:#FFF; text-align:center;line-height:1.5;}/*闂備胶绮…鍫ュ春閺嶎厽鏅搁柡鍌樺�栫�氾拷*/
.CMB_Common2{ width:21px; height:21px;margin:0 auto; text-align:center; line-height:21px }/*circle*/
.CMB_Common3{width:12px; height:12px;margin:5px auto}

.CMB_Common1 div{padding:30px 0 0 0;}

.CMB_green_state{ background-image:url(../images/CMB_Green_status.png);}
.CMB_green_Circle{ background-image:url(../images/CMB_Circle.png);background-position:0 -105px; }
.CMB_green_Point{background-image:url(../images/CMB_Point.png);  background-position:0 -72px;}

.CMB_blue_state{ background-image:url(../images/CMB_Blue_state.gif);}
.CMB_blue_Circle{background-image:url(../images/CMB_Circle.png);background-position:0 -84px;}
.CMB_blue_Point{background-image:url(../images/CMB_Point.png);  background-position:0 -60px;}

.CMB_orange_state{ background-image:url(../images/CMB_Orange_state.gif);}
.CMB_orange_Circle{background-image:url(../images/CMB_Circle.png);background-position:0 -63px;}
.CMB_orange_Point{background-image:url(../images/CMB_Point.png);  background-position:0 -48px;}

.CMB_gray_state{background-image:url(../images/CMB_Gray_state.png);}
.CMB_gray_Circle{background-image:url(../images/CMB_Circle.png);background-position:0 -42px;}
.CMB_gray_Point{background-image:url(../images/CMB_Point.png);  background-position:0 -36px;}

.CMB_purple_state{background-image:url(../images/CMB_Purple_state.png);}
.CMB_purple_Circle{background-image:url(../images/CMB_Circle.png);background-position:0 -21px;}
.CMB_purple_Point{background-image:url(../images/CMB_Point.png);  background-position:0 -24px;}

.CMB_red_state1{background-image:url(../images/CMB_status_red_s.gif);}
.CMB_red_state{background-image:url(../images/CMB_status_red_s.png);}
.CMB_red_Circle{background-image:url(../images/CMB_Circle.png);background-position:0 0px;}
.CMB_red_Point{background-image:url(../images/CMB_Point.png);  background-position:0 -12px;}

.CMB_End_Bg{ background-image:url(../images/CMB_Start_state.png); width:105px; height:127px;background-position:0 -254px}

.CMB_status_common_m{
	width:125px;
	height:auto;
	font-size:12px;
	font-family:"";
	color:#595757;
	float:left;
	margin:0 0px;
	position:relative;
	z-index:1;
	}
.CMB_instanceName{
	font-size:14px;
	font-weight:bolder;
	color:#3e3a39;
	font-family:"Microsoft Yahei";
	line-height:16px;
	padding:10px;
	width:100%;
	float:left;
	}

.body-panel{
	background-color:#23272b;
	box-shadow:0 0 8px #6d757a;
	-webkit-box-shadow:0 0 8px #6d757a;
	-moz-box-shadow:0 0 8px #6d757a;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	}
.body-panel-header-text-container{

}
.body-panel-header-default-horizontal{padding:5px;}
	
.msg .x-box-mc {
    font-size:14px;
}
#msg-div {
    position:absolute;
    left:50%;
    top:10px;
    width:400px;
    margin-left:-200px;
    z-index:20000;
}
#msg-div .msg {
    border-radius: 8px;
    -moz-border-radius: 8px;
    background: #F6F6F6;
    border: 2px solid #ccc;
    margin-top: 2px;
    padding: 10px 15px;
    color: #555;
}
#msg-div .msg h3 {
    margin: 0 0 8px;
    font-weight: bold;
    font-size: 15px;
}
#msg-div .msg p {
    margin: 0;
}
/*web UL*/
.webui-popover-content {
  display: none;
}
.webui-popover-rtl {
  direction: rtl;
  text-align: right;
}
/*  webui popover  */
.webui-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  display: none;
  min-width: 50px;
  min-height: 32px;
  padding: 1px;
  text-align: left;
  white-space: normal;
}
.webui-popover.top,
.webui-popover.top-left,
.webui-popover.top-right {
  margin-top: -10px;
}
.webui-popover.right,
.webui-popover.right-top,
.webui-popover.right-bottom {
  margin-left: 10px;
}
.webui-popover.bottom,
.webui-popover.bottom-left,
.webui-popover.bottom-right {
  margin-top: 10px;
}
.webui-popover.left,
.webui-popover.left-top,
.webui-popover.left-bottom {
  margin-left: -10px;
}
.webui-popover.pop {
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  -o-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.pop-out {
  -webkit-transition-property: "opacity,transform";
  -o-transition-property: "opacity,transform";
  transition-property: "opacity,transform";
  -webkit-transition: 0.15s linear;
  -o-transition: 0.15s linear;
  transition: 0.15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.fade,
.webui-popover.fade-out {
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.out {
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.in {
  -webkit-transform: none;
  -o-transform: none;
  transform: none;
  opacity: 1;
  filter: alpha(opacity=100);
}
.webui-popover .webui-popover-content {
  padding: 0;
  overflow: auto;
  display: block;
}
.webui-popover-inner .close {
  font-family: arial;
  margin: 8px 10px 0 0;
  float: right;
  font-size: 16px;
  font-weight: bold;
  line-height: 16px;
  color: #000000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20);
  text-decoration: none;
}
.webui-popover-inner .close:hover,
.webui-popover-inner .close:focus {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.webui-popover-inner .close:after {
  content: "\00D7";
  width: 0.8em;
  height: 0.8em;
  padding: 4px;
  position: relative;
}
.webui-popover-title {
  padding: 8px 14px;
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  line-height: 18px;
  background-color: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 5px 5px 0 0;
}
.webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
  display: none;
}
.webui-popover-inverse {
  background-color: #333333;
  color: #eeeeee;
}
.webui-popover-inverse .webui-popover-title {
  background: #333333;
  border-bottom: 1px solid #3b3b3b;
  color: #eeeeee;
}
.webui-no-padding .webui-popover-content {
  padding: 0;
}
.webui-no-padding .list-group-item {
  border-right: none;
  border-left: none;
}
.webui-no-padding .list-group-item:first-child {
  border-top: 0;
}
.webui-no-padding .list-group-item:last-child {
  border-bottom: 0;
}
.webui-popover > .webui-arrow,
.webui-popover > .webui-arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.webui-popover > .webui-arrow {
  border-width: 11px;
}
.webui-popover > .webui-arrow:after {
  border-width: 10px;
  content: "";
}
.webui-popover.top > .webui-arrow,
.webui-popover.top-right > .webui-arrow,
.webui-popover.top-left > .webui-arrow {
  bottom: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  border-bottom-width: 0;
}
.webui-popover.top > .webui-arrow:after,
.webui-popover.top-right > .webui-arrow:after,
.webui-popover.top-left > .webui-arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-top-color: #ffffff;
  border-bottom-width: 0;
}
.webui-popover.right > .webui-arrow,
.webui-popover.right-top > .webui-arrow,
.webui-popover.right-bottom > .webui-arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.right > .webui-arrow:after,
.webui-popover.right-top > .webui-arrow:after,
.webui-popover.right-bottom > .webui-arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #ffffff;
}
.webui-popover.bottom > .webui-arrow,
.webui-popover.bottom-right > .webui-arrow,
.webui-popover.bottom-left > .webui-arrow {
  top: -11px;
  left: 50%;
  margin-left: -11px;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  border-top-width: 0;
}
.webui-popover.bottom > .webui-arrow:after,
.webui-popover.bottom-right > .webui-arrow:after,
.webui-popover.bottom-left > .webui-arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #ffffff;
  border-top-width: 0;
}
.webui-popover.left > .webui-arrow,
.webui-popover.left-top > .webui-arrow,
.webui-popover.left-bottom > .webui-arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.left > .webui-arrow:after,
.webui-popover.left-top > .webui-arrow:after,
.webui-popover.left-bottom > .webui-arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #ffffff;
  bottom: -10px;
}
.webui-popover-inverse.top > .webui-arrow,
.webui-popover-inverse.top-left > .webui-arrow,
.webui-popover-inverse.top-right > .webui-arrow,
.webui-popover-inverse.top > .webui-arrow:after,
.webui-popover-inverse.top-left > .webui-arrow:after,
.webui-popover-inverse.top-right > .webui-arrow:after {
  border-top-color: #333333;
}
.webui-popover-inverse.right > .webui-arrow,
.webui-popover-inverse.right-top > .webui-arrow,
.webui-popover-inverse.right-bottom > .webui-arrow,
.webui-popover-inverse.right > .webui-arrow:after,
.webui-popover-inverse.right-top > .webui-arrow:after,
.webui-popover-inverse.right-bottom > .webui-arrow:after {
  border-right-color: #333333;
}
.webui-popover-inverse.bottom > .webui-arrow,
.webui-popover-inverse.bottom-left > .webui-arrow,
.webui-popover-inverse.bottom-right > .webui-arrow,
.webui-popover-inverse.bottom > .webui-arrow:after,
.webui-popover-inverse.bottom-left > .webui-arrow:after,
.webui-popover-inverse.bottom-right > .webui-arrow:after {
  border-bottom-color: #333333;
}
.webui-popover-inverse.left > .webui-arrow,
.webui-popover-inverse.left-top > .webui-arrow,
.webui-popover-inverse.left-bottom > .webui-arrow,
.webui-popover-inverse.left > .webui-arrow:after,
.webui-popover-inverse.left-top > .webui-arrow:after,
.webui-popover-inverse.left-bottom > .webui-arrow:after {
  border-left-color: #333333;
}
.webui-popover i.icon-refresh:before {
  content: "";
}
.webui-popover i.icon-refresh {
  display: block;
  width: 30px;
  height: 30px;
  font-size: 20px;
  top: 50%;
  left: 50%;
  position: absolute;
  margin-left: -15px;
  margin-right: -15px;
  background: url(../img/loading.gif) no-repeat;
}
@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
.webui-popover-backdrop {
  background-color: rgba(0, 0, 0, 0.65);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9998;
}
.webui-popover .dropdown-menu {
  display: block;
  position: relative;
  top: 0;
  border: none;
  box-shadow: none;
  float: none;
}

/*Graph*/
ul{ padding:0; margin:0;}
ul li{ padding:0; margin:0; list-style:none}
img{ 
	border:0;
	cursor:pointer;
	}
.DR_Panel{
height:100%;
}
.DR_Panel_body{
width:auto;height:auto;
margin:auto;
}
.Change_Panel{
height:100%;
}
.Change_Panel_body{
width:1215px;height:auto;
margin:auto;
}
.Em_Panel{
height:99%;
margin:5px 0 0 0;
}
.Em_Panel_body{
width:100%;height:100%;
margin:0 auto;
}

#package_bottom{
	margin:5px 0 0 0;
}
.Change_leftcon{
}
.Package_LeftPage{
	background-image:url(../images/Package_LeftPage.png);
	width:24px;
	height:24px;
	margin:0 10px;
	cursor:pointer;
}
.Package_LeftPage:hover{
	opacity:0.6;
	filter:alpha(opacity=60); 
}
.Package_RightPage{
	background-image:url(../images/Package_RightPage.png);
	width:24px;
	height:24px;
	margin:0 10px;
	cursor:pointer;
}
.Package_RightPage:hover{
	opacity:0.6;
	filter:alpha(opacity=60); 
}
.Change_Body{ 
	width:100%; 
	height:100%; 
	font-family:"Microsoft Yahei"; 
	font-size:12px;
	background-color:transparent;
	}
.Change_Left_Common{
	/*border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:1px solid #cccccc;
	background-color: rgba(255,255,255,0.3);
	box-shadow:3px 3px 4px #16181b inset;
	-webkit-box-shadow:3px 3px 4px #16181b inset;
	-moz-box-shadow:3px 3px 4px #16181b inset;
	-o-box-shadow:3px 3px 4px #16181b inset;
	float:left;
	position:relative;
	overflow:auto;*/
}
.Disaster_Recovery_Left{
	width:225px;
	margin:0 0 0 30px;
}
.Disaster_Recovery_Left ul li{
	float:right;
	margin:8px 0 0 0;
	font-size:14px;
	line-height:31px;
	}
.Disaster_Recovery_Left ul li .System_info{ 
	float:left; 
	width:265px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.Change_Left{ 
	width:225px;
	height:auto;
	margin:0 10px;
	}
.Change_Left ul li{
	float:right;
	margin:8px 0 0 0;
	font-size:12px;
	line-height:31px;
	}
.Change_Left ul li .System_info{ 
	float:left; 
	width:260px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.Change_Left_SUS{ 
	height:auto;
	margin:0 10px;
	}
.Change_Left_SUS ul li{
	margin:0 0 0 0;
	font-size:12px;
	line-height:31px;
	}
.Change_Left_SUS ul li .System_info{ 
	float:left; 
	width:260px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.Change_Left_SUS.resize_width ul li .System_info{
	float:left; 
	width:250px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.System{ 
	float:left;
	border-radius:50%;
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	-o-border-radius:50%;
	width:21px;
	height:21px;
	display:block;
	margin:10px 0 0 0;
	}
.Leftmenu_over_btn{
	cursor:pointer;
	width:100%;
	color:#46545d;
	background-color:#ffffff;
	}
.C_Leftmenu_over_btn{
	
	}
.Leftmenu_btn{
	cursor:pointer;
	width:100%;
	border-top:0px solid #dcdddd;
	border-bottom:0px solid #dcdddd;
	color:#46545d;
	background:transparent;
	
	}
.C_Leftmenu_btn{
	
	}
.System_yellow{
	background-image:url(../images/System_yellow.png);
}
.System_red{
	background-image:url(../images/System_red.png);
}
.System_green{ 
	background-image:url(../images/System_green.png);
}
.System_purple{
	background-image:url(../images/System_purple.png);
}
.System_blue{ 
	background-image:url(../images/System_blue.png);
}
.System_gray{
	background-image:url(../images/System_gray.png);
}
.System_listblue{
	background-image:url(../images/kl_list_blue.png);
}
.System_pause{
	background-image:url(../images/kl_state_pause.png);
}
.Right_Common{
	border-radius:8px;
	-webkit-border-radius:8px;
	-moz-border-radius:8px;
	-o-border-radius:8px;
	border:0px solid #ffffff;
	color:#fff;
	margin:0 0;
	background-position: center;
	width:100%;
	height:100%;
	}
.Right_Commonzb{
	border-radius:8px;
	-webkit-border-radius:8px;
	-moz-border-radius:8px;
	-o-border-radius:8px;
	border:0px solid #ffffff;
	color:#fff;
	margin:0 0;
	background-position: center;
	width:100%;
	}
.Change_Right{
	
	}
.CR_Content{
	height:auto;
	overflow-x: auto;
	border: 0px solid;
	position: relative;
	}
.CR_package{ 
	width:234px;
	border-radius: 8px;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	-o-border-radius: 8px;
	border:1px solid #b5b5b6;
	background-color: #f7f8f8;
	box-shadow:2px 2px 10px #161617;
	-webkit-box-shadow:2px 2px 10px #161617;
	-moz-box-shadow:2px 2px 10px #161617;
	-o-box-shadow:2px 2px 10px #161617;
	margin:10px 20px;
	float:left;
	height:auto;
	}
.Package_Title{
	width:100%;
	height:27px;
	border-radius: 8px 8px 0 0;
	background:#cacacb;
	background-image: -webkit-linear-gradient(top, #f7f8f8 0%, #cacacb 100%);
  	background-image: -moz-linear-gradient(top, #f7f8f8 0%, #cacacb 100%);
  	background-image: -o-linear-gradient(top, #f7f8f8 0%, #cacacb 100%);
  	background-image: linear-gradient(to bottom, #f7f8f8 0%, #cacacb 100%);
	box-shadow:0px 0px 1px #ffffff inset;
	-webkit-box-shadow:0px 0px 1px #ffffff inset;
	-moz-box-shadow:0px 0px 1px #ffffff inset;
	-o-box-shadow:0px 0px 1px #ffffff inset;
	color:#000000;
	line-height:27px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-align:center;
	}
.Package_Title a{
	color:#000;
	text-decoration:underline;
}
.Package_Common{
	padding:0 10px 5px 10px;
	}
.Package{
	width:70px; 
	height:20px;
	border-radius:15px;
	color:#fff;
	line-height:20px;
	text-align:center;
	background:#15b2f6;
	background-image: -webkit-linear-gradient(top, #3bc3ff 0%, #15b2f6 100%);
  	background-image: -moz-linear-gradient(top, #3bc3ff 0%, #15b2f6 100%);
  	background-image: -o-linear-gradient(top, #3bc3ff 0%, #15b2f6 100%);
  	background-image: linear-gradient(to bottom, #3bc3ff 0%, #15b2f6 100%);
	}
.Package_start{ 
	/*background-image:url(../images/Package_start.png); */
	}
.Package_end{
	/*background-image:url(../images/Package_end.png);*/
	margin:5px 0 0 0; 
	}
.Package_arrow{ 
	background-image:url(../images/Package_arrow.png); 
	width:12px; 
	height:12px;
	margin:5px 0 5px 28px;
	}
.Package_arrow_show{ 
	background-image:url(../images/Package_arrow_show.png); 
	width:12px; 
	height:12px;
	margin:5px 0 5px 28px;
	}
.Package_color{
	width:212px;
	height:40px;
	border-radius:4px;
	cursor:pointer;
	}
.Package_Green{
	/*background-image:url(../images/Package_Green.png);*/
	border-left:4px solid #05c464;
	border-top:1px solid #05c464;
	border-right:1px solid #05c464;
	border-bottom:1px solid #05c464;
	color:#05c464;
	}
.Package_Green:hover{
	background-color: rgba(12,191,71,0.2);
}
.Package_Yellow{
	/*background-image:url(../images/Package_Yellow.png);*/
	border-left:4px solid #ffa808;
	border-top:1px solid #ffa808;
	border-right:1px solid #ffa808;
	border-bottom:1px solid #ffa808;
	color:#ffa808;
	}
.Package_Yellow:hover{
	background-color: rgba(255,168,8,0.2);
}
.Package_Gray{
	/*background-image:url(../images/Package_Gray.png);*/
	border-left:4px solid #a3a4a4;
	border-top:1px solid #a3a4a4;
	border-right:1px solid #a3a4a4;
	border-bottom:1px solid #a3a4a4;
	color:#727171;
	}
.Package_Gray:hover{
	background-color: rgba(163,164,164,0.2);
}
.Package_Red{
	/*background-image:url(../images/Package_Red.png);*/
	border-left:4px solid #fa5d52;
	border-top:1px solid #fa5d52;
	border-right:1px solid #fa5d52;
	border-bottom:1px solid #fa5d52;
	color:#fa5d52;
	}
.Package_Red:hover{
	background-color: rgba(250,93,82,0.2);
}
.Package_Purple{
	/*background-image:url(../images/Package_Purple.png);*/
	border-left:4px solid #fc6630;
	border-top:1px solid #fc6630;
	border-right:1px solid #fc6630;
	border-bottom:1px solid #fc6630;
	color:#fc6630;
	}
.Package_Purple:hover{
	background-color: rgba(255,122,35,0.2);
}
.Package_Blue{
	/*background-image:url(../images/Package_Blue.png);*/
	border-left:4px solid #13b1f5;
	border-top:1px solid #13b1f5;
	border-right:1px solid #13b1f5;
	border-bottom:1px solid #13b1f5;
	color:#13b1f5;
	}
.Package_Blue:hover{
	background-color: rgba(19,177,245,0.2);
}
.Package_Font{
	width:212px;
	text-align:center;
	float:left;
	margin:-11px 0 0 0;
	}
.Package_Font span{
	display:block; 
	height:16px; 
	line-height:16px;
	}
.Package_Page{ 
	position:relative; 
	bottom:0;
	float:right;
	background-color:#007af5;
	border-radius:4px;
	margin:0 30px 0 0;
	}
.CR_Body_Package{
	width:270px;
	float:left;
	}
.Package_Page img{ 
	padding:0 0;
	display:block; 
	}
.Package_BodyPage{
	clear:both;
	padding:5px 0;
	margin:0 auto;
	width:auto;
	text-align:center;
	}
.Package_BodyPage img{
	padding:0 30px;
	}
.Remark_all{
	width:auto;margin:0 auto
}
.Package_Remark{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	padding:10px 15px 10px 0;
	margin:10px auto;
	color:#444444;
	font-size:14px;
	height:40px;
	float:left;
	}
.Package_Remark2{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:1px solid #cccccc;
	padding:10px 0;
	width:560px;
	color:#1e252d;
	height:40px;
	background-color: rgba(255,255,255,0.3);
	margin:0 0 0 10px;
}
.Package_Remark_bg{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:0px solid #cccccc;
	padding:10px 0;
	width:600px;
	color:#1e252d;
	height:40px;
	background-color: transparent;
	margin:0 0 0 10px;
}
.Remark_bm_button{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:0px solid #007af5;
	height:34px;
	line-height:34px;
	background-color:#007af5;
	cursor:pointer;
	font-size:12px;
	float:left;
	font-family:Microsoft YaHei;
	}
.Remark_bm_button02{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:0px solid #007af5;
	height:34px;
	line-height:34px;
	background-color:#ffffff;
	cursor:pointer;
	font-size:12px;
	float:left;
	font-family:Microsoft YaHei;
	color:#444444
	}
.Remark_button{
	padding:0 15px;
	height:34px;
	cursor:pointer;
	border-right:0px solid #cccccc;
	float:left;
	}
.Remark_button:hover{
	background-color:#007af5;
	color:#fff;
	}
.Remark_button3:hover{
	background-color:#007af5;
	color:#fff;
	border-radius:4px 0 0 4px;
	}
.Remark_button2{
	padding:0 15px;
	cursor:pointer;
	float:left;
	height:34px;
	}
.Remark_button2:hover{
	background-color:#007af5;
	color:#fff;
	border-radius:0px 4px 4px 0px;
	}
.Remark_button4{
	padding:0 15px;
	cursor:pointer;
	float:left;
	color:#ffffff;
	height:34px;
	}
.Remark_button4:hover{
	background-color: rgba(255,255,255,0.3);
	color:#fff;
	border-radius:4px;
	}

.Remark_List{
	display:block;
	float:left;
	}
.Remark{
	width:21px;
	height:21px;
	display:block;
	float:left;
	margin:0 15px 0 15px;
	}
.Remark_Green{
	background-image:url(../images/Remark_icon_new.png);
	background-position:0 0;
	}
.Remark_Yellow{
	background-image:url(../images/Remark_icon_new.png);
	background-position:-21px 0;
	}
.Remark_Purple{
	background-image:url(../images/Remark_icon_new.png);
	background-position:-105px 0;
	}
.Remark_Orange{
	background-image:url(../images/Remark_icon_new.png);
	background-position:-105px 0;
	}
.Remark_Blue{
	background-image:url(../images/Remark_icon_new.png);
	background-position:-42px 0;
	}
.Remark_Gray{
	background-image:url(../images/Remark_icon_new.png);
	background-position:-63px 0;
	}
.Remark_Red{
	background-image:url(../images/Remark_icon_new.png);
	background-position:-84px 0;
	}
.Package_bottom{float:left; color:#FFF; height:30px;}

/*2018-08-24*/
.package_status{
	width:26px;
	height:26px;
	position:relative;
	margin:-12px 0 0 195px;
}
.Package_Green .package_status{
	background-image:url(../images/Package_status.png);
	background-position:0 0px;
}
.Package_Blue .package_status{
	background-image:url(../images/Package_status.png);
	background-position:0 -26px;
}
.Package_Red .package_status{
	background-image:url(../images/Package_status.png);
	background-position:0 -52px;
}

/*Graph2*/
.DR_Leftmenu_over_btn{
	/*border-radius: 15px 0 0 15px;
	-webkit-border-radius: 15px 0 0 15px;
	-moz-border-radius: 15px 0 0 15px;
	-o-border-radius: 15px 0 0 15px;*/
	}
.DR_Leftmenu_btn{
	/*border-radius: 15px 0 0 15px;
	-webkit-border-radius: 15px 0 0 15px;
	-moz-border-radius: 15px 0 0 15px;
	-o-border-radius: 15px 0 0 15px;*/
	}
.Disaster_Recovery_Right{
	/*background-image:url(../images/Disaster_Recovery_BG.png);*/
	}
.Disaster_Recovery_arrow{
	float:left;
	padding:150px 45px 0 45px;
	}
.DR_arrow_img{
	background-image:url(../images/Disaster_Recovery_arrow.png);
	width:18px;
	height:18px;
}
.Disaster_Recovery_One{
	width:310px;
/* 	max-height:525px;
	min-height:525px; */
	}
.Disaster_Recovery_Common{
	border:1px solid #b5b5b6;
	box-shadow:2px 2px 10px #161617;
	-webkit-box-shadow:2px 2px 10px #161617;
	-moz-box-shadow:2px 2px 10px #161617;
	-o-box-shadow:2px 2px 10px #161617;
	background-color: rgba(255,255,255,0.3);
	float:left;
	padding:10px;
	}
.Disaster_Recovery_Line{
	background-image:url(../images/Disaster_Recovery_line.png);
	width:100%;
	height:auto;
	float:left;
	padding:0 0 0 10px;
	}
.Size_public{
	border-radius:50px;
	-webkit-border-radius:50px;
	-moz-border-radius:50px;
	-o-border-radius:50px;
	width:30px;
	height:30px;
	line-height:17px;
	float:left;
	font-size:14px;
	text-align:center;
	}
.Size_public2{
	width:14px; 
	height:14px;
	float:left;
	margin:4px 0 0 0;
	}
.Start_point_circle{ 
	background-image:url(../images/Start_Point.png); 
	}
.Green_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #05c464;
  	color:#05c464;
	}
.Green_Point:hover{
	background:#05c464;
	color:#ffffff;
	cursor:pointer;
}
.Yellow_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #ffbe24;
  	color:#ffbe24;
	}
.Yellow_Point:hover{
	background:#ffbe24;
	color:#ffffff;
	cursor:pointer;
}
.Orange_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #fc6630;
  	color:#fc6630;
	}
.Orange_Point:hover{
	background:#fc6630;
	color:#ffffff;
	cursor:pointer;
}
.Red_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #ff001c;
  	color:#ff001c;
	}
.Red_Point:hover{
	background:#ff001c;
	color:#ffffff;
	cursor:pointer;
}
.Gray_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #bbbbbb;
  	color:#bbbbbb;
	}
.Gray_Point:hover{
	background:#bbbbbb;
	color:#ffffff;
	cursor:pointer;
}
.Purple_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #fd7241;
  	color:#fd7241;
	}
.Purple_Point:hover{
	background:#fd7241;
	color:#ffffff;
	cursor:pointer;
}
.Blue_Point{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:5px solid #409dfa;
  	color:#409dfa;
	}
.Blue_Point:hover{
	background:#409dfa;
	color:#ffffff;
	cursor:pointer;
}
.Disaster_Recovery_start{
	background-image:url(../images/Disaster_Recovery_start.png);
	width:89px;
	height:20px;
	line-height:20px;
	color:#fff;
	text-align:center;
	float:left;
	margin:-2px 0 0 10px;
	}
.Disaster_Recovery_end{
	background-image:url(../images/Disaster_Recovery_start.png);
	width:89px;
	height:20px;
	line-height:20px;
	color:#fff;
	text-align:center;
	float:left;
	margin:2px 0 0 24px;
	position:relative;
	}
.Disaster_Recovery_List{
	width:100%;
	height:auto;
	margin:0 0 0 0;
	padding:10px 10px;
	float:left;
	cursor:pointer;
	}
.Disaster_Recovery_List:hover{
	background-color:#ffffff;
}
.Disaster_Recovery_List2{
	width:100%;
	height:auto;
	/*margin:0 0 15px 0;*/
	float:left
	}
.Disaster_Recovery_List3{
	width:100%;
	height:auto;
	/*margin:5px 0 0 0;*/
	float:left
	}
.DR_Color_Common{
	width: 190px;
    height: 35px;
	line-height:35px;
	position: relative;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
	-o-border-radius: 4px;
	-ms-border-radius: 4px;
    border-radius: 4px;
	float:left;
	margin:-5px 0 0 5px;
	color:#4d4d4d;
	background-color:transparent;
	cursor:pointer;
	}
.DR_Color_Common:hover{
	background-color: transparent
}
.DR_Color_Common:before{
	/*content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 10px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #e6e6e6;
    border-bottom: 7px solid transparent;*/
}
.Disaster_Recovery_Green{
    background:#0d9b3e;
	background-image: -webkit-linear-gradient(top, #47d283 0%, #2ab761 50%, #0d9b3e 100%);
  	background-image: -moz-linear-gradient(top, #47d283 0%, #2ab761 50%, #0d9b3e 100%);
  	background-image: -o-linear-gradient(top, #47d283 0%, #2ab761 50%, #0d9b3e 100%);
  	background-image: linear-gradient(to bottom, #47d283 0%, #2ab761 50%, #0d9b3e 100%);
	}
.Disaster_Recovery_Green:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 7px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #0aa859;
    border-bottom: 7px solid transparent;
	}
.Disaster_Recovery_Red{
	background:#e11c1c;
	background-image: -webkit-linear-gradient(top, #f86d6d 0%, #ed4545 50%, #e11c1c 100%);
  	background-image: -moz-linear-gradient(top, #f86d6d 0%, #ed4545 50%, #e11c1c 100%);
  	background-image: -o-linear-gradient(top, #f86d6d 0%, #ed4545 50%, #e11c1c 100%);
  	background-image: linear-gradient(to bottom, #f86d6d 0%, #ed4545 50%, #e11c1c 100%);
	}
.Disaster_Recovery_Red:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 7px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #f15555;
    border-bottom: 7px solid transparent;
	}
.Disaster_Recovery_Yellow{
	background:#eb9205;
	background-image: -webkit-linear-gradient(top, #f6bb43 0%, #f1a724 50%, #eb9205 100%);
  	background-image: -moz-linear-gradient(top, #f6bb43 0%, #f1a724 50%, #eb9205 100%);
  	background-image: -o-linear-gradient(top, #f6bb43 0%, #f1a724 50%, #eb9205 100%);
  	background-image: linear-gradient(to bottom, #f6bb43 0%, #f1a724 50%, #eb9205 100%);
	}
.Disaster_Recovery_Yellow:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 7px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #f3af31;
    border-bottom: 7px solid transparent;
	}
.Disaster_Recovery_Blue{
	background:#0592e0;
	background-image: -webkit-linear-gradient(top, #71d1fa 0%, #3cb2ed 50%, #0592e0 100%);
  	background-image: -moz-linear-gradient(top, #71d1fa 0%, #3cb2ed 50%, #0592e0 100%);
  	background-image: -o-linear-gradient(top, #71d1fa 0%, #3cb2ed 50%, #0592e0 100%);
  	background-image: linear-gradient(to bottom, #71d1fa 0%, #3cb2ed 50%, #0592e0 100%);
	}
.Disaster_Recovery_Blue:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 7px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #52bff3;
    border-bottom: 7px solid transparent;
	}
.Disaster_Recovery_Purple{
	background:#9842b5;
	background-image: -webkit-linear-gradient(top, #cb73ef 0%, #b25bd2 50%, #9842b5 100%);
  	background-image: -moz-linear-gradient(top, #cb73ef 0%, #b25bd2 50%, #9842b5 100%);
  	background-image: -o-linear-gradient(top, #cb73ef 0%, #b25bd2 50%, #9842b5 100%);
  	background-image: linear-gradient(to bottom, #cb73ef 0%, #b25bd2 50%, #9842b5 100%);
	}
.Disaster_Recovery_Purple:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 7px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #be67e0;
    border-bottom: 7px solid transparent;
	}
.Disaster_Recovery_Gray{
	background:#788194;
	background-image: -webkit-linear-gradient(top, #b1bacc 0%, #959eb0 50%, #788194 100%);
  	background-image: -moz-linear-gradient(top, #b1bacc 0%, #959eb0 50%, #788194 100%);
  	background-image: -o-linear-gradient(top, #b1bacc 0%, #959eb0 50%, #788194 100%);
  	background-image: linear-gradient(to bottom, #b1bacc 0%, #959eb0 50%, #788194 100%);
	}
.Disaster_Recovery_Gray:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 7px;
    position: absolute;
    border-top: 7px solid transparent;
    border-right: 10px solid #a2abbd;
    border-bottom: 7px solid transparent;
	}
.Disaster_Recovery_Green:hover{
	background:#47d283;
	background-image: -webkit-linear-gradient(top, #0d9b3e 0%, #2ab761 50%, #47d283 100%);
  	background-image: -moz-linear-gradient(top, #0d9b3e 0%, #2ab761 50%, #47d283 100%);
  	background-image: -o-linear-gradient(top, #0d9b3e 0%, #2ab761 50%, #47d283 100%);
  	background-image: linear-gradient(to bottom, #0d9b3e 0%, #2ab761 50%, #47d283 100%);
	background-image: -ms-linear-gradient(to bottom, #0d9b3e 0%, #2ab761 50%, #47d283 100%);
	box-shadow:2px 2px 5px #373d48;
	-webkit-box-shadow:2px 2px 5px #373d48;
	-moz-box-shadow:2px 2px 5px #373d48;
	-o-box-shadow:2px 2px 5px #373d48;
	cursor:pointer;
	}
.Disaster_Recovery_Red:hover{
	background:#f86d6d;
	background-image: -webkit-linear-gradient(top, #e11c1c 0%, #ed4545 50%, #f86d6d 100%);
  	background-image: -moz-linear-gradient(top, #e11c1c 0%, #ed4545 50%, #f86d6d 100%);
  	background-image: -o-linear-gradient(top, #e11c1c 0%, #ed4545 50%, #f86d6d 100%);
  	background-image: linear-gradient(to bottom, #e11c1c 0%, #ed4545 50%, #f86d6d 100%);
	box-shadow:2px 2px 5px #373d48;
	-webkit-box-shadow:2px 2px 5px #373d48;
	-moz-box-shadow:2px 2px 5px #373d48;
	-o-box-shadow:2px 2px 5px #373d48;
	cursor:pointer;
	}
.Disaster_Recovery_Yellow:hover{
	background:#f6bb43;
	background-image: -webkit-linear-gradient(top, #eb9205 0%, #f1a724 50%, #f6bb43 100%);
  	background-image: -moz-linear-gradient(top, #eb9205 0%, #f1a724 50%, #f6bb43 100%);
  	background-image: -o-linear-gradient(top, #eb9205 0%, #f1a724 50%, #f6bb43 100%);
  	background-image: linear-gradient(to bottom, #eb9205 0%, #f1a724 50%, #f6bb43 100%);
	box-shadow:2px 2px 5px #373d48;
	-webkit-box-shadow:2px 2px 5px #373d48;
	-moz-box-shadow:2px 2px 5px #373d48;
	-o-box-shadow:2px 2px 5px #373d48;
	cursor:pointer;
	}
.Disaster_Recovery_Blue:hover{
	background:#71d1fa;
	background-image: -webkit-linear-gradient(top, #0592e0 0%, #3cb2ed 50%, #71d1fa 100%);
  	background-image: -moz-linear-gradient(top, #0592e0 0%, #3cb2ed 50%, #71d1fa 100%);
  	background-image: -o-linear-gradient(top, #0592e0 0%, #3cb2ed 50%, #71d1fa 100%);
  	background-image: linear-gradient(to bottom, #0592e0 0%, #3cb2ed 50%, #71d1fa 100%);
	box-shadow:2px 2px 5px #373d48;
	-webkit-box-shadow:2px 2px 5px #373d48;
	-moz-box-shadow:2px 2px 5px #373d48;
	-o-box-shadow:2px 2px 5px #373d48;
	cursor:pointer;
	}
.Disaster_Recovery_Purple:hover{
	background:#cb73ef;
	background-image: -webkit-linear-gradient(top, #9842b5 0%, #b25bd2 50%, #cb73ef 100%);
  	background-image: -moz-linear-gradient(top, #9842b5 0%, #b25bd2 50%, #cb73ef 100%);
  	background-image: -o-linear-gradient(top, #9842b5 0%, #b25bd2 50%, #cb73ef 100%);
  	background-image: linear-gradient(to bottom, #9842b5 0%, #b25bd2 50%, #cb73ef 100%);
	box-shadow:2px 2px 5px #373d48;
	-webkit-box-shadow:2px 2px 5px #373d48;
	-moz-box-shadow:2px 2px 5px #373d48;
	-o-box-shadow:2px 2px 5px #373d48;
	cursor:pointer;
	}
.Disaster_Recovery_Gray:hover{
	background:#b1bacc;
	background-image: -webkit-linear-gradient(top, #788194 0%, #959eb0 50%, #b1bacc 100%);
  	background-image: -moz-linear-gradient(top, #788194 0%, #959eb0 50%, #b1bacc 100%);
  	background-image: -o-linear-gradient(top, #788194 0%, #959eb0 50%, #b1bacc 100%);
  	background-image: linear-gradient(to bottom, #788194 0%, #959eb0 50%, #b1bacc 100%);
	box-shadow:2px 2px 10px #373d48;
	-webkit-box-shadow:2px 2px 10px #373d48;
	-moz-box-shadow:2px 2px 10px #373d48;
	-o-box-shadow:2px 2px 10px #373d48;
	cursor:pointer;
	}
.Disaster_Recovery_Info{ float:left}
.Disaster_Recovery_Info span{
	display:block;
	color:#46545d;
	line-height:17px;
	padding:0 10px;
	width:195px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size:14px;
	}
.Disaster_Recovery_Info span:nth-child(2n){
	color:#999999;
	font-size:12px;
}
.Disaster_Recovery_Num{
	background-image:url(../images/Disaster_Recovery_Num.png);
	width:23px;
	height:23px;
	float:left;
	line-height:22px;
	color:#373d48;
	text-align:center;
	margin:0 0 0 15px;
	}
.Disaster_Recovery_Two{
	width:310px;
	margin:0 0 20px 0;
/* 	max-height:500px;
	min-height:500px; */
	}
.DR_Two_Common{
	border-radius:4px;
	height:36px;
	line-height:36px;
	color:#4d4d4d;
	border:1px solid #dcdddd;
	width:290px;
	text-align:center;
	float:left;
	margin:1px 10px 17px 10px;
	padding:0 5px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	cursor:pointer;
	background-color:#ffffff;
	font-size:14px;
	}
.DR_Two_Green{
	border-left:4px solid #05c464;
	border-top:1px solid #05c464;
	border-right:1px solid #05c464;
	border-bottom:1px solid #05c464;
	color:#05c464;
	}
.DR_Two_Red{
	border-left:4px solid #ff001c;
	border-top:1px solid #ff001c;
	border-right:1px solid #ff001c;
	border-bottom:1px solid #ff001c;
	color:#ff001c;
	}
.DR_Two_Yellow{
	border-left:4px solid #ffbe24;
	border-top:1px solid #ffbe24;
	border-right:1px solid #ffbe24;
	border-bottom:1px solid #ffbe24;
	color:#ffbe24;
	}
.DR_Two_Orange{
	border-left:4px solid #fc6630;
	border-top:1px solid #fc6630;
	border-right:1px solid #fc6630;
	border-bottom:1px solid #fc6630;
	color:#fc6630;
	}
.DR_Two_Blue{
	border-left:4px solid #409dfa;
	border-top:1px solid #409dfa;
	border-right:1px solid #409dfa;
	border-bottom:1px solid #409dfa;
	color:#409dfa;
}
.DR_Two_Purple{
	border-left:4px solid #fd7241;
	border-top:1px solid #fd7241;
	border-right:1px solid #fd7241;
	border-bottom:1px solid #fd7241;
	color:#fd7241;
	}
.DR_Two_Gray{
	border-left:4px solid #bbbbbb;
	border-top:1px solid #bbbbbb;
	border-right:1px solid #bbbbbb;
	border-bottom:1px solid #bbbbbb;
	color:#bbbbbb;
	}
.Disaster_Recovery_Desc{
	padding:10px;
	border:1px solid #d9d9d9;
	background-color:#fff;
	color:#46545d;
	}
.Disaster_Recovery_Desc tr{ height:30px;}

/*Graph3*/
.Emergency_Bg{
	border-radius:8px;
	-webkit-border-radius:8px;
	-moz-border-radius:8px;
	-o-border-radius:8px;
	border:0px solid #ffffff;
	background-position: center;
	width:100%;
	height:100%;
	font-family:"Microsoft YaHei";
	font-size:12px;
	background-color:transparent;
	}
.Emergency_Body{ 
	width:330px;
	height:auto;
	color:#4d4d4d;
	margin:8px 0;
	float:left;
	}
.Emergency_space{
	margin:0 10px;
}
.Emergency_Color{
	width:240px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size:14px;
}
.Emergency_top{ 
	background-image:url(../images/Emergency_top.png); 
	width:330px; 
	height:58px;
	float:left;
	}
.Emergency_top span{ 
	padding:15px 0 0 15px; 
	display:block;
	font-size:16px;
	color:#46545d;
	font-weight:bold;
	}
.Emergency_top span a{
	color:#4d4d4d;
	text-decoration:none;
	font-family:Microsoft Yahei , Arial ;
	margin:3px 0 0 0;
	display:block;
	}
.Emergency_middle{ 
	background-image:url(../images/Emergency_middle.png); 
	width:330px; 
	float:left;
	padding:10px 0 0 0;
	}
.Emergency_bottom{ 
	background-image:url(../images/Emergency_bottom.png); 
	width:330px; 
	height:25px;
	float:left;
	}
.Emergency_Body2{ 
	width:330px;
	height:auto;
	color:#46545d;
	margin:8px 10px;
	float:left;
	overflow:hidden;

	}	
.Emergency_top2{ 
	background-image:url(../images/Emergency_top.png); 
	width:330px; 
	height:58px;
	float:left;
	font-size:16px;
	font-weight:bold;
	}
.Emergency_top2 span{ 
	padding:10px 0 0 15px; 
	display:block;
	font-size:13px;
	}
.Emergency_top2 span a{
	color:#4d4d4d;
	text-decoration:none;
	font-family:Microsoft Yahei , Arial ;
	margin:3px 0 0 0;
	display:block;
	}
.Emergency_middle2{ 
	background-image:url(../images/Emergency_middle.png); 
	width:330px; 
	float:left;
	}
.Emergency_bottom2{ 
	background-image:url(../images/Emergency_bottom.png); 
	width:330px; 
	height:25px;
	float:left;
	}
	
.Emergency_arrow{
	float:left;
	margin:365px 0 0 0;
	background-image:url(../images/Disaster_Recovery_arrow.png); 
	width:60px;
	height:25px;
	position:relative;
	}
.arrow_line{
	/*width:32px;
	height:5px;
	margin:7px 0 0 0;
 	background-image:url(../images/line.png);
	position:absolute;
	animation:mymove 2s infinite;
	-moz-animation:mymove 2s infinite; 
	-webkit-animation:mymove 2s infinite; 
	-o-animation:mymove 2s infinite; */
}
.arrow_line2{
	/*width:32px;
	height:5px;
	margin:16px 0 0 0;
 	background-image:url(../images/line.png);
	position:absolute;
	animation:mymove2 2s infinite;
	-moz-animation:mymove2 2s infinite; 
	-webkit-animation:mymove2 2s infinite; 
	-o-animation:mymove2 2s infinite; */
}
@keyframes mymove
	{
	from {left:-5px;}
	to {left:34px;}
		}
@-moz-keyframes mymove
	{
	from {left:-5px;}
	to {left:34px;}
	}
	
@-webkit-keyframes mymove
	{
	from {left:-5px;}
	to {left:34px;}
	}
@-o-keyframes mymove
	{
	from {left:-5px;}
	to {left:34px;}
	}
	
@keyframes mymove2
	{
	from {left:-5px;}
	to {left:34px;}
		}
@-moz-keyframes mymove2
	{
	from {left:-5px;}
	to {left:34px;}
	}
	
@-webkit-keyframes mymove2
	{
	from {left:-5px;}
	to {left:34px;}
	}
@-o-keyframes mymove2
	{
	from {left:-5px;}
	to {left:34px;}
	}
.Emergency_Content{
	width:310px;
	}
.Emergency_Content2{
	width:100%;
	height:100%;
	}
.Emergency_scroll{
	margin:0 0 0 10px;
}
.Emergency_Content ul li{ 
	padding:0 10px; 
	height:36px; 
	line-height:36px;
	margin:0 0 5px 0;
	}
.Emergency_Content2 ul li{ 
	padding:0 10px; 
	height:36px; 
	line-height:36px;
	}

.Emergency_Btn_list{
	background-color:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border-top:0px solid #dcdddd;
  	border-bottom:0px solid #dcdddd;
  	font-size:14px;
}
.Emergency_hover_Btn{
	background-color:#e5f1fe;
	background-image: -webkit-linear-gradient(top, #e5f1fe 0%, #e5f1fe 100%);
  	background-image: -moz-linear-gradient(top, #e5f1fe 0%, #e5f1fe 100%);
  	background-image: -o-linear-gradient(top, #e5f1fe 0%, #e5f1fe 100%);
  	background-image: linear-gradient(to bottom, #e5f1fe 0%, #e5f1fe 100%);
	color:#46545d;
	cursor:pointer;
	}
.Em_overview{width:1110px; margin:auto;}
.List_Info{
	width:220px;
	float:left;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	cursor:pointer;
	margin:0 0 0 10px;
	}
.Emergency_Common_btn{
	border-radius: 4px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	-o-border-radius: 4px;
	color:#46545d;
	width:290px;
	margin:10px auto 0 auto;
	cursor:pointer;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	}
.Emergency_Common_icon{
	width:21px; 
	height:21px; 
	background-image:url(../images/Emergency_icon2.png);
	float:left;
	display:block;
	margin:6px 0 0 6px;
	}
.Emergency_Common_icon2{
	width:21px; 
	height:21px; 
	background-image:url(../images/Emergency_icon2.png);
	float:left;
	display:block;
	margin:0 10px;
	}
.Emergency_icon1{
	background-position:0 -21px;
	}
.Emergency_icon2{ 
	background-position:0 0;
	}
.Emergency_btn{
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
  	border:0px solid #dcdddd;
  	font-size:14px;
	}
.Emergency_over_btn{
	background:#e5f1fe;
	background-image: -webkit-linear-gradient(top, #e5f1fe 0%, #e5f1fe 100%);
  	background-image: -moz-linear-gradient(top, #e5f1fe 0%, #e5f1fe 100%);
  	background-image: -o-linear-gradient(top, #e5f1fe 0%, #e5f1fe 100%);
  	background-image: linear-gradient(to bottom, #e5f1fe 0%, #e5f1fe 100%);
  	border:0px solid #05c464;
  	color:#46545d;
	}
.Emergency_Line{
	background-image:url(../images/Emergency_Line.png);
	width:100%;
	height:auto;
	float:left;
	}
.Emergency_Common_SE{
	border-radius:15px;
	height:20px;
	line-height:20px;
	color:#fff;
	width:50px;
	background:#15b2f6;
	background-image: -webkit-linear-gradient(top, #3bc3ff 0%, #15b2f6 100%);
  	background-image: -moz-linear-gradient(top, #3bc3ff 0%, #15b2f6 100%);
  	background-image: -o-linear-gradient(top, #3bc3ff 0%, #15b2f6 100%);
  	background-image: linear-gradient(to bottom, #3bc3ff 0%, #15b2f6 100%);
	text-align:center;
	}
.Emergency_Start{
	margin:0 0 10px 6px;
	clear:both;
	}
.Emergency_Start2{
	clear:both;
	margin:0 0 0 -6px;
	}
.Emergency_End{
	margin:30px 0 0 7px;
	float:left;
	clear:both;
	}
.Emergency_List{
	clear:both;
	float:left;
	background-color:#fff;
	border-radius:4px;
	width:290px;
	margin:10px 0 0 10px;
	}
.Emergency_List:hover{
	background-color:#e5f1fe;
}
.Emergency_Circle{
	border-radius:50px;
	-webkit-border-radius:50px;
	-moz-border-radius:50px;
	-o-border-radius:50px;
	width:35px;
	height:35px;
	float:left;
	}
.Emergency_Cir_Green_Btn{
	background:#0cbf47;
	background-image: -webkit-linear-gradient(top, #13ea5a 0%, #0cbf47 100%);
  	background-image: -moz-linear-gradient(top, #13ea5a 0%, #0cbf47 100%);
  	background-image: -o-linear-gradient(top, #13ea5a 0%, #0cbf47 100%);
  	background-image: linear-gradient(to bottom, #13ea5a 0%, #0cbf47 100%);
  	border:1px solid #0cbf47;
	}
.Emergency_Cir_Red_Btn{
	background:#fa5d52;
	background-image: -webkit-linear-gradient(top, #ff918e 0%, #fa5d52 100%);
  	background-image: -moz-linear-gradient(top, #ff918e 0%, #fa5d52 100%);
  	background-image: -o-linear-gradient(top, #ff918e 0%, #fa5d52 100%);
  	background-image: linear-gradient(to bottom, #ff918e 0%, #fa5d52 100%);
  	border:1px solid #fa5d52;
	}
.Emergency_Cir_Yellow_Btn{
	background:#ffa808;
	background-image: -webkit-linear-gradient(top, #ffca6a 0%, #ffa808 100%);
  	background-image: -moz-linear-gradient(top, #ffca6a 0%, #ffa808 100%);
  	background-image: -o-linear-gradient(top, #ffca6a 0%, #ffa808 100%);
  	background-image: linear-gradient(to bottom, #ffca6a 0%, #ffa808 100%);
  	border:1px solid #ffa808;
	}
.Emergency_Cir_Blue_Btn{
	background:#13b1f5;
	background-image: -webkit-linear-gradient(top, #5fceff 0%, #13b1f5 100%);
  	background-image: -moz-linear-gradient(top, #5fceff 0%, #13b1f5 100%);
  	background-image: -o-linear-gradient(top, #5fceff 0%, #13b1f5 100%);
  	background-image: linear-gradient(to bottom, #5fceff 0%, #13b1f5 100%);
  	border:1px solid #13b1f5;
	}
.Emergency_Cir_Purple_Btn{
	background:#fc6630;
	background-image: -webkit-linear-gradient(top, #ffa503 0%, #fc6630 100%);
  	background-image: -moz-linear-gradient(top, #ffa503 0%, #fc6630 100%);
  	background-image: -o-linear-gradient(top, #ffa503 0%, #fc6630 100%);
  	background-image: linear-gradient(to bottom, #ffa503 0%, #fc6630 100%);
  	border:1px solid #fc6630;
	}
.Emergency_Cir_Gray_Btn{
    background:#a3a4a4;
	background-image: -webkit-linear-gradient(top, #dddede 0%, #a3a4a4 100%);
  	background-image: -moz-linear-gradient(top, #dddede 0%, #a3a4a4 100%);
  	background-image: -o-linear-gradient(top, #dddede 0%, #a3a4a4 100%);
  	background-image: linear-gradient(to bottom, #dddede 0%, #a3a4a4 100%);
  	border:1px solid #a3a4a4;
	}
	
.Emergency_C_info{
	width:auto;
    height: 36px;
	line-height:36px;
	position: relative;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
	float:left;
	color:#46545d;
	text-align:left;
	margin:0 0 0 10px;
	cursor:pointer;
}
/*.Emergency_C_info:hover{
	background-color: rgba(0,0,0,0.2);
}
.Emergency_C_info:before{
	content: "";
    width: 0;
    height: 0;
    right: 100%;
    top: 10px;
    position: absolute;
    border-top: 5px solid transparent;
    border-right: 10px solid #e6e6e6;
    border-bottom: 5px solid transparent;
}*/
.Emergency_Remark{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:0px solid #cccccc;
	padding:10px 15px 10px 0;
	margin:0 auto;
	color:#46545d;
	height:40px;
	background-color: transparent;
	float:left;
	}
.Emergency_Cir_Green_Btn:hover{
	background:#0cbf47;
	cursor:pointer;
	}
.Emergency_Cir_Red_Btn:hover{
	background:#fa5d52;
	cursor:pointer;
	}
.Emergency_Cir_Yellow_Btn:hover{
	background:#ffa808;
	cursor:pointer;
	}
.Emergency_Cir_Blue_Btn:hover{
	background:#13b1f5;
	cursor:pointer;
	}
.Emergency_Cir_Purple_Btn:hover{
	background:#fc6630;
	cursor:pointer;
	}
.Emergency_Cir_Gray_Btn:hover{
	background:#a3a4a4;
	cursor:pointer;
	}

/*闂佸搫顦弲婊呯矙閹寸媴鎷风憴鍕枙鐎殿喖鐖奸弫鎾绘晸閿燂拷*/
.progress{
  background: #a6a6a6;
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  border-radius: 15px;
  width:22px;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
 /* box-shadow: -3px -3px 3px #000000;
  -webkit-box-shadow: -3px -3px 3px #000000;
  -moz-box-shadow: -3px -3px 3px #000000;
  -o-box-shadow: -3px -3px 3px #000000;*/
  margin:10px 5px 0 5px;
  height:100%;
	}		
.Progress_Bar{
	width:22px;
	margin:0 0 0 -1px;
	height:100%;
	background:#0d84f6;
	background-image: -webkit-linear-gradient(top, #0d84f6 0%, #0d84f6 100%);
  	background-image: -moz-linear-gradient(top, #0d84f6 0%, #0d84f6 100%);
  	background-image: -o-linear-gradient(top, #0d84f6 0%, #0d84f6 100%);
  	background-image: linear-gradient(to bottom, #0d84f6 0%, #0d84f6 100%);
	border-radius: 15px;
}
.progress span{
	position:relative;
	transform: rotate(-180deg);
	-webkit-transform: rotate(-180deg);
	-moz-transform: rotate(-180deg);
	-o-transform: rotate(-180deg);
	color:#efefef;
	font-family:"Microsoft Yahei";
	font-size:11px;
	text-align:center;
	margin:0 0px 0 -2px;
	display:block;
	}
.Progress_Detail{
	font-family:"Microsoft Yahei";
	font-size:14px;
	}
.Progress_D_column{ width:auto; margin:15px 0 0 0;color:#666666;}
.Progress_circle1{
	background-color:#0d84f6;
	border-radius: 50%;
	width:10px;
	height:10px;
	display:block;
	float:left;
	margin:3px 5px 0 0;
	}
.Progress_circle2{
	background-color:#a6a6a6;
	border-radius: 50%;
	width:10px;
	height:10px;
	display:block;
	float:left;
	margin:3px 5px 0 0;
	}
.reportFont{color:#fff;}

.dbsourBtn{
	border-radius:2px;
	background-color:#007af5;
	border:1px solid #007af5;
	color:#ffffff;
	line-height:18px;
	cursor:pointer;
	font-size:12px;
}
.online_grid .x-grid-cell-inner {
	text-overflow: ellipsis;
	padding: 5px 10px 5px 10px
}
.monitor_search{
	background-image: url(../images/monitor_search.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_termination{
	background-image: url(../images/monitor_termination.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_execute{
	background-image: url(../images/monitor_execute.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip{
	background-image: url(../images/monitor_skip.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_curve{
	background-image: url(../images/monitor_curve.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_download{
	background-image: url(../images/monitor_download.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.Manual_deployment{
	background-image: url(../images/Manual_deployment.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.Deployment_failure{
	background-image: url(../images/Deployment_failure.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.Deployment_Fail{
	color: #C31515;
	text-decoration: underline;
}
.no-icon {display:none;}

.result_report{
	background-image: url(../images/result_report.png);
	width:146px;
	height:108px;
	margin:0 auto;
}

.welcome-permission{
	background-image: url(../images/welcome-permission.png);
	width:161px;
	height:152px;
	margin:0 auto;
}

.monitor_systemIcon{
	background-image: url(../images/monitor_system.png);
	width:16px;
	height:16px;
}

.monitor_descIcon{
	background-image: url(../images/monitor_desc.png);
	width:16px;
	height:16px;
}

.role_permission{
	background-image: url(../images/permission.png);
	width:16px;
	height:16px;
}

/*.tp_logo{
	background-image: url(../images/tp_logo.png);
	width:200px;
	height:53px;
	}*/
.tooltip{
	background-color:#ffffff;
	color:#595757;
	border:1px solid #dcdddd;
	padding:5px 8px;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius:4px;
    -o-border-radius:4px;
	
}

/*.row_Red .x-grid-cell{
		color: #e73030;
	}
.row_Orage .x-grid-cell{
		color: #e17b0d;
	}
.row_Purple .x-grid-cell{
		color: #6541bf;
	}
	
.row_Green .x-grid-cell{
		color: #009944;
	}
	
.row_Blue .x-grid-cell{
		color: #0081cc;
	}
.row_Yellow .x-grid-cell{
		color: #e7cc0f;
	}
	
.row_Gray .x-grid-cell{
		color: #3e3a39;
	}*/
.row_Red_C .x-grid-cell{
		color: #c31515;
		font-weight:bold;
		text-shadow: 1px 1px 2px #c31515;
	}
.row_Orage_C .x-grid-cell{
		color: #e18f1a;
		font-weight:bold;
		text-shadow: 1px 1px 2px #e18f1a;
	}
.row_Purple_C .x-grid-cell{
		color: #bc82bb;
		font-weight:bold;
		text-shadow: 1px 1px 2px #bc82bb;
	}
	
.row_Green_C .x-grid-cell{
		color: #109450;
		font-weight:bold;
		text-shadow: 1px 1px 2px #109450;
	}
	
.row_Blue_C .x-grid-cell{
		color: #297ae9;
		font-weight:bold;
		text-shadow: 1px 1px 2px #297ae9;
	}
	
.row_Gray_C .x-grid-cell{
		color: #898989;
		font-weight:bold;
		text-shadow: 1px 1px 2px #898989;
	}
.tree_panel{
	background-color:#363d47;
	/*background-image: url(../images/tree_panel.jpg);*/
	}
.Common_Btn5{
	background:#ffffff;
}
.Common_Btn5 .x-btn-default-toolbar-small .x-btn-split-right{
	background-image: url(../rewrite_images/button/default-toolbar-small-s-arrow02.png);
}
.Common_Btn5:hover{
	background:#282c35;
}
.customize_body .menu_item_body{
	background:#282c35;
	border-color:#282c35;
	top:0;
}
.customize_body .menu_item_body .x-menu-item-active{
	background-image: none;
	background-color: #f3f5f9;
	border-color: #f3f5f9
}
.customize_body .menu_item_body .x-menu-item-arrow {
	width: 12px;
	height: 12px;
	top: 17px;
	right: 17px;
	background-image: url(../images/menu-parent.png)
}
.customize_body .menu_item_body .x-menu-item-link{
	line-height: 46px;
	padding: 0 4px 0 37px;
	display: inline-block
}
.customize_body .menu_item_body .x-menu-item-icon {
	width: 16px;
	height: 16px;
	top: 16px;
	left: 15px;
	background-position: center center
}
.customize_body .menu_item_three{
	background:#f3f5f9;
	border-color:#f3f5f9;
}
.customize_body .menu_item_three .x-menu-item-active{
	background-image: none;
	background-color: #f3f5f9;
	border-color: #f3f5f9
}
.customize_body .menu_item_three .x-menu-icon-separator {
	left: 22px;
	border-left: solid 0px #e1e1e1;
	background-color: white;
	width: 0px
}
.customize_body .menu_item_three .x-menu-item-link{
	line-height: 46px;
	padding: 0 4px 0 12px;
	display: inline-block;
	border-bottom:1px solid #dddfed;
	width:160px;
}
.customize_body .menu_item_three .x-menu-item-active .x-menu-item-arrow {
	width: 12px;
	height: 12px;
	top: 17px;
	right: 10px;
	background-image: url(../images/menu-parent.png);
	background-position:0 -12px;
}
.customize_body .menu_item_three .x-menu-item-active a span font{
	color:#5168fc;
}
.customize_body .menu_item_three .x-menu-item-arrow {
	width: 12px;
	height: 12px;
	top: 17px;
	right: 10px;
	background-image: url(../images/menu-parent.png)
}
.customize_body .menu_item_three .x-menu-item-icon {
	width: 16px;
	height: 16px;
	top: 15px;
	left: 15px;
	background-position: center center;
	background-image: url(../images/menu-item-icon.png);
}
#menuposition{
	background-image: url(../images/menu-parent.png);
	background-repeat:no-repeat;
	width: 12px;
	height: 12px;
	margin:16px 9px 0 0;
}
.arrow_down{
	background-image: url(../images/arrow_down.png);
	width:16px;
	height:16px;
}
.arrow_up{
	background-image: url(../images/arrow_up.png);
	width:16px;
	height:16px;
}
.collapse{
	background-image: url(../images/collapse.gif);
	width:16px;
	height:16px;
}
.expand{
	background-image: url(../images/expand.gif);
	width:16px;
	height:16px;
}
.remind{
	background: #f57242;color:#fff;padding:2px 6px;border-radius:20px;
}
.completecss{
	background: #69b74e;color:#fff;padding:2px 6px;border-radius:20px;
}
.abnormal{
	background: #f63508;color:#fff;padding:2px 6px;border-radius:20px;
}
.skip_exception{
	background: #3c7bfe;color:#fff;padding:2px 6px;border-radius:20px;
}
.tp_border2 .bell3{
	background-image: url(../images/bell3.png);
	width:31px; 
	height:31px;
	margin-left:0px;
}
.tp_border2:hover .bell3{
	background-image:url(../images/bell3_hover.png); 
	width:31px; 
	height:31px;
	margin-left:0px;
}
.searchFdBg{
	background-image: url(../images/searchFdBg.png);
	width:16px;
	height:16px;
}
.property{
	background-image: url(../images/property.png);
	width:16px;
	height:16px;
}
.equipment{
	background-image: url(../images/equipment.png);
	width:16px;
	height:16px;
}
.inspection_time{
	background-image: url(../images/inspection_time.png);
	width:16px;
	height:16px;
}
.snapshot{
	background-image: url(../images/snapshot.png);
	width:16px;
	height:16px;
}
.calendar{
	background-image: url(../images/calendar.png);
	width:16px;
	height:16px;
}
.normal{
	background-image: url(../images/normal.png);
	width:20px;
	height:20px;
}
.detect{
	background-image: url(../images/detect.png);
	width:20px;
	height:20px;
}
.nocheck{
	background-image: url(../images/nocheck.png);
	width:20px;
	height:20px;
}
.check_termination{
	background-image: url(../images/check_termination.png);
	width:20px;
	height:20px;
}
.green_light{
	background-image: url(../images/green_light.png);
	width:20px;
	height:20px;
}
.blue_light{
	background-image: url(../images/blue_light.png);
	width:20px;
	height:20px;
}
.purple_light{
	background-image: url(../images/purple_light.png);
	width:20px;
	height:20px;
}
.orange_light{
	background-image: url(../images/orange_light.png);
	width:20px;
	height:20px;
}
.red_light{
	background-image: url(../images/red_light.png);
	width:20px;
	height:20px;
}
.ready{
	background-image: url(../images/ready.png);
	width:20px;
	height:20px;
}
.running{
	background-image: url(../images/running.png);
	width:21px;
	height:21px;
}
.fail{
	background-image: url(../images/fail.png);
	width:20px;
	height:20px;
}
.manual{
	background-image: url(../images/manual.png);
	width:20px;
	height:20px;
}
.normal_common{
	/*width:18px;
	height:18px;*/
	margin:0 6px;
}
.export{
	background-image: url(../images/export.png);
	width:18px;
	height:18px;
}
.mail{
	background-image: url(../images/mail.png);
	width:16px;
	height:16px;
}
.add{
	background-image: url(../images/add.png);
	width:16px;
	height:16px;
}
.page_copy{
	background-image: url(../images/page_copy.png);
	width:18px;
	height:18px;
}
.save{
	background-image: url(../images/save.png);
	width:16px;
	height:16px;
}
.delete{
	background-image: url(../images/delete.png);
	width:16px;
	height:16px;
}
.group{
	background-image: url(../images/group.png);
	width:16px;
	height:16px;
}
.inherit{
	background-image: url(../images/inherit.png);
	width:18px;
	height:18px;
}
.import{
	background-image: url(../images/import.png);
	width:18px;
	height:18px;
}
.server_export{
	/*background-image: url(../images/server_export.png);*/
	width:18px;
	height:18px;
}
.arrow_undo{
	background-image: url(../images/arrow_undo.png);
	width:16px;
	height:16px;
}
.confirm{
	background-image: url(../images/confirm.png);
	width:18px;
	height:18px;
}
.acquiretask{
	background-image: url(../images/acquiretask.png);
	width:16px;
	height:16px;
}
.server_copy{
	background-image: url(../images/server_copy.png);
	width:18px;
	height:18px;
}
.script_save{
	background-image: url(../images/script_save.png);
	width:18px;
	height:18px;
}
.script_test{
	background-image: url(../images/script_test.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.script_download{
	background-image: url(../images/script_download.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.clean{
	background-image: url(../images/clean.png);
	width:18px;
	height:18px;
}
.import_configure{
	background-image: url(../images/import_configure.png);
	width:18px;
	height:18px;
}
.export_configure{
	background-image: url(../images/export_configure.png);
	width:18px;
	height:18px;
}
.x-btn-inner u{color:#595757;text-decoration:none;}

.uploadify {
	position: relative;
	margin-bottom: 1em;
}
.uploadify-button {
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px;
	-webkit-box-shadow:inset 0 0 0 #fff;  
	-moz-box-shadow:inset 0 0 0 #fff;  
	box-shadow:inset 0 0 0 #fff;   
	text-align:center;
	font-size:12px;
	height:34px;
	line-height:34px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:0 0 0 5px;
}
.uploadify:hover .uploadify-button {
	background-color:#0067ce;
	background-image: -webkit-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -moz-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: -o-linear-gradient(top, #0067ce 0%, #0067ce 100%);
  	background-image: linear-gradient(to bottom, #0067ce 0%, #0067ce 100%);
  	border:1px solid #0067ce;
}
.uploadify-button.disabled {
	background-color: #bfbfbf;
	color: #ffffff;
}
.uploadify-queue {
	margin-bottom: 1em;
}
.uploadify-queue-item {
	background-color: #F5F5F5;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	font: 11px Verdana, Geneva, sans-serif;
	margin-top: 5px;
	max-width: 350px;
	padding: 10px;
}
.uploadify-error {
	background-color: #FDE5DD !important;
}
.uploadify-queue-item .cancel a {
	background: url('../img/uploadify-cancel.png') 0 0 no-repeat;
	float: right;
	height:	16px;
	text-indent: -9999px;
	width: 16px;
}
.uploadify-queue-item.completed {
	background-color: #E5E5E5;
}
.uploadify-progress {
	background-color: #E5E5E5;
	margin-top: 10px;
	width: 100%;
}
.uploadify-progress-bar {
	background-color: #0099FF;
	height: 3px;
	width: 1px;
}
.bottom_color_common{
	width:10px;
	height:10px;
	background-repeat:no-repeat;
	margin:0 5px;
}
.bottom_green{
	background-image: url(../images/green.png);
}
.bottom_orange{
	background-image: url(../images/orange.png);
}
.bottom_purple{
	background-image: url(../images/purple.png);
}
.bottom_blue{
	background-image: url(../images/blue.png);
}
.bottom_gray{
	background-image: url(../images/gray.png);
}
.bottom_red{
	background-image: url(../images/red.png);
}
.reportA{
	color:#595757;
	text-decoration:none
}
.Menu_List{ 
	padding:0; 
	margin:0;
	list-style:none; 
	background-color:#ffffff;
	}
.Menu_List li{ 
	height:40px; 
	line-height:40px; 
	width:100%;
	cursor:pointer;
	color:#585a69;
	font-size:14px;
	padding:0 0 0 15px;
	}
.Menu_List li:hover{
	background-color: #f3f5f9;
	color:#5168fc;
}
.Menu_List li:hover #menuposition{
	background-image: url(../images/menu-parent.png);
	background-repeat:no-repeat;
	width: 12px;
	height: 12px;
	margin:16px 9px 0 0;
	background-position:0 -12px;
}
.Menu_L_Hover{
	background:#f3f5f9;
	background-image: -webkit-linear-gradient(top, #f3f5f9  0%,#f3f5f9 100%);
  	background-image: -moz-linear-gradient(top, #f3f5f9  0%, #f3f5f9 100%);
  	background-image: -o-linear-gradient(top, #f3f5f9  0%, #f3f5f9 100%);
  	background-image: linear-gradient(to bottom, #f3f5f9  0%, #f3f5f9 100%);
  	color:#ffffff;
	}
.Menu_L_Hover .Menu_Span2{
	color:#5168fc;
}
.Menu_L_Hover #menuposition{
	background-image: url(../images/menu-parent.png);
	background-repeat:no-repeat;
	width: 12px;
	height: 12px;
	margin:16px 9px 0 0;
	background-position:0 -12px;
}
.Menu_Span1{
	background-image:url(../images/Menu_Span1.png);
	width:20px;
	height:20px;
	display:block;
	float:left;
	margin:10px 5px 0 0;
}
.Menu_Span2{
	float:left;
	display:block;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}
.Menu_First_Icon{
	background-image:url(../images/Menu_First_Icon.png);
	width:16px;
	height:16px;
}
.tp_select{
	border:0px solid #a6a6a6;
	border-radius:2px;
	color:#5168fc;
	background-color:#ffffff;
	appearance:none;
  	-moz-appearance:none;
 	-webkit-appearance:none;
  	background: url("../images/red_arrow.png") no-repeat scroll right center; 
	padding-right: 14px;
	width:60px;
	font-family:Microsoft YaHei;
	padding:0 14px 0 3px;
}
.tp_select::-ms-expand { display: none;}
.tp_select option{ 
	background:#ffffff;
    color:#2b2b2b;
} 
.tp_select option:hover{
	background:#fdcdc9;
    color:#2b2b2b; 
} 
.tp_select option:checked{ 
    background:#fa594d; 
    color:#ffffff;  
}
.Overview_table{ 
	width:100%; 
	font-family:Microsoft Yahei
	}
.Overview_table_td{
	padding:0 20px 0 0;
}
.Script{ 
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.Release.color-1{ 
	background: url(../images/sc_back_img.png) no-repeat right, linear-gradient(left,#27d7b0,#3c60e8);
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#27d7b0,#3c60e8);
	background: url(../images/sc_back_img.png) no-repeat right, -moz-linear-gradient(left,#27d7b0,#3c60e8);
	background-color:#7184fb;
	}
.Test.color-2{ 
	background: url(../images/sc_back_img.png) no-repeat right, linear-gradient(left,#63e3fe,#3c60e8);
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#63e3fe,#3c60e8);
	background: url(../images/sc_back_img.png) no-repeat right, -moz-linear-gradient(left,#63e3fe,#3c60e8);
	background-color:#7184fb;
	}
.Test.color-3{ 
	background: url(../images/sc_back_img.png) no-repeat right, linear-gradient(left,#5b85fd,#5406d4);
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#5406d4);
	background: url(../images/sc_back_img.png) no-repeat right, -moz-linear-gradient(left,#5b85fd,#5406d4);
	background-color:#7184fb;
	}
.Not_released.color-4{ 
	background: url(../images/sc_back_img.png) no-repeat right, linear-gradient(left,#5b85fd,#5406d4);
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#5406d4);
	background: url(../images/sc_back_img.png) no-repeat right, -moz-linear-gradient(left,#5b85fd,#5406d4);
	background-color:#7184fb;
	}			
.Script_img{ 
	background-image:url(../images/Script.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.Color_Common{ 
	margin:0 auto;
	padding:0 0 0 15px;
	}
.Script_font{ 
	text-align:left; 
	font-size:34px;
	font-weight:bold;
	float:left;
	margin:35px 0 0 0;
	line-height:1;
	}
.Script_special{ 
	font-size:13px; 
	display:block;
	font-weight:normal;
	opacity:0.6;
	}
.utility_line{
	width:1px;
	background-color:#ffffff;
	float:left;
	height:40px;
	margin:40px 10px 0 0;
}
	
.Release{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.Release_img{
	background-image:url(../images/Release.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.Test{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.Test_img{
	background-image:url(../images/Test.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.Test_img.portal_Test_img{background-image:url(../images/portal_Test.png); }
.Not_released{
	background: url(../images/release_back_img.png) no-repeat right, -webkit-linear-gradient(left,#fe8a7c,#ec4b59);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.Not_released_img{
	background-image:url(../images/Not_released.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
	
.ReservePlanNum{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#fe8a7c,#ec4b59);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.ReservePlanNum_img{
	background-image:url(../images/reservePlanNum.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
	
.ResourceNum{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#fe8a7c,#ec4b59);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.ResourceNum_img{
	background-image:url(../images/resourceNum.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
	
.ResourceNum{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#fe8a7c,#ec4b59);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.ResourceNum_img{
	background-image:url(../images/resourceNum.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
	
.ScriptNum{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#fe8a7c,#ec4b59);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.ScriptNum_img{
	background-image:url(../images/scriptNum.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
	
.WaitScriptNum{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#fe8a7c,#ec4b59);
	background-color:#7184fb;
	height:120px; 
	border-radius:4px; 
	width:100%; 
	color:#fff;
	}
.WaitScriptNum_img{
	background-image:url(../images/waitScriptNum.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}

.yybs_circle{position:relative;font-size:14px;}
.yysb_circle_cn{width:100%;position:absolute;text-align:left;margin:0 0 0 20px;color:#929ea8;}
.yysb_circle_cn2{width:100%;position:absolute;text-align:center;margin:78px 0 0 0;color:#616f78}
.yybs_blue{color:#13b0f5;}
.yybs_green{color:#03db4a}
.yybs_red{color:#fd8382}
.yybs_gray{color:#888888}
.zzdd_Fonts{width:100%;text-align: center;color:#929ea8;font-size:12px;}
.State_Color{
	display:block;
	border-radius:20px;
	padding:0 0;
	font-size:14px;
	font-family:"Microsoft YaHei";
	float:left;
	width:auto;
	text-align:center;
	background:transparent;
	}
.Red_color{
	color:#ff001c;
}
.Green_color{
	color:#05c464;
}
.Green_color2{
	color:#05c464;
}
.Green_color3{
	color:#05c464;
}
.Green_color4{
	color:#05c464;
}
.Blue_color{
	color:#409dfa;
}
.Blue_color2{
	color:#409dfa;
}
.Gray_color{
	color:#444444;
}
.Gray_color2{
	color:#bbbbbb;
}
.yellow_color{
	color:#ffbe24;
}
.yellow_color2{
	color:#ffbe24;
}
.orange_color{
	color:#fc6630;
}
.orange_color2{
	color:#fc6630;
}
.purple_color{
	color:#fc6630;
}

.equipment_name{
	width:46%;
	height:38px;
	line-height:38px;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:14px;
	color:#6a6a6a;
	float:left;
	margin:10px 10px 10px 10px;
	background-color:#fafafa
	}
.equipment_name span{
	display:block;
	}
.equipment_name span a{
	color:#6a6a6a;
}


.equipment_new_monitor{
	width:31%; 
	/* width:300px; */
	height:111px;
	line-height:18px;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:14px;
	color:#6a6a6a;
	float:left;
	margin:10px 10px 10px 10px;
	background-color:#fafafa
	}
.equipment_new_monitor span{
	display:block;
	}
.equipment_new_monitor span a{
	color:#6a6a6a;
}


.equipment_new_monitor_dynamic{
	width:23%; 
	height:111px;
	line-height:18px;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:14px;
	color:#6a6a6a;
	float:left;
	margin:10px 10px 10px 16px;
	background-color:#fafafa
	}
.equipment_new_monitor_dynamic span{
	display:block;
	}
.equipment_new_monitor_dynamic span a{
	color:#6a6a6a;
}


.ement_status_new_monitor{
	width:38px;
	height:38px;
	float:left;
	margin:10px 10px 0 20px;
	position:absolute;
	margin-left:220px;
	margin-top:50px;
	}
.ement_list_icon{
	width:18px;
	position:absolute;
	height:18px;
	background-size:100% 100%;
}
.ement_new_blue{
	background-image: url(../images/newblue.png);
		}
.ement_new_green{
	background-image: url(../images/newgreen.png);
		}
.ement_new_red{
	background-image: url(../images/newred.png);
	}
.ement_new_grey{
	background-image: url(../images/newgrey.png);
	}
.ement_status{
	width:16px;
	height:16px;
	float:left;
	margin:10px 10px 0 20px;
	}
.ement_blue{
	background-image: url(../images/ement_blue.png);
		}
.ement_green{
	background-image: url(../images/ement_green.png);
		}
.ement_orange{
	background-image: url(../images/ement_orange.png);
		}
.ement_red{
	background-image: url(../images/ement_red.png);
	}
.snapshot_level{
	background-image: url(../images/snapshot_level.png);
	width:16px;
	height:16px;
}	
/*xunjiankanban*/
.inspection {
	width: 100%;
	height: 120px;
	font-family: Microsoft Yahei;
}
.inspection tr td {
	width: 25%;
}
.inspection_01 {
	border: 0px solid #e6e6e6;
	width: 100%;
	background-color:#ffffff;
	height: 120px;
	border-radius:4px;
	padding:18px 0 0 0;
}
.intion_part1 {
	width: 100%;
}
.intion_partcmon {
	width: 100%;
	text-align: center;
	font-size: 12px;
	color: #666666;
}
.intion_part2 {
	background-color: transparent;
}
.intion_part3 {
	background-color: transparent;
}
.intion_part4 {
	background-color: transparent;
}
.intion_part5 {
	background-color: transparent;
}
.intion_ic {
	background-image: url(../images/inspection_icon02.png);
	width: 30px;
	height: 30px;
	margin:0 auto;
	overflow: hidden;
}
.intion_icon1 {
	background-position: 0 0;
}
.intion_icon2 {
	background-position: 0 -30px;
}
.intion_icon3 {
	background-position: 0 -60px;
}
.intion_icon4 {
	background-position: 0 -90px;
}
.intion_icon5 {
	background-position: 0 -120px;
}
.intion_text {
	font-size: 30px;
	color: #2d3039;
	font-weight:bold;
	text-align:center;
}
/*2018-07-26*/
.inspection_overview2{
	width: 100%;
	height: 114px;
	background-color:transparent;
	font-family: Microsoft Yahei;
}
.inspection_overview2 tr td {
	width: 20%;
}
.inspection_td1{
	padding:0 20px 0 0;
}
.inspection_td2{
	padding:0 0 0 0;
}
.inspection_td3{
	padding:0 20px 0 0;
}
.intion_icon5 {
	background-position: 0 -120px;
}
.intion_icon6 {
	background-position: 0 -150px;
}
.intion_icon7 {
	background-position: 0 -180px;
}
.intion_icon8 {
	background-position: 0 -210px;
}
.intion_icon9 {
	background-position: 0 -240px;
}
.intion_icon10 {
	background-position: 0 -270px;
}
.intion_icon11 {
	background-position: 0 -300px;
}
.intion_icon12 {
	background-position: 0 -330px;
}
.intion_icon13 {
	background-position: 0 -360px;
}
.intion_icon14 {
	background-position: 0 -390px;
}
.intion_icon15 {
	background-position: 0 -420px;
}
.intion_part6 {
	background-color: transparent;
}
.intion_part7 {
	background-color: transparent;
}
.intion_part8 {
	background-color: transparent;
}
.intion_part9 {
	background-color: transparent;
}
.inspection_overview{
	width: 100%;
	height: 114px;
	font-family: Microsoft Yahei;
}
.inspection_overview tr td {
	width: 16.6%;
}

.menu_icon{
	border-radius:4px;
	padding:2px 0;
	width:20px;
	text-align:center;
	}

.monitor-wrap {
	width: 100%;
	height: auto;
	overflow: hidden;
	padding-right: 13px;
	box-sizing: border-box;
	color:#4d4d4d;
}

.monitor-item {
	width: 33.33333%;
	height: 180px;
	box-sizing: border-box;
	padding-left: 13px;
	float: left;
	margin-bottom: 20px;
	margin-top: 13px;
}

.monitor-item-wrap {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	border: 0px solid #dddddd;
	background-color:#fff;
	border-radius:4px;
}

.monitor-item-left {
	width: 168px;
	height: 100%;
	border-right: 1px solid #ebebeb;
	float: left;
}

.monitor-item-left-status {
	font-size: 13px;
	height:35px;
	padding-top: 18px;
	color: #05c464;
	font-weight:bold;
	width:95px;
	margin:auto;
	text-align:center;
}

.monitor-item-left-tip {
	display: block;
	width: 100%;
	padding-top: 8px;
	font-size: 13px;
	line-height: 14px;
	font-weight: bold;
	text-align: center;
	color: #9fa0a0;
}

.monitor-item-left-img-overall{
	width: 79px;
	height: 79px;
	position: relative;
	margin: 10px 0 7px 44px;
	}
.mon-left-img-common {
	width: 79px;
	height: 79px;
	border-radius: 100%;
	position:absolute;
	-moz-transition: all 2s ease;
	-webkit-transition: all 2s ease;
	transition: all 2s ease;
	-moz-animation: dotted-spin 2s linear infinite;
	-webkit-animation: dotted-spin 2s linear infinite;
	animation: dotted-spin 2s linear infinite;
}
.monitor-item-left-img-green{
	background: -moz-linear-gradient(top, #d2fee1 0%, #05c464 100%);
	background: -webkit-linear-gradient(top, #d2fee1 0%, #05c464 100%);
	background: linear-gradient(to bottom, #d2fee1 0%, #05c464 100%);
	}
.monitor-item-left-img-red{
	background: -moz-linear-gradient(top, #ffdee1 0%, #ef1023 100%);
	background: -webkit-linear-gradient(top, #ffdee1 0%, #ef1023 100%);
	background: linear-gradient(to bottom, #ffdee1 0%, #ef1023 100%);
	}
.monitor-item-left-img-yellow{
	background: -moz-linear-gradient(top, #fff3dd 0%, #ffa602 100%);
	background: -webkit-linear-gradient(top, #fff3dd 0%, #ffa602 100%);
	background: linear-gradient(to bottom, #fff3dd 0%, #ffa602 100%);
	}
.mon-left-img-inside {
	width: 71px;
	height: 71px;
	background-color: #fff;
	border-radius: 100%;
	position: relative;
	top:4px;
	z-index:1;
	margin:0 0 0 4px;
}
.mon-inside-image-green{
	background-image:url(../images/monitor-item-left-img-green.png);
	width:63px;
	height:63px;
	margin:0 auto;
	position:relative;
	top:3px;
	}
.mon-inside-image-red{
	background-image:url(../images/monitor-item-left-img-red.png);
	width:63px;
	height:63px;
	margin:0 auto;
	position:relative;
	top:3px;
	}
.mon-inside-image-yellow{
	background-image:url(../images/monitor-item-left-img-yellow.png);
	width:63px;
	height:63px;
	margin:0 auto;
	position:relative;
	top:3px;
	}
 @-webkit-keyframes dotted-spin {
 0% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
}
 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
 @-moz-keyframes dotted-spin {
 0% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
}
 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
 @keyframes dotted-spin {
 0% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
}
 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
.monitor-item-left-title {
	font-size: 13px;
	/*line-height: 14px;*/
	font-weight: 500;
	color: #2b2b2b;
	text-align: center;
}

.monitor-item-left-status .is-run {
	color: #05c464;
}

.monitor-item-left-status .is-error {
	color: #ef1023;
}

.monitor-item-left-status .is-next {
	color: #ffa602;
}

.monitor-item-right {
	width: 100%;
	height: 100%;
}

.monitor-item-right-line {
	line-height: 15px;
	padding-top: 17px;
	font-size: 13px;
	display: flex;
}

.monitor-item-right-icon {
	width: 14px;
	height: 14px;
	float: left;
	background-image:url(../images/monitor-item-right-icon.png);
	margin: 0 7px;
}
.monitor-item-left-msg{
	width:72px;
	float:left;
}
.monitor-item-right-msg{
	width:120px;
	float:left;
	white-space:nowrap;
	overflow:hidden;
	text-overflow: ellipsis; 
}
.monitor-item-bg {
	display: none;
}
.monitor_status{
	display:block;
	float:left;
}
/*jiaobenshichang*/
.panel-body3 {
	color:#43515c;
	padding:5px;
	font-size:13px;
	height:68%;
}
.tabs {
  float: none;
  list-style: none;
  position: relative;
  margin:-33px 0 0 0;
}
.tabs li {
  float: left;
  display: block;
}
.tabs input[type="radio"] {
  position: absolute;
  display:none;
}
.tabs label {
  display: block;
  width:98px;
  height:28px;
  line-height:28px;
  text-align:center;
  font-size: 14px;
  font-weight:normal;
  color:#4d4d4d;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.tabs label:hover {
  background: #e5e5e5;
}
.tabs .tab-content {
  z-index: 2;
  display: none;
  overflow: hidden;
  width: 100%;
  position: absolute;
  top: 38px;
  left: 0;
  height:auto;
}
.tabs.script-market .tab-content {
	top: 101px;
}
.tabs [id^="tab"]:checked + label {
  top: 0;
  width:98px;
  height:28px;
  line-height:28px;
  text-align:center;
  background: #ffffff;
  box-shadow: inset 0px 2px 0px 0px #888888; 
  border-left:1px solid #c9c9c9;
  border-right:1px solid #c9c9c9;
  color:#666666;
  font-weight:bold;
}
.tabs [id^="tab"]:checked ~ [id^="tab-content"] {
  display: block;
}
/*commnet*/
.tabs_comment {
  float: none;
  list-style: none;
  position: relative;
  margin:0 0 0 0;
}
.tabs_comment li {
  float: left;
  display: block;
}
.tabs_comment input[type="radio"] {
  position: absolute;
  display:none;
}
.tabs_comment label {
  display: block;
  width:98px;
  height:28px;
  line-height:28px;
  text-align:center;
  font-size: 14px;
  font-weight:normal;
  color:#4d4d4d;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.tabs_comment label:hover {
  background: #e5e5e5;
}
.tabs_comment .tab-con-ment {
  z-index: 2;
  display: none;
  overflow: hidden;
  width: 100%;
  position: absolute;
  top: 35px;
  left: 0;
  height:auto;
}
.tabs_comment [id^="tab_com"]:checked + label {
  top: 0;
  width:98px;
  height:28px;
  line-height:28px;
  text-align:center;
  background: #ffffff;
  box-shadow: inset 0px 2px 0px 0px #888888; 
  border-left:1px solid #c9c9c9;
  border-right:1px solid #c9c9c9;
  color:#666666;
  font-weight:bold;
}
.tabs_comment [id^="tab_com"]:checked ~ [id^="tab-con-ment"] {
  display: block;
}
.market_comment{
	height:28px;
	border-bottom:1px solid #dcdddd;
}
.Description{
	padding:5px;
}
.Comment_input{
	padding:10px;
}
.Comment_Desc{
	background-color:#fff;
	border:1px solid #e6e6e6;
	border-radius:4px;
	-moz-border-radius:4px;
	-webkit-border-radius:4px;
	height:80px;
	width:100%;
	color:#727171;
}
.Comment_btn{
	float:right;
	margin:10px 0;
	width:55px;
}
.Comment_List1,.Comment_List2{
	height:auto;
	padding:5px;
	width:100%;
	clear:both;
}
.Commnet_info{
	width:100%;
}
.Comment_btn2{
	float:right;
	margin:7px 10px;
}
.Comment_btn2 img{
	padding:0 5px;
}
.Comment_List1:nth-of-type(odd) {
    background-color: #f2f2f3;
    color:#4d4d4d;
}
.Commnet_button{
	padding:5px 8px 4px 8px;
}
.List_tool{
	height:25px;
	line-height:25px;
	padding:0 5px;
	font-size:11px;
	color:#2b2b2b;
}
.List_left{
	width:50%;
	text-align:left;
	float:left;
	color:#727171;
}
.List_right{
	width:50%;
	text-align:right;
	float:left;
	color:#f01024;
}
.Comment_overview{
	overflow-y:auto;
	width:100%;
}
.Script_type{
	width:33%;
	float:left;
	padding: 5px 10px;
	color:#4d4d4d;
}
.Script_textF{
	line-height:18px;
	padding:0 0 0 10px;
	word-wrap:break-word;
	overflow:hidden;
	width:90%;
	text-align:left;
}
.shell,.bat,.perl,.python,.software,.general,.ssflow,.sql{
	width:24px;
	height:24px;
	background-image:url(../images/script_market_icon2.png);
}
.shell{
	background-position:0 0;
}
.bat{
	background-position:0 -24px;
}
.perl{
	background-position:0 -48px;
}
.python{
	background-position:0 -72px;
}
.ssflow{
	background-position:0 -96px;
}
.sql{
	background-position:0 -120px;
}
.scriptL_Search{
	border:1px solid #d9d9d9;
	border-radius:2px;
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	width:100%;
	height:36px;
}
.scriptL_SearchA{
	border-radius:0 4px 4px 0;
	-webkit-border-radius:0 4px 4px 0;
	-moz-border-radius:0 4px 4px 0;
	-o-border-radius:0 4px 4px 0;
	height:34px;
	width:34px;
	margin:0;
	float:right
}
.scriptL_Searchinput{
	border:0px solid #dcdddd;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	height:34px;
	color:#c1c1c1;
	float:left;
	width:85%;
	background-color:#fff;
	padding:0 0 0 10px;
	font-family:Microsoft Yahei;
	font-size:14px;
}
.Search_button{
	background-image:url(../images/MH_Search.png);
	width:34px;
	height:34px;
}
.Script_library{
	width:100%;
	color:#46545d;
	padding:10px 0 10px 10px;
	background-color:#fafafa;
	margin:0 0 4px 0;
}
.Script_library a{
	color:#46545d;
}
.Script_textF2{
	line-height:18px;
	padding:0 0 0 0;
	word-wrap:break-word;
	overflow:hidden;
	width:80%;
	text-align:left;
}
.Script_textF3{
	line-height:18px;
	padding:0 0 0 10px;
	word-wrap:break-word;
	overflow:hidden;
	width:60%;
	text-align:left;
}
.Sc_add1{
	background-image:url(../images/sc_add1.png);
	width:44px;
	height:44px;
	margin:0 0 10px 0;
	float:left;
}
.Add_script{
	display:block;
	margin:12px 0 0 10px;
	float:left;
}
.Script_market{
	width:100%;
}
.Script_market tr td{
	height:100%;
	width:16%;
}
.script_L_icon1{
	background-position:0 0;
}
.script_L_icon2{
	background-position:0 -50px;
}
.script_L_icon3{
	background-position:0 -100px;
}
.script_L_icon4{
	background-position:0 -150px;
}
.script_L_icon5{
	background-position:0 -200px;
}
.script_L_icon6{
	background-position:0 -250px;
}
.script_L_common{
	background-image:url(../images/script_market_icon.png);
	width:50px;
	height:50px;
	float:left;
	margin:35px 10px 0 10px;
}
.script_L_common2{
	background-image:url(../images/script_market_icon.png);
	width:24px;
	height:24px;
	float:left;
	margin:8px 0 0 0;
}
.script_R{
	float:left;
	font-size:16px;
	margin:28px 0 0 0;
}
.Overview_shell,.Overview_bat,.Overview_perl,.Overview_python,.Overview_color1,.Overview_color2{
	border-radius:4px;
	height:120px;
	color:#ffffff;
	cursor: pointer;
}
.Overview_shell{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364eeb);
	background-color:#7184fb;
	margin:0 5px 0 0;
}
.Overview_shell:hover{
	background-color:#eaf6fd;
}
.Overview_bat{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
	margin:0 5px 0 0;
}
.Overview_bat:hover{
	background-color:#eaf6fd;
}
.Overview_perl{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
	margin:0 5px 0 0;
}
.Overview_perl:hover{
	background-color:#eaf6fd;
}
.Overview_python{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
	margin:0 5px 0 0;
}
.Overview_python:hover{
	background-color:#eaf6fd;
}
.Overview_color1{
	background: url(../images/sc_back_img02.png) no-repeat right, -webkit-linear-gradient(left,#4ca3f9,#278cf0);
	background-color:#278cf0;
	margin:0 5px 0 0;
}
.Overview_color1:hover{
	background-color:#eaf6fd;
}
.Overview_color2{
	background: url(../images/sc_back_img.png) no-repeat right, -webkit-linear-gradient(left,#5b85fd,#364de9);
	background-color:#7184fb;
}
.Overview_color2:hover{
	background-color:#eaf6fd;
}
.Large_font{
	font-size:32px;
	display:block;
	color:#ffffff;
	font-family:Arial;
}
.normal_font{
	display:block;
	color:rgba(255,255,255,0.6);
}
.scriptL_SearchR{
	border:1px solid #c9c9c9;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	width:100%;
	height:28px;
	float:left;
	margin:5px 0;
}
.scriptL_SearchinputR{
	border:0px solid #dcdddd;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	height:26px;
	color:#9fa0a0;
	float:left;
	width:95%;
	background-color:#fff;
	padding:0 0 0 10px;
	font-family:Microsoft Yahei; 
	font-size:12px;
}
.script_select{
	-webkit-box-shadow:0 0 5px 0px #13b1f5;  
    -moz-box-shadow:0 0 5px 0px #13b1f5;  
    box-shadow:0 0 5px 0px #13b1f5;
    border:1px solid #59c8f8;
    background-color:#ffffff;
}
.info_tab{
	color:#4d4d4d;
	margin:0 0 0 0;
}
.info_tr{
	height:30px;
}
.info_tab tr td{
	padding:0 10px 0 0;
}
.info_tab tr span{
	font-weight:bold;
}
.info_image{
	background-image: url(../images/icon-info.png);
	width:32px;
	height:32px;
	margin:5px 5px 0 10px;
}

.market_content{
	border-radius:4px;
	border:1px solid #dcdddd;
	width:100%;
	height:100%;
	float:left;
	margin:0 0 5px 0;
}
.market_Cleft{
	width:25%;
	height:100%;
	border-right:1px solid #dcdddd;
	float:left;
}
.market_Cleft ul{
	padding:0;
	margin:0;
}
.market_Cleft ul li{
	height:auto;
	padding:0 10px;
	cursor:pointer;
}
.market_Cleft ul li.active .Script_library{
	background-color:#e0effe;
}
.market_Cleft ul li.active .Script_textF2{
	color:#46545d;
}
.market_Cright{
	width:75%;
	height:100%;
	float:left;
	color:#595757;
}
.market_CR_part1{
	width:100%;
	height:230px;
	border-bottom:1px solid #dcdddd;
}
.market_CR_part2{
	width:100%;
	height:auto;
	padding:5px 0 0 0;
	line-height:2;
}
.mar_CR_table{
	width:100%;
	margin:10px 0 0 0;
}
.mar_CR_info tr{
	height:22px;
}
.mar_CR_table .mar_td{
	text-align:right;
	color:#666666;
	padding:0 0;
}
.mar_line{
	background-color:#dcdddd;
	height:1px;
	width:100%;
	margin:10px 0;
}
.mar_content span{
	padding:0 10px;
	line-height:1.8;
	display:block;
}
.mar_desc{
	padding:0 10px 10px 10px;
}
.mar_dot{
	background-color:#13b1f5;
	width:1px;
}
.basic_Info .basic_td{
	padding:10px 0;
}
.Developers{
	background-image:url(../images/market_Cright.png);
	width:118px;
	height:80px;
	margin:0 10px;
}
.base_sc_border{
	width:600px;
	height:auto;
	background-color:#ffffff;
	border-top:4px solid #7dc349;
	border-left:1px solid #dcdddd;
	border-right:1px solid #dcdddd;
	border-bottom:1px solid #dcdddd;
	margin:auto;
	font-size:13px;
	color:#595757;
	padding:15px 0;
	-webkit-box-shadow: 3px 3px 3px #9fa0a0;
	-o-box-shadow: 3px 3px 3px #9fa0a0;  
 	-moz-box-shadow: 2px 2px 2px #9fa0a0;  
    box-shadow: 2px 2px 2px #9fa0a0; 
}
.base_table{ width:100%}
.base_table tr{height:30px;}
.base_table tr td{
	padding:10px 5px;
}
.base_td1{
	width:30%;
	text-align:right;
	font-weight:bold;
}
.base_button{
	border:0;
	font-family: "Microsoft Yahei";
	width:60px;
	margin:0 5px;
}
#backinfo{
	border:1px solid #dcdddd;
	width:98%;
	height:100px;
}
.viewBase_all{
	background-color:#efefef;
	height:100%;
}
.secondary_default{
	background-color:#272c35;
}
.secondary_select{
	color:#FFF;
	cursor:pointer;
	height:40px; 
	line-height:40px;
	width:100%;
	background:#f08300;
	background-image: -webkit-linear-gradient(top, #e4a617 0%, #f08300 100%);
  	background-image: -moz-linear-gradient(top, #e4a617 0%, #f08300 100%);
  	background-image: -o-linear-gradient(top, #e4a617 0%, #f08300 100%);
  	background-image: linear-gradient(to bottom, #e4a617 0%, #f08300 100%);
}
.attachments .uploadify {
	float: right;
	margin-right: 5px;
}
.attachments .uploadify-button{
	 background-image: url(../images/uploadify-add.png);
	 width:16px;
	 height:16px;
	 background-color:none;
	 border:0px;
}
.attachments .uploadify:hover .uploadify-button{
	 background-image: url(../images/uploadify-add-hover.png);
	 width:16px;
	 height:16px;
	 background-color:none;
	 border:0px;
}
.attachments .x-tool {
	display: none;
}
.collection-col {
    background-image: url(../images/collection_select.png);
    width: 19px;
    height: 19px;
}
.uncollection-col {
    background-image: url(../images/collection.png);
    width: 19px;
    height: 19px;
}
a.collection-col, a.uncollection-col {
	height: 19px;
	width: 19px;
	display: block;
}
.step-status {
	width: 20px;
    height: 20px;
    float: left;
    margin: 0 10px 0 5px;
}

.step-status.notrun {
	background-image: url(../images/mxgraphImages/step-notrun.png);
}
.step-status.running {
	background-image: url(../images/mxgraphImages/step-running.png);
}
.step-status.finish {
	background-image: url(../images/mxgraphImages/step-finish.png);
}
.step-status.fail-running {
	background-image: url(../images/mxgraphImages/step-fail-running.png);
}
.step-status.fail-finish {
	background-image: url(../images/mxgraphImages/step-fail-finish.png);
}
.step-status.kill {
	background-image: url(../images/mxgraphImages/step-kill.png);
}

.norowexpandblah  .x-grid-row-expander {
    visibility: hidden;
}
.MH_Search{
	margin:10px 0 5px 6px;
	border:1px solid #cccccc;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	width:230px;
	height:30px;
}
.MH_Search a{
	border-radius:0 4px 4px 0;
	-webkit-border-radius:0 4px 4px 0;
	-moz-border-radius:0 4px 4px 0;
	-o-border-radius:0 4px 4px 0;
	height:28px;
	width:28px;
	/*background-color:#13b0f5;*/
	float:left;
}
.MH_Search input{
	border:0px solid #c9caca;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	height:28px;
	color:#595757;
	width:200px;
	float:left;
	background-color:#fff;
}
.MH_Button{
	float:left;
	line-height:30px; 
	margin:0 0 0 10px;
	width:70px;
	}
	
.script_cancel{
	background-image: url(../images/script_cancel.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.script_time{
	background-image: url(../images/script_time.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.script_termina{
	background-image: url(../images/script_termina.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_search{
	background-image: url(../images/monitor_search.png);
	width:16px;
	height:16px;
	margin:-2px 5px 0 0;
}
.monitor_search_F{
	background-image: url(../images/monitor_search_F.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_termination{
	background-image: url(../images/monitor_termination.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.monitor_execute{
	background-image: url(../images/monitor_execute.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_execute_F{
	background-image: url(../images/monitor_execute_F.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_execute_foucs{
	background-image: url(../images/monitor_execute_foucs.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip{
	background-image: url(../images/monitor_skip.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip_F{
	background-image: url(../images/monitor_skip_F.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip_foucs{
	background-image: url(../images/monitor_skip_foucs.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.Manual_deployment{
	background-image: url(../images/Manual_deployment.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.Deployment_failure{
	background-image: url(../images/Deployment_failure.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}

.monitor_delete{
	background-image: url(../images/monitor_delete.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_export{
	background-image: url(../images/monitor_export.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_Enlarge{
	background-image: url(../images/monitor_Enlarge.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_version{
	background-image: url(../images/monitor_version.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}

.Stick_top{
	background-image: url(../images/Stick_top.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.execute{
	background-image: url(../images/execute.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_edit{
	background-image: url(../images/script_edit.png);
	width:16px;
	height:16px;
	margin:-2px 5px 0 0;
}
.script_set{
	background-image: url(../images/script_set.png);
	width:16px;
	height:16px;
	margin:-4px 5px 0 0;
}

.script_test{
	background-image: url(../images/script_test.png);
	width:16px;
	height:16px;
	margin:-4px 5px 0 0;
}
.script_tool{
	background-image: url(../images/script_tool.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.script_search{
	background-image: url(../images/script_search.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.script_task{
	background-image: url(../images/script_task.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_user{
	background-image: url(../images/script_user.png);
	width:16px;
	height:16px;
	margin:-4px 5px 0 0;
}
.script_template{
	background-image: url(../images/script_template.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_monitor{
	background-image: url(../images/script_monitor.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_text{
	background-image: url(../images/script_text.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_start{
	background-image: url(../images/script_start.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_end{
	background-image: url(../images/script_end.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_submit{
	background-image: url(../images/script_submit.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_bt_edit{
	background-image: url(../images/script_bt_edit.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_collect{
	background-image: url(../images/script_collect.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_download{
	background-image: url(../images/script_download.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_transport{
	background-image: url(../images/script_transport.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_copy{
	background-image: url(../images/script_copy.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.script_continue{
	background-image: url(../images/script_continue.png);
	width:16px;
	height:16px;
	margin:-3px 5px 0 0;
}
.Run_Green{
	color:#409dfa;
	}
.Complete_Green{
	color:#05c464;
	}
.Abnormal_yellow{
	color:#ff001c;
}
.Abnormal_Complete_purple{
	color:#409dfa;
	}
.Abnormal_Operation_orange{
	color:#fc6630;
	}
.Kill_red{
	color:#ff001c;
	}
.Not_running{
	color:#444444;
}
.Ignore{
	color:#409dfa;
}
.attachment_delete{
	background-image: url(../images/clean_btn.png);
	width:16px;
	height:16px;
}
.script_end{
	background-image: url(../images/script_end.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_search_F{
	background-image: url(../images/monitor_search_F.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_execute_F{
	background-image: url(../images/monitor_execute_F.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_execute_foucs{
	background-image: url(../images/monitor_execute_foucs.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip{
	background-image: url(../images/monitor_skip.png);
	width:16px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip_F{
	background-image: url(../images/monitor_skip_F.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.monitor_skip_foucs{
	background-image: url(../images/monitor_skip_foucs.png);
	width:50px;
	height:16px;
	margin:0 5px 0 0;
}
.review_font .x-form-display-field-body {
	vertical-align: middle
}

.system_intro{
	width:100%;
	font-family:Microsoft YaHei;
	color:#4d4d4d;
	}
.system_intro tr td{
	padding:5px;
	}
.system_intro tr td span{
	font-size:12px;
	}
.system_intro_common{
	background-image:url(../images/system_intro.png);
	width:30px;
	height:30px;
	margin:0 0 5px 0;
	}
.system_intro_icon{
	background-position:0 0;
	}
.system_intro_icon2{
	background-position:0 -30px;
	}
.system_intro_icon3{
	background-position:0 -60px;
	}
.system_intro_icon4{
	background-position:0 -90px;
	}
.system_intro_icon5{
	background-position:0 -120px;
	}
.system_intro_icon6{
	background-position:0 -150px;
	}
.system_intro_icon7{
	background-position:0 -180px;
}
.system_intro_icon8{
	background-position:0 -210px;
}
.system_intro_icon9{
	background-position:0 -240px;
}
.system_content{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	border:1px solid #dcdddd;
	height:36px;
	font-size:12px;
	padding:8px 10px;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
	width:100%;
	cursor:pointer;
	}
/*tree*/
.tree_grid .x-grid-cell-inner{
	text-overflow: ellipsis;
	padding: 5px 10px 4px 10px
}
/*Agent*/
.win_prompt{
    width: 580px;
    height: auto;
    overflow-y: auto;
    padding: 5px;
    font-size: 12px;
    color: #4d4d4d;
    font-family: "Microsoft Yahei";
}
.win_prompt p{
    padding: 0;
    margin: 0 0 5px 0;
    font-size: 13px;
}
.pro_ip{
	float:left;
	line-height:2;
	}
.pro_ip span{
	display:block;
	float:left;
	margin:0 10px 0 0;
	width:170px;
	}
	
.Graph_div{
	position:relative;
	overflow:auto;
	width:100%;
	height:100%;
	background:url('../images/grid.gif');
	border-left:0px solid #dddfeb;
	background-color:#ffffff;
}	
/*zhongyuankanban-zuoyetiaodu*/
.lkmonitor{
	width:100%;
	height:82px;
	margin:0 0 6px 0;
	}
.lkmonitor table{
	width:100%;
	color:#595757;
	font-family:Microsoft Yahei;
	font-size:13px;
	}
.lkmonitor table tr td{
	height:82px;
	padding:5px;
	}
.lkmonitor table tr .lkm_td1{
	width:16.6%;
}
.lkmonitor table tr .lkm_td2{
	width:17%;
}
.lkmonitorzy{
	width:100%;
	height:120px;
	margin:0 0 6px 0;
	}
.lkmonitorzy table{
	width:100%;
	color:#595757;
	font-family:Microsoft Yahei;
	font-size:13px;
	}
.lkmonitorzy table tr td{
	text-align:center;
	}
.lkmonitorzy table tr .lkm_td1{
	width:16.6%;
	padding:0 20px 0 0;
}
.lkmonitorzy table tr .lkm_td2{
	width:17%;
	padding:0 20px 0 0;
}
.lkmonitorzy table tr .lkm_td3{
	
}
.lkmzy_date{
	padding:0 0 0 20px;
	margin:0 0 0 0;
}
.lkm_center{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	border:0px solid #e6e6e6;
	background-color:#ffffff;
	width:100%;
	height:120px;
	padding:20px 0 0 0;
	float:left;
	}
.lkm_header{
	border-radius:2px 2px 0 0;
	-webkit-border-radius:2px 2px 0 0;
	-moz-border-radius:2px 2px 0 0;
	background-color:#f7f7f7;
	height:22px;
	padding:6px 0 0 0;
}
.lkm_header span{
	background-image:url(../images/lkm_header.png);
	width:44px;
	height:9px;
	display:block;
}
.lkm_headerzy{
	border-radius:2px 2px 0 0;
	-webkit-border-radius:2px 2px 0 0;
	-moz-border-radius:2px 2px 0 0;
	background-color:#efefef;
	color:#2b2b2b;
	font-size:14px;
	height:26px;
	line-height:26px;
}
.lkm_text{
	float:left;
	width:100%;
	text-align:center;
	}
.lkm_text span{
	display:block;
	}
.lkm_gray{
	font-size:30px;
	color:#2d3039;
	font-weight:bold;
	line-height:27px;
	font-family:Arial, Helvetica, sans-serif
	}
.lkm_character{
	font-size:12px;
	color:#666666;
}
.lkm_gray a{
	color:#4d4d4d;
	text-decoration:unline;
}
.lkm_common{
	width:30px;
	height:30px;
	margin:0 auto 10px auto;
	background-image:url(../images/lkmonitor_icon.png);
	background-position:0 0;
	}
.lkm_icon{
	background-position:0 0;
	}
.lkm_icon2{
	background-position:0 -30px;
	}
.lkm_icon3{
	background-position:0 -60px;
	}
.lkm_icon4{
	background-position:0 -90px;
	}
.lkm_icon5{
	background-position:0 -120px;
	}
.lkm_date{
	background-image:url(../images/lkmonitor_icon.png);
	background-position:0 -150px;
	width:30px;
	height:30px;
	margin:0 auto;
}
.lkCalendar{
	background-color:#54d27e;
	width:110px;
	height:34px;
	color:#fff;
	border:0;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	font-size:20px;
	text-align:center;
	letter-spacing:1px;
	-webkit-box-shadow:0 2px 0 #35b55f;  
    -moz-box-shadow:0 2px 0 #35b55f;  
    box-shadow:0 2px 0 #35b55f;
}
.lkCalendarzy{
	border:0;
	font-size:30px;
	width:190px;
	font-weight:bold;
	color:#2d3039;
	padding:0;
}
.zy_table .x-column-header{
	background-color: #ffffff
}
.zy_table .x-grid-cell-inner{
	padding: 5px 10px 4px 10px
}
.State_all{
	display:block;
	border-radius:20px;
	width:56px;
	height:20px;
	color:#fff;
	font-size:12px;
	font-family:"Microsoft YaHei";
	float:left;
	width:auto;
	text-align:center;
}
.State_not_finish{
	background-image: url(../images/State_not_finish.png);
	width:56px;
	height:21px;
	/*line-height:21px;*/
	color:#ffffff;
	text-align:center;
	display:block;
}
.yysb_circle_tab{position:absolute}
.yysb_circle_cn_progress{width:100%;text-align:center;color:#666666; font-size:16px;}
.tip_body{
    padding:0px;
    font-size: 14px;
    font-family:"Microsoft YaHei";
}
.tip_title{
    font-size: 16px;
    color: #323a3d;
    margin:7px 0 0 10px;
}
.tip_content{
    width: 100%;
    height:auto;
    background-color:#fff;
    border:1px solid #ebeff5;
}
.tip_state {
    width:100%;
    border-bottom: 1px solid #dddddd;
    padding:5px 0;
    float: left;
}
.state_hr{
    width:100%;
    clear: both;
}
.state_c{
    float: left;
    padding: 5px;
}
.state_c span{
    float: left;
    margin: 0 0 0 10px;
}
.state_text{
    text-align: right;
    width: 60px;
    font-size:16px;
}
.state_blue{
    display: block;
    padding:1px 5px;
    border-radius: 2px;
    color: #409dfa;
    font-weight:bold;
    font-size:18px;
}
.state_green{
    display: block;
    padding:1px 5px;
    border-radius: 2px;
    color: #05c464;
    font-weight:bold;
    font-size:18px;
}
.state_red{
    display: block;
    padding:1px 5px;
    border-radius: 2px;
    color: #ff001c;
    font-weight:bold;
    font-size:18px;
}
.state_gray{
    display: block;
    padding:1px 5px;
    border-radius: 2px;
    color: #bbbbbb;
    font-weight:bold;
    font-size:18px;
}
.tip_table{
    clear: both;
    padding:0 10px;
    height:458px;
    overflow-y:auto;
}
.tip_table p{
	padding:0;
	margin:5px 0 0 0;
}
.title_red{
    color: #ff001c;
    background-color:#fff3f1;
    font-size:16px;
}
.title_gray{
    color: #bbbbbb;
    background-color:#f5f5f5;
    font-size:16px;
}
.title_green{
    color: #05c464;
    background-color:#e9fbf7;
    font-size:16px;
}
.title_blue{
    color: #409dfa;
    background-color:#e5f2ff;
    font-size:16px;
}
.tip_table01{
    width: 100%;
    background-color: #fffffff;
    margin:6px 0;
    color:#929ea8;
}
.tip_table01 tr{
    background-color: #ffffff;
    height: 30px;
    font-weight:bold;
}
.tip_table01 tr td{
	width:50%;
	text-align:left;
}
.tip_table02{
    width: 100%;
    background-color: #fffffff;
    margin:6px 0;
    color:#929ea8;
}
.tip_table02 tr{
    background-color: #ffffff;
    height: 30px;
    font-weight:bold;
}
.tip_table02 tr td{
	width:33.33%;
	text-align:left;
}
.table01_tr1 td{
    text-align: center;
}
.table01_tr2 td{
	text-align:center
}
.tip_pos .x-tool-img{
	position:relative;
	left:-7px;
	top:3px;
}
/*calendarWarp*/
.ECalendarNoneSelect, .ECalendarNoneSelect input {
	-moz-user-select: -moz-none;
	-moz-user-select: none;
	-o-user-select: none;
	-khtml-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.calendarWarp {
	position: relative;
	display: inline-block;
	font-weight: 100;
	padding:0 0 0 0;
}
\ .calendarWarp .icon {
	position: absolute;
	right: 20px;
	top: 50%;
	margin-top: -9px
}

.calendarWarp h2 {
	margin: 0;
	font-weight: 400
}

.ECalendarBox {
	border: 1px solid #409dfa
}

.ECalendarBox div, .ECalendarBox li, .ECalendarBox ul {
	float: none;
	margin: 0;
	padding: 0;
	font-weight: 100
}

.ECalendarBox li {
	list-style-type: none
}

.ECalendarBox ol, .ECalendarBox ul {
	list-style: none
}

.ECalendarBox .ilasttext {
	cursor: pointer
}

.ECalendarBox {
	position: absolute;
	width: 290px;
	background: #fff;
	color: #fff;
	font-weight: 100;
	font-size: 12px;
	line-height: 1em;
	z-index:1000
}

.ECalendarBox .currentyear {
	position: relative
}

.ECalendarBox .currentyear:after {
	position: absolute;
	top: -1px;
	left: 35px;
	display: block;
	overflow: hidden;
	width: 8px;
	height: 8px;
	border: 1px solid #fff;
	border-top: 0;
	border-left: 0;
	content: "";
	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	transform: rotate(45deg)
}

.ECalendarBox ul:after {
	clear: both;
	display: block;
	visibility: hidden;
	height: 0;
	content: ""
}

.ECalendarBox ul {
	zoom: 1
}

.ECalendarBox li {
	float: left;
	text-align: center;
	font-size: 14px;
	cursor: pointer
}

.ECalendarBox .head {
	position: relative;
	border-style: solid;
	border-top-width: 3px;
	border-left-width: 10px;
	border-right-width: 10px;
	border-bottom-width: 0
}

.ECalendarBox ul.day {
	border: 10px solid #fff
}

.ECalendarBox .day li, .ECalendarBox ul.week li {
	width: 14.25%;
	height: 24px;
	line-height: 24px
}

.ECalendarBox ul.week {
	margin-top: 10px
}

.ECalendarBox ul.week li:hover {
	background: 0
}

.ECalendarBox .currentdate {
	padding: 4px 4px 0 4px;
	height: 42px;
	cursor: pointer
}

.ECalendarBox .currentdate h2 {
	margin-left: 2px;
	font-size: 34px;
	line-height: 1.2em
}

.ECalendarBox .imonth, .ECalendarBox .iyear {
	position: relative
}

.ECalendarBox h2 i {
	position: absolute;
	left: 0;
	z-index: 9;
	display: block;
	height: 26px;
	background: rgba(0, 0, 0, .2);
	line-height: 26px
}

.ECalendarBox i.a1 {
	top: 38px;
	opacity: .6
}

.ECalendarBox i.a2 {
	top: 64px;
	opacity: .4
}

.ECalendarBox i.a3 {
	top: 90px;
	opacity: .2
}

.ECalendarBox i.r1 {
	top: -20px;
	opacity: .6
}

.ECalendarBox i.r2 {
	top: -46px;
	opacity: .4
}

.ECalendarBox i.r3 {
	top: -72px;
	opacity: .2
}

.ECalendarBox .currentlastgo {
	position: relative;
	top: 5px;
	cursor: pointer
}

.ECalendarBox .currenttime {
	position: relative;
	padding: 6px;
	font-size: 14px
}

.ECalendarBox .currenttime .itime {
	position: relative;
	top: 1px;
	font-size: 16px
}

.ECalendarBox .currenttime .itimebox {
	margin-left: 10px
}

.ECalendarBox .currenttime li:hover {
	background: rgba(0, 0, 0, .1)
}

.ECalendarBox .oper {
	position: absolute;
	top: 11px;
	right: 4px
}

.ECalendarBox .oper li {
	margin-left: 5px;
	width: 28px;
	height: 28px;
	background: rgba(255, 255, 255, .1);
	line-height: 28px
}

.ECalendarBox .oper li:hover {
	background: 0;
	background: rgba(255, 255, 255, .3)
}

.ECalendarBox ul.day li {
	position: relative;
	overflow: hidden;
	height: 30px;
	color: #444;
	line-height: 30px
}

.ECalendarBox ul.day li::after {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 7;
	margin: -15px 0 0 -50px;
	width: 100px;
	height: 30px;
	color: #000;
	content: attr(data-settime);
	text-align: center;
	font-size: 20px;
	line-height: 30px
}

.ECalendarBox .at12, .ECalendarBox .at12::after, .ECalendarBox .at24,
	.ECalendarBox .at24::after {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 5;
	border: 1px solid rgba(0, 0, 0, .4);
	background: rgba(255, 255, 255, .9);
	box-shadow: 0 0 10px rgba(0, 0, 0, .2)
}

.ECalendarBox .at12 {
	margin: -61px 0 0 -61px;
	width: 120px;
	height: 120px;
	border-radius: 100px
}

.ECalendarBox .at12::after {
	margin: -101px 0 0 -101px;
	width: 200px;
	height: 200px;
	border-radius: 200px;
	content: " ";
	opacity: .2
}

.ECalendarBox .at24 {
	margin: -101px 0 0 -101px;
	width: 200px;
	height: 200px;
	border-radius: 200px
}

.ECalendarBox .at24::after {
	margin: -61px 0 0 -61px;
	width: 120px;
	height: 120px;
	border-radius: 120px;
	content: " ";
	opacity: .2
}

.ECalendarBox .pointer {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 6;
	margin: -60px 0 0 -60px;
	width: 120px;
	height: 120px
}

.ECalendarBox .at24 .pointer {
	margin: -100px 0 0 -100px;
	width: 200px;
	height: 200px
}

.ECalendarBox .pointer::before {
	position: absolute;
	top: 50%;
	right: 14px;
	left: 50%;
	height: 1px;
	background: rgba(0, 0, 0, .1);
	content: ""
}

.ECalendarBox .pointer::after {
	position: absolute;
	top: 50%;
	right: -10px;
	margin-top: -10px;
	width: 18px;
	height: 18px;
	border: 1px solid rgba(0, 0, 0, .4);
	border-radius: 30px;
	border-bottom-left-radius: 0;
	background: rgba(255, 255, 255, .8);
	content: "";
	transform: rotate(45deg)
}

.ECalendarBox li:hover {
	background: #f6f6f6
}

.ECalendarBox li.activ {
	color: #fff !important;
	background: rgba(0, 0, 0, .6)
}

.ECalendarBox li.other {
	background: #fff;
	color: #ddd !important
}

.ECalendarBox .select {
	overflow: hidden;
	margin: 0 -10px;
	height: 20px;
	height: 0;
	background: rgba(0, 0, 0, .1)
}

.ECalendarBox .select li {
	padding: 0 10px;
	color: rgba(255, 255, 255, .6);
	font-size: 12px;
	line-height: 20px;
	line-height: 20px
}

.ECalendarBox .select li:first-child {
	margin-left: 8px
}

.ECalendarBox .select li:hover {
	background: rgba(255, 255, 255, .1)
}

/*toolbar btn gray*/
.tool_menu_Btn{
	text-align:center;
	font-size:14px;
	color:#585a69;
	cursor:pointer;
	padding:7px;
	border:0;
	background:transparent;
	}
.tool_menubar .x-btn-default-toolbar-small .x-btn-arrow {
	background-image: url(../images/default-toolbar-small-arrow.png)
}
.tool_menubar .x-btn-default-toolbar-small-disabled{
	border-color: #bfbfbf;
	background-color: #bfbfbf;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #bfbfbf),
		color-stop(50%, #bfbfbf), color-stop(51%, #bfbfbf),
		color-stop(100%, #bfbfbf));
	background-image: -webkit-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: -moz-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: -o-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf)
}
.customize_body .tool_menubar .x-btn-default-toolbar-small .x-btn-inner {
	font-size: 14px;
	color:#ffffff;
}
.customize_body .tool_menubar .x-btn-default-toolbar-small-over .x-btn-inner{
	font-size: 14px;
	color:#ffffff;
}


/*xinbanjiankong*/
.index_body{
	margin-bottom:0; 
	margin-top:0; 
	margin-right:0; 
	margin-left:0;
	background-color:#e0e9f0;
	font-family:"Microsoft YaHei";
	font-size:14px;
	color:#585a69;
}
.index_user{
	color:#585a69;
}
.pandect_top{
	width:100%;
	height:60px;
	background:#ffffff;
	background-image: -webkit-linear-gradient(top, #ffffff 0%,#ffffff 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #ffffff 100%);
	-webkit-box-shadow: 0 0 5px #898989;  
 	-moz-box-shadow: 0 0 5px #898989;  
  	box-shadow: 0 0 5px #898989;   
	}
.pd_t_td1{
	width:200px;
	background:#e8313e;
	background-image: -webkit-linear-gradient(top, #e8313e 0%,#e8313e 100%);
  	background-image: -moz-linear-gradient(top, #e8313e 0%, #e8313e 100%);
  	background-image: -o-linear-gradient(top, #e8313e 0%, #e8313e 100%);
  	background-image: linear-gradient(to bottom, #e8313e 0%, #e8313e 100%);
	height:60px;
	float:left;
	}
.pd_t_td2{
	color:#2c3e50;
	font-size:24px;
	padding:0 0 0 15px;
	line-height:60px;
	float:left;
	}
.pd_t_td3{
	float:right;
	line-height:60px;
	padding:0 15px 0 0;
	color:#2c3e50;
	}
.tp_common{
	background-image:url(../images/tp_user.png);
	width:16px;
	height:16px;
	display:block;
	float:left;
	margin:22px 10px 0 10px;
	}
.pd_t_td3 img{
	 padding:0 5px;
	 }
.pd_content{
	width:1240px;
	margin:auto;
	padding:30px 0;
	}
.pd_cn{
	border-radius:2px;
	-webkit-border-radius:2px;
	-moz-webkit-border-radius:2px;
	background-color:#ffffff;
	}
.pd_cn_c1{
	width:263px;
	height:430px;
	float:left;
	}
.pd_cn_c2{
	width:263px;
	float:left;
	margin:0 0 0 13px;
	}
.pd_cn_title{
	width:100%;
	height:34px;
	line-height:34px;
	background-color:#ffffff;
	color:#303340;
	font-weight:bold;
	font-size:16px;
	border-top:1px solid #dddfeb;
	border-left:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
	}
.pd_cn_title span{
	margin:0 0 0 15px;
	}
.pd_left_icon{
	width:92px;
	float:left
	}
.pd_icon_common{
	width:32px;
	height:32px;
	background-image:url(../images/pandect_icon.png);
	float:left;
	}
.pd_icon{
	background-position:0 0;
	margin:45px 0 0 30px;
	}
.pd_icon2{
	background-position:0 -32px;
	margin:50px 18px 0 33px;
	}
.pd_icon3{
	background-position:0 -64px;
	margin:50px 18px 0 37px;
	}
.pd_icon4{
	background-position:0 -96px;
	margin:50px 18px 0 37px;
	}
.pd_list{
	float:left;
	width:163px;
	overflow:hidden;
	text-overflow:ellipsis;
	padding:0 10px 0 0;
	}
.pd_list li{
	height:30px;
	line-height:30px;
	}
.pd_list li a{
	 margin:0 0 0 20px;
	 }
.pd_list li a:hover{
	color:#13b1f5;
	}
.pd_list_hover{ background-image:url(../images/check.png); width:100%; height:30px;}
.pd_list_hover a{
	color:#13b1f5;
	font-weight:bold;
	}
.pd_cn_c2_1{
	width:100%; 
	height:134px;
	margin:0 0 11px 0;
	border:1px solid #dddfeb;
	}
.pd_left_icon2{
	height:100%;
	float:left
	}
.pd_list2{
	margin:40px 0 0 10px;
	}
.pd_list2 table tr{
	height:25px;
}
.pd_list2 table tr td{
	font-size:14px;
	text-align:left;
	}
.pd_forward{
	width:70px;
}
.pd_forwardinfo{
	width:100px;
	text-overflow:ellipsis;
	overflow:hidden; 
}
.pd_list3{
	float:left;
	text-align:center;
	margin:40px 0 0 0;
	line-height:25px;
	font-size:14px;
	}
.pd_list3 .pd_F_Green{ 
	font-size:20px;
	color:#303340;
	display:block
	}
.pd_F_Red{
	font-size:20px;
	color:#303340;
	display:block
	}
.pd_cn_c3{
	width:686px;
	margin:0 0 0 13px;
	float:left;
	}
.pd_pillar{
	width:328px;
	height:205px;
	float:left;
	border:1px solid #dddfeb;
	}
.pillar_title{
	font-size:16px;
	padding:10px 0 0 15px;
	width:100%;
	color:#303340;
	font-weight:bold
	}
.pd_circle{
	width:329px;
	height:205px;
	float:left;
	margin:0 0 0 11px;
	border:1px solid #dddfeb;
	}
.pd_polygonal{
	width:670px;
	height:210px;
	float:left;
	margin:11px 0 0 0;
	border:1px solid #dddfeb;
	}
.pd_step{
	width:1184px;
	margin:0 auto;
	clear:both;
	padding:19px;
	border:1px solid #dddfeb;
	float:left;
	}
.step_title{
	border-bottom:1px solid #dddfeb;
	padding:0 0 15px 0;
	font-size:16px;
	color:#303340;
	font-weight:bold
	}
.step_cn{
	width:100%;
	height:150px;
	overflow: hidden;
	}
.step_btn_common{
	background-image:url(../images/step_btn.png);
	width:30px;
	height:30px;
	float:left;
	margin:60px 0 0 0;
	cursor:pointer;
	}
.step_btn_common:hover{
	opacity:0.5;
}
.step_btn1{
	background-position: 0 0;
	}
.step_btn2{
	background-position: 0 -30px;
	}
.step_run{
	width:1085px;
	height:170px;
	float:left;
	padding:30px 18px;
	position:relative;
	overflow-x:auto;
	}
.step_tab{
	width:auto;
	}
.step_tab tr td{
	background-image:url(../images/step_line.png);
	}
.step_part{
	width:auto;
	margin:0 131px 0 0;
	text-align:center;
	position:relative;
	z-index:1;
	}
.step_part p{
	width:auto;
	text-align:center;
	margin:15px 0 0 0;
	font-size:12px;
	}
.step_part p span{
	padding:0 0 0 10px;
	}
.step_status{
	width:56px;
	height:56px;
	border-radius:100%;
	-moz-border-radius:100%;
	-webkit-border-radius:100%;
	margin:auto;
	cursor:pointer;
	}
.step_complete{
	border:2px solid #64b548;
	background-color:#ffffff;
	}
.step_complete:hover{
	background-color:#eff7ec;
	}
.step_not_running{
	border:2px solid #b8bbc4;
	background-color:#ffffff;
	}
.step_not_running:hover{
	background-color:#f1f1f3;
	}
.step_runing{
	border:2px solid #5168fc;
	position:relative;
	-webkit-box-shadow:0 0 15px #5168fc;  
	-moz-box-shadow:0 0 15px #5168fc;  
	box-shadow:0 0 15px #5168fc;
	background-color:#ffffff;  
	}
.step_runing:hover{
	background-color:#edf0ff;
	}
.step_abnormal{
	border:2px solid #fd703e;
	-webkit-box-shadow:0 0 15px #fd703e;  
	-moz-box-shadow:0 0 15px #fd703e;  
	box-shadow:0 0 15px #fd703e;
	background-color:#ffffff;
	}
.step_abnormal:hover{
	background-color:#fff0eb;
	}
.step_img_common{
	background-image:url(../images/step_run.png);
	width:56px;
	height:56px;
	}
.step_img{
	background-position:0 0;
	}
.step_img2{
	background-position:0 -112px;
	}
.step_img3{
	background-position:0 -56px;
	}
.step_img4{
	background-position:0 -168px;
	}
.pd_table{
	width:100%;
	height:auto;
	border:1px solid #e6e6e6;
	float:left
	}
.pd_tab_cn{
	width:100%;
	height:auto;
	float:left;
	}
.pd_tab_cn table{
	width:100%;
	}
.pd_tab_cn table tr td{
	padding:0 0 0 10px;
	border-bottom:1px solid #e6e6e6;
	}
.pd_tab_tr1{
	height:40px;
	background-color:#f6f6f6;
	font-weight:bold;
	font-size:13px;
	}
.pd_tab_tr2{
	height:35px;
	}
.pd_tab_cn a{
	float:left;
	}
.refreshbody{
	background: #ffffff;
}
.menu_font a span font{
	color:#585a69;
}
/**/
@keyframes twinkle_animation {
	0%{ fill-opacity:1}
	50%{ fill-opacity:0.2}
	100%{ fill-opacity:1}
}
/*clock*/
.clock {
    position: absolute;
    opacity: 1;
	top:105px;
	left:160px;
	
}
.centre {
    position: absolute;
    width: 0;
    height: 0;
}

.expand_hour {
    position: absolute;
    transform: translate(-50%, -50%);
}

.anchor {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
}

.element {
    position: absolute;
    top: 0;
    left: 0;
}

.round {
    border-radius: 296px;
}

.circle-1 {
    background: #d7dfff;
    width: 12px;
    height: 12px;
}

.circle-2 {
    background: #3c7bfe;
    width: 8px;
    height: 8px;
}

.circle-3 {
    background: #d7dfff;
    width: 4px;
    height: 4px;
}

.second {
    transform: rotate(180deg);
}

.minute {
    transform: rotate(54deg);
}

.second-hand {
    width: 2px;
    height: 112px;
    background: #3c7bfe;
    transform: translate(-50%,-100%) translateY(24px);
}

.hour {
    transform: rotate(304.5deg);
}

.thin-hand {
    width: 4px;
    height: 50px;
    background:#9fa0a0;
    transform: translate(-50%,-100%);
}

.fat-hand {
    width: 10px;
    height: 57px;
    border-radius: 10px;
    background: #9fa0a0;
    transform: translate(-50%,-100%) translateY(-18px);
}

.minute-hand {
    height: 112px;
}

.hour-text {
    position: absolute;
    font: 40px Hei, Helvetica, Arial, sans-serif;
    color: #d9e5ff;
    transform: translate(-50%,-50%);
}
.hour-10 {
    padding-left: 0.4ex;
}
.hour-11 {
    padding-left: 0.25ex;
}

.minute-text {
    position: absolute;
    font: 12px Avenir Next, Helvetica, Arial, sans-serif;
    color: #585a69;
    transform: translate(-50%,-50%);
}

.minute-line {
    background: #c3c5d2;
    width: 1px;
    height: 9px;
    transform: translate(-50%,-100%) translateY(-79px);
}
.hour_text{
	color:#5168fc;
	font-size:14px;
	width:100%;
	text-align:center;
	margin:110px 0 0 0;
	}
.k-ball-holder {
	position:absolute;
	width:10px;
	height:46px;
	left:23px;
	top:4px;
}
.k-ball-holder2 {
	position:absolute;
	width:10px;
	height:46px;
	left:25px;
	top:6px;
}
.k-ball1b {
	position:absolute;
	top:-10px;
	left:0px;
	width:8px;
	height:8px;
	border-radius:100%;
	background:#5168fc
}
.k-ball2a {
	position:absolute;
	bottom:-10px;
	left:0px;
	width:8px;
	height:8px;
	border-radius:100%;
	background:#5168fc
}

.k-ball3 {
	position:absolute;
	top:-10px;
	left:0px;
	width:8px;
	height:8px;
	border-radius:100%;
	background:#fd703e
}
.k-ball4 {
	position:absolute;
	bottom:-10px;
	left:0px;
	width:8px;
	height:8px;
	border-radius:100%;
	background:#fd703e
}

.k-ball-holder {
	animation:k-loadingE 1.3s linear infinite
}
.k-ball-holder2 {
	animation:k-loadingE 1.3s linear infinite
}
/* Utils */
.pull-right {
	float: right !important;
}
/*闂佽崵濮嶉崘顭戜还婵°倗濮村ú顓㈠箚閸モ晪鎷烽敐搴′簼缂佽鎷�2019-03-06*/
.cmdb_type{
	width:380px;
	height:auto;
	float:left;
	color:#303340;
	margin:0 5px;
	}
.cmdb_title{
	height:45px;
	line-height:45px;
	font-size:16px;
	}
.cmdb_detail{
	width:378px;
	height:160px;
	border:1px solid #dddfeb;
	}
.cmdb_detail ul{
	padding:0;
	margin:15px 0;
	float:left;
	}
.cmdb_detail ul li{
	float:left;
	width:125px;
	list-style:none;
	}
.cmdb_r_line{
	border-right:1px dashed #c8cedb;
	}
.dtl_icon{
	background-image:url(../images/cmdb_icon_count.png);
	width:30px;
	height:30px;
	float:left;
	margin:10px 9px 0 15px;
	}
.dtl_pos{
	background-position:0 0;
	}
.dtl_pos2{
	background-position:0 -30px;
	}
.dtl_pos3{
	background-position:0 -60px;
	}
.dtl_text{
	float:left;
	line-height:25px
	}
.cmdb_detail ul li h1{
	font-size:14px;
	color:#585a69;
	font-weight:normal;
	margin:0;
	}
.cmdb_detail ul li h2{
	font-size:18px;
	font-weight:normal;
	margin:0;
	text-align:center;
	cursor:pointer
	}
.cmdb_des{
	background-color:#f7f8fa;
	clear:both;
	width:auto;
	height:78px;
	padding:5px 5px;
	}
.cmdb_des h1{
	font-size:14px;
	font-weight:normal;
	margin:0;
	}
.cmdb_des h2{
	font-size:14px;
	color:#585a69;
	margin:0;
	padding:0;
	font-weight:normal;
	text-indent:2em;
	height:50px;
	overflow-y:auto;
	}

.cmdb_thumb {
    background: #ffffff;
    padding: 3px;
    padding-bottom: 0;
}

.cmdb_thumb img{
    height: 50px;
    width: 50px;
}

.cmdb-thumb-wrap {
    float: left;
    margin: 4px;
    margin-right: 0;
    padding: 5px;
}

.cmdb-x-item-over{
    border:1px solid #dddddd;
    /*background: #efefef url(over.gif) repeat-x left top;*/
    padding: 4px;
}

.cmdb-x-item-selected{
  /*  background: #eff5fb url(selected.gif) no-repeat right bottom;*/
    border:1px solid #99bbe8;
    padding: 4px;
}
.cmdb-x-item-selected .thumb{
	background:transparent;
}
/*缂傚倷绶￠崑澶愵敋瑜旈幃妤呮倻閽樺顦ч悗瑙勬礀濞差參宕楅敓锟�2019-03-06*/
.pic {
	width:206px;
	height:203px;
	background-color:#fff;
	box-shadow:0 0 10px #daddf2;
	-webkit-box-shadow:0 0 10px #daddf2;
	position:relative;
	overflow:hidden;
	margin:16px 8px 0 8px;
	display:inline-block;
	float:left;
	border-radius:4px;
	-webkit-animation:anima 2s;
	-moz-animation:anima 2s;
	-o-animation:anima 2s;
	-ms-animation:anima 2s;
	animation:anima 2s;
	-webkit-backface-visibility:hidden;
	-moz-backface-visibility:hidden;
	-o-backface-visibility:hidden;
	-ms-backface-visibility:hidden;
	backface-visibility:hidden
}
.picselect {
	border: 2px solid #409dfa;
}
.pic-text{
	width:186px;
	height:30px;
	line-height:30px;
	text-align:center;
	font-size:14px;
	color:#585a69;
	overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    margin:10px 0 0 0;
}
.pic-image {
	width:139px;
	height:139px;
	-webkit-transform:scale(1);
	-moz-transform:scale(1);
	-o-transform:scale(1);
	-ms-transform:scale(1);
	transform:scale(1);
	margin:15px auto 0 auto;
	cursor:pointer;
}
.image_size{
	width:100%; height:100%; background:url(../images/cmdb_topo.png) no-repeat 0 0 / 100% 100%;
	background-position:center center;
	background-size:cover;
	}
.pic:hover .pic-image {
	-webkit-transform:scale(1);
	-moz-transform:scale(1);
	-o-transform:scale(1);
	-ms-transform:scale(1);
	transform:scale(1)
}
.pic-caption {
	cursor:default;
	position:absolute;
	width:206px;
	height:100%;
	color:#fff;
	background:rgba(55, 61, 71, 0.8);
	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=($opacity * 100))";
	filter:alpha(opacity=0);
	-moz-opacity:0;
	-khtml-opacity:0;
	font-size:14px;
	line-height:2;
	text-indent:2em;
	padding:5px;
	opacity:0;
	cursor:pointer;
	overflow: hidden;
	font-family:"Microsoft Yahei";
}
.pic-caption a{
	text-decoration:none;
	color:#fff;
	}
.pic-caption a:hover{
	text-decoration:underline;
	}
.pic .pic-image, .pic-caption, .pic:hover .pic-caption, .pic:hover img {
	-webkit-transition:all 0.5s ease;
	-moz-transition:all 0.5s ease;
	-o-transition:all 0.5s ease;
	-ms-transition:all 0.5s ease;
	transition:all 0.5s ease;
	text-decoration:none;
	color:#fff;
}
.pic:hover .rotate-in{
	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=($opacity * 100))";
	filter:alpha(opacity=100);
	-moz-opacity:1;
	-khtml-opacity:1;
	opacity:1;
	-webkit-user-select:none;
	-moz-user-select:none;
	-o-user-select:none;
	-ms-user-select:none;
	user-select:none;
	-webkit-touch-callout:none;
	-moz-touch-callout:none;
	-o-touch-callout:none;
	-ms-touch-callout:none;
	touch-callout:none;
	-webkit-tap-highlight-color:transparent;
	-moz-tap-highlight-color:transparent;
	-o-tap-highlight-color:transparent;
	-ms-tap-highlight-color:transparent;
	tap-highlight-color:transparent
}
.rotate-in {
	-webkit-transform:rotate(90deg) scale(0.1);
	-moz-transform:rotate(90deg) scale(0.1);
	-o-transform:rotate(90deg) scale(0.1);
	-ms-transform:rotate(90deg) scale(0.1);
	transform:rotate(90deg) scale(0.1);
	top:0;
	left:0
}
.pic:hover .rotate-in {
	-webkit-transform:rotate(360deg) scale(1);
	-moz-transform:rotate(360deg) scale(1);
	-o-transform:rotate(360deg) scale(1);
	-ms-transform:rotate(360deg) scale(1);
	transform:rotate(360deg) scale(1)
}
.cmdb_ment{
	background-image:url(../images/cmdb_ment.png);
	width:161px;
	height:152px;
	margin:0 auto;
}
.md_text{
	border:1px solid #dddfeb;
	background-color:#f7f8fa;
	height:36px;
	font-size:14px;
	width:60%;
}
/*2019-03-25濠电儑绲藉ú鐘诲礈濠靛洤顕遍柛娑樼摠閸庡酣鏌熺�电浠滃ù鐙呮嫹*/
.statisticalChart_tab{
	background-color:#ffffff;
}
.StaChart_Common {
	width: 100%;
	height: 79px;
	border-radius: 2px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	text-align: center;
	padding: 7px 0 0 0;
	border:1px solid #dddfeb;
	background-color:#ffffff;
}
.statisticalChart_green {
	
}
.statisticalChart_blue {
	
}
.statisticalChart_red {
	
}
.statisticalChart_orange {
	
}
.statisticalChart_tab td {
	width: 25%;
	padding: 5px;
}
.StaChart_Con {
	width: 120px;
	margin: 10px auto;
}
.StaChart_Icon {
	background-image: url(../images/statisticalChart_green.png);
	width: 31px;
	height: 31px;
	display: block;
	float: left;
	margin:6px 0 0 0;
}
.StaChart_Icon2 {
	background-image: url(../images/statisticalChart_blue.png);
	width: 31px;
	height: 31px;
	display: block;
	float: left;
	margin:6px 0 0 0;
}
.StaChart_Icon3 {
	background-image: url(../images/statisticalChart_red.png);
	width: 31px;
	height: 31px;
	display: block;
	float: left;
	margin:6px 0 0 0;
}
.StaChart_Icon4 {
	background-image: url(../images/statisticalChart_orange.png);
	width: 31px;
	height: 31px;
	display: block;
	float: left;
	margin:6px 0 0 0;
}
.StaChart_Right {
	float: left;
	font-size: 14px;
	font-family: "Microsoft YaHei";
	color: #585a69;
	margin: 0 0 0 19px;
	border-left:1px dashed #c8cedb;
	padding:0 0 0 19px;
}
.StaChart_Bigfont {
	font-size: 20px;
	color:#303340;
}
.StaChart_Right span {
	display: block;
}
.Status_faulted{
	background-image: url(../images/Status_faulted.png);
	width:20px;
	height:20px;
}
.Status_partial{
	background-image: url(../images/Status_partial.png);
	width:20px;
	height:20px;
}
.Status_offline{
	background-image: url(../images/Status_offline.png);
	width:20px;
	height:20px;
}
.Status_online{
	background-image: url(../images/Status_online.png);
	width:20px;
	height:20px;
}
/*2019-03-28闂佸搫顦弲娆撴嚄閺堢數鏄�*/
.time_task_new{
	width: 100%;
	height: 120px;
	background-color:transparent;
	font-family: Microsoft Yahei;
}
.time_task_new tr td {
	width: 14%;
}
.time_task_td{
	padding:0 20px 0 0;
}
.time_task_new_01 {
	border: 0px solid #dddfeb;
	width: 100%;
	height: 120px;
	background-color:#ffffff;
	border-radius:4px;
}
.time_task_new_02 {
	border: 0px solid #dddfeb;
	width: 100%;
	height: 230px;
	background-color:#ffffff;
	border-radius:4px;
}
.t_icon_common{
	width:30px;
	height:30px;
	background-image: url(../images/task_icon.png);
	margin:17px auto 0 auto;
}
.task_icon{
	background-position:0 0;
}
.task_icon02{
	background-position:0 -30px;
}
.task_icon03{
	background-position:0 -60px;
}
.task_icon04{
	background-position:0 -90px;
}
.task_icon05{
	background-position:0 -150px;
}
.task_icon06{
	background-position:0 -180px;
}
.task_icon07{
	background-position:0 -120px;
}
.tasktext_new{
	float:left;
	text-align:center;
}
.tasktext_new h1{
	font-size:14px;
	color:#666666;
	font-weight:normal;
	padding:0;
	margin:0;
}
.tasktext_new h2{
	font-size:28px;
	padding:0;
	margin:0;
	font-weight:bold;
	color:#2d3039;
}
.tasktext_new h2 a{
	color:#2d3039;
	text-decoration:none;
}
.time_task_centered{
	display:table;
	margin:10px auto;
}

/*闂備礁鎲￠弻锝夊礉瀹ュ鏅搁柟瀛樺笩閸忓瞼绱掗鍝勫鐎殿喚顭堥…銊╁礃椤忓懐绉剧紓鍌欑筏閹风兘鏌ㄩ悤鍌涘*/
.data_demon{
	border:1px solid #dddfeb;
	border-radius:2px;
	background:#fff;
	padding:19px;
	color:#585a69;
	height:100%;
}
.data_selct{
	border:1px solid #dddfeb;
	border-radius:2px;
	color:#3d404d;
	appearance:none;
  	-moz-appearance:none;
 	-webkit-appearance:none;
  	background: url("../images/data_selct_arrow.png") no-repeat scroll right center #f7f8fa; 
	padding-right: 14px;
	width:210px;
	height:36px;
	font-family:Microsoft YaHei;
	float:left;
}
.data_selct::-ms-expand { display: none;}
.data_selct option{ 
	background:#ffffff;
    color:#585a69;
} 
.data_selct option:hover{
	background:#e3ebff;
    color:#5168fc; 
} 
.data_selct option:checked{ 
    background:#e3ebff; 
    color:#5168fc;  
}
.start_time{
	background-color:#f7f8fa;
	border:1px solid #dddfeb;
	color:#3d404d;
	width:210px;
	height:36px;
	line-height:36px;
	margin:0 0 0 0;
	font-family:Microsoft YaHei;
}
.data_chart{
	width:100%;
	height:350px;
	margin:19px 0 0 0;
	padding:19px 0 0 0;
	border:1px solid #dddfeb;
	float:left;
}
.data_demon_tab{
	width:100%;
	height:170px;
	overflow-y:auto;
	margin:19px 0 0 0;
	float:left;
}
.data_st_time{
	display:block;
	margin:0 0 0 15px;
	float:left;
}
.d_srch_btn{
	float:left;
	margin:0 0 0 15px;
}
.data_demon_tab table{
	width:100%;
	max-width: 100%;
	border-top:1px solid #dddfeb;
	border-left:1px solid #dddfeb;
}
.data_demon_tab table thead{
	height:34px;
	width:100%;
	background-color:#f7f8fa;
	color:#303340;
}
.data_demon_tab table thead th{
	border-bottom:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
}
.data_demon_tab table tbody{
	width:100%;
}
.data_demon_tab table tbody tr{
	height:34px;
	width:100%;
}
.data_demon_tab table tbody tr td{
	padding:0 0 0 10px;
	border-bottom:1px solid #dddfeb;
	border-right:1px solid #dddfeb;
}
.data_demon_tab table tbody tr:nth-child(odd) {
	background-color:#ffffff;
	} 
.data_demon_tab table tbody tr:nth-child(even) {
	background-color:#f7f8fa;
	}
/*2019-04-08*/
.wflow_running{
	background-image: url(../images/running02.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_queue{
	background-image: url(../images/queue.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_hangup{
	background-image: url(../images/hangup.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_fail{
	background-image: url(../images/fail02.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_buss{
	background-image: url(../images/buss_abnormal.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_pause{
	background-image: url(../images/pausejob.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_timeout{
	background-image: url(../images/timeout02.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_ready{
	background-image: url(../images/ready02.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_connect{
	background-image: url(../images/connection_failed.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}
.wflow_manual{
	background-image: url(../images/manual.png);
	width:21px;
	height:21px;
	margin:0 5px 0 0;
}

/*2019-04-24*/
.error_cry{
	background-image: url(../images/cry.png);
	width:238px;
	height:122px;
}

/*2019-04-25闂備礁鎼崐鐟邦熆濮楋拷璺柛鎰ㄦ櫆閸庣喖鏌￠崘銊ワ拷褰掑汲韫囨洜纾藉ù锝呮憸閹界姴鈹戦鍝勭伈闁诡垰瀚伴獮瀣倷闊厾甯�*/
.System_info1{
	display:block;
	padding:0 20px;
}
.graph_select{
	border:1px solid #d9d9d9;
	border-radius:2px;
	color:#46545d;
	background-color:#ffffff;
	appearance:none;
  	-moz-appearance:none;
 	-webkit-appearance:none;
  	background: url("../images/red_arrow.png") #ffffff no-repeat scroll right center; 
	padding-right: 14px;
	width:97%;
	height:31px;
	line-height:31px;
	font-family:Microsoft YaHei;
	padding:0 14px 0 3px;
	margin:0 0 5px 5px;
}
.graph_select::-ms-expand { display: none;}
.graph_select option{ 
	background:#ffffff;
    color:#2b2b2b;
} 
.graph_select option:hover{
	background:#b8e8fc;
    color:#2b2b2b; 
} 
.graph_select option:checked{ 
    background:#15b2f6; 
    color:#ffffff;  
}
.Package_Pre{
	background-image: url(../images/Package_Pre.png);
	width:24px;
	height:20px;
}
.Package_Next{
	background-image: url(../images/Package_Next.png);
	width:24px;
	height:20px;
}
/*2019-06-17*/
.state_position{
	position:relative
}
.pg_tab{
	font-size:12px;
	}
.pg_start{
	background-image:url(../images/pg_start.png);
	width:20px;
	height:34px;
	}
.pg_end{
	background-image:url(../images/pg_end.png);
	width:20px;
	height:34px;
	}
.pg_green{
	background:url(../images/pg_green.png) no-repeat;
	width:20px;
	height:48px;
	}
.pg_yellow{
	background:url(../images/pg_yellow.png) no-repeat;
	width:20px;
	height:48px;
	}
.pg_purple{
	background:url(../images/pg_orange.png) no-repeat;
	width:20px;
	height:48px;
	}
.pg_blue{
	background:url(../images/pg_blue.png) no-repeat;
	width:20px;
	height:48px;
	}
.pg_gray{
	background:url(../images/pg_gray.png) no-repeat;
	width:20px;
	height:48px;
	}
.pg_red{
	background:url(../images/pg_red.png) no-repeat;
	width:20px;
	height:48px;
	}
.pg_start_cn{
	border-radius:4px;
	background-color:#007af4;
	height:24px;
	line-height:24px;
	color:#fff;
	width:70px;
	text-align:center
	}
.pg_green_cn{
	background-color:#05c464;
	}
.pg_yellow_cn{
	background-color:#ffbe24;
	}
.pg_purple_cn{
	background-color:#fc6630;
	}
.pg_blue_cn{
	background-color:#409dfa;
	}
.pg_gray_cn{
	background-color:#bbbbbb;
	}
.pg_red_cn{
	background-color:#ff001c;
	}
.Emergency_middle2_pf .pg_color_cn{
	width:280px;
}
.Emergency_middle2_pf .pg_status{
	width:28px;
	height:28px;
	position:absolute;
	margin:-34px 0 0 265px;
	}
.Emergency_middle2_pf .Package_Common{
	padding:0 0px 5px 21px;
	}
.pg_color_cn{
	border-radius:4px;
	height:38px;
	padding:0 20px;
	color:#fff;
	}
.pg_color_cn ul{
	padding:0;
	margin:0;
	text-align:center;
	}
.pg_color_cn ul li{
	list-style:none;
	line-height:19px;
	}
.pg_color_cn ul li span{
	padding:0 5px;
	width:200px;
	margin:0 auto;
	display:block;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
	}
.Emergency_middle2_pf .pg_status{
	width:28px;
	height:28px;
	position:absolute;
	margin:-34px 0 0 265px
 }
.pg_status{
	width:28px;
	height:28px;
	position:absolute;
	margin:-34px 0 0 225px;
	}
.pg_status_green{
	background-image:url(../images/pg_status.png);
	background-position:0 0;
	}
.pg_status_blue{
	background-image:url(../images/pg_status.png);
	background-position:0 -28px;
	}
.pg_status_red{
	background-image:url(../images/pg_status.png);
	background-position:0 -56px;
	}
	
/*闂備浇顕栭崜姘ｉ幒妤嬬稏鐎广儱鎷嬮崵鏇㈢叓閸ャ劍绀冪痪鐙欏洦鐓欓柣姘摠鐎氾拷*/
.view_mtor{
	width:290px;
	height:210px;
	background-color:#fff;
	border-radius:6px;
	padding:15px;
	float:left;
	margin:0 20px 20px 0;
	}
.view_mtor2{
	width:313px;
	height:235px;
	background-color:#fff;
	border-radius:6px;
	padding:15px;
	float:left;
	margin:0 20px 20px 0;
	}
.view_mtor3{
	width:25%;
	height:265px;
	float:left;
	padding:20px 20px 0 0;
	
	}
.view_mtor3:nth-child(4n+4) {padding-right:10px}
.view_mtor3-con{
	background-color:#fff;
	border-radius:6px;
	padding:15px;
	height:100%;
	
}
.view_mtor3 .v_mtor_link a{color:#409dfa}

.v_mtor_cn{
	height:auto;
	float:left;
	width:100%;
	padding:0 0 15px 0;
	border-bottom:1px solid #d9d9d9;
	}
.v_mtor3_text{
	float:left;
	margin:0 0 0 20px;
	}
.v_mtor3_text h1{
	font-size:18px;
	color:#929ea8;
	padding:0;
	margin:0 0 5px 0;
	}
.v_mtor3_text h2{
	font-size:14px;
	color:#929ea8;
	padding:0;
	margin:0;
	font-weight:normal;
	}
.v_color_icon{
	background-image:url(../images/view_mtor_icon.png);
	width:44px;
	height:44px;
	display:block;
	float:left
	}
.v_mtor_blue{
	background-position:0 0;
	}
.v_mtor_yellow{
	background-position:0 -44px;
	}
.v_mtor_red{
	background-position:0 -88px;
	}
.v_mtor_text{
	float:left;
	margin:0 0 0 20px;
	}
.v_mtor_text h1{
	font-size:18px;
	color:#323a3d;
	padding:0;
	margin:0 0 5px 0;
	}
.v_mtor_text h2{
	font-size:14px;
	color:#409dfa;
	padding:0;
	margin:0;
	font-weight:normal;
	}
.v_text_blue{
	color:#409dfa;
	}
.v_text_yellow{
	color:#ffbe24;
	}
.v_text_red{
	color:#ff001c;
	}
.v_mtor_link{
	float:right;
	}
.v_mtor_link a{
	color:#929ea8;
	font-size:14px;
	text-decoration:none;
	}
.v_mtor_link a:hover{
	text-decoration:underline;
	}
.v_mtor_list{
	font-size:14px;
	color:#929ea8;
	float:left;
	margin:8px 0 0 0;
	}
.v_mtor_list ul{
	padding:0;
	margin:0;
	}
.v_mtor_list ul li{
	list-style:none;
	line-height:28px;
	width:100%;
	display:flex;
	}
.v_mtor_row{
	width:75px;
	text-align:right;
	display:block;
	float:left;
	}
.v_mtor_row02{
	float:left;
	margin:0 0 0 15px;
	display:block;
	}
	
	.v_mtor_row03{
	float:left;
	margin:0 0 0 15px;
	display:block;
	width:120px;
	float:left;
	white-space:nowrap;
	overflow:hidden;
	text-overflow: ellipsis; 
	}
	
/*2019-06-26*/
.kl_monitor_tab{
	width:100%;
	padding:9px;
}
.kl_monitor_td01{
	width:238px;
	padding:0 8px 0 0;
}
.kl_monitor_td02{
	width:238px;
	padding:0 9px 0 0;
}
.kl_business_sys{
	width:238px;
	border:1px solid #dcdddd;
	border-radius:4px;
	padding:0px 7px 7px 7px;
	background-color:#ffffff;
}
.kl_business_sys h1{
	font-size:14px;
	padding:0;
	margin:0;
	height:49px;
	line-height:49px;
	color:#595757;
	font-weight:bold;
}
.kl_business_sys_stage{
	width:238px;
	border:1px solid #dcdddd;
	background-color:#ffffff;
	border-radius:4px;
	padding:0px 7px 7px 9px;
}
.kl_business_sys_stage h1{
	font-size:14px;
	padding:0;
	margin:0;
	height:49px;
	line-height:49px;
	color:#595757;
	font-weight:bold;
}
.kl_sys_cn{
	width:100%;
}
.kl_sys_cn ul li{
	border-radius:4px;
	margin:0 0 4px 0;
	padding:0 5px;
	cursor:pointer;
}
.kl_list_over{
	background-color:#f8f8f8;
	height:40px;
	line-height:40px;
}
.kl_list_over .kl_list_name{
	color:#05c464;
}
.kl_list_over .kl_list_text{
	color:#05c464;
}
.kl_list_default{
	background-color:#f8f8f8;
	height:40px;
	line-height:40px;
}
.kl_list_secondary{
	padding:0 0;
}
.kl_list_secondary li span{
	margin:0 0 0 10px;
	cursor:pointer;
}
.kl_list_text{
	float:left;
}
.kl_list_name{
	float:left;
	font-size:12px;
	color:#595757;
	width:154px;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}
.kl_list_status{ 
	float:left;
	width:24px;
	height:24px;
	display:block;
	margin:8px 0 0 0;
	}
.kl_list_orange{
	background-image:url(../images/kl_list_yellow.png);
}
.kl_list_red{
	background-image:url(../images/kl_list_red.png);
}
.kl_list_green{ 
	background-image:url(../images/kl_list_green.png);
}
.kl_list_purple{
	background-image:url(../images/kl_list_purple.png);
}
.kl_list_blue{ 
	background-image:url(../images/kl_list_blue.png);
}
.kl_list_gray{
	background-image:url(../images/kl_list_gray.png);
}
.kl_list_orange_skip{
	background-image:url(../images/kl_list_orange_skip.png);
}
.kl_page_center{
	text-align:center;
}
.kl_page{
	background-image:url(../images/kl_page.png);
	width:22px;
	height:22px;
	display:block;
	cursor:pointer;
	display:inline-block
}
.kl_page_pre{
	background-position:0 0;
}
.kl_page_next{
	background-position:0 -22px;
	margin:0 0 0 6px;
}
.kl_sys_stage_cn{
	padding:0
}
.klstage_color{
	height:40px;
	border-radius:4px;
	cursor:pointer;
	padding:3px 0 0 9px;
	}
.klstage_click_green .klstage_Font{
	color:#05c464
}
.klstage_click_red .klstage_Font{
	color:#ff001c
}
.klstage_click_yellow .klstage_Font{
	color:#ffbe24
}
.klstage_click_blue .klstage_Font{
	color:#409dfa
}
.klstage_Green{
	border:1px solid #d8f5e1;
	}
.klstage_Yellow{
	border:1px solid #f9e7c4;
	}
.klstage_Gray{
	border:1px solid #ececec;
	}
.klstage_Red{
	border:1px solid #f8e4e2;
	}
.klstage_Purple{
	border:1px solid #ffe4d3;
	}
.klstage_Blue{
	border:1px solid #d9e9fc;
	}
.klstage_status{
	width:24px;
	height:24px;
	float:left;
	margin:4px 0 0 0;
}
.klstage_Green .klstage_status{
	background-image:url(../images/kl_list_green.gif);
}
.klstage_Blue .klstage_status{
	background-image:url(../images/kl_list_blue.gif);
}
.klstage_Red .klstage_status{
	background-image:url(../images/kl_list_red.gif);
}
.klstage_Font{
	width:176px;
	float:left;
	font-size:12px;
	color:#595757;
	}
.klstage_Font span{
	display:block; 
	height:16px; 
	line-height:16px;
	width:157px;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
	}
.klstage_arrow_show{ 
	background-image:url(../images/klstage_arrow_show.png); 
	width:12px; 
	height:8px;
	margin:10px auto;
	}
.stage_details{
	width:100%;
	border:1px solid #dcdddd;
	background-color:#ffffff;
	border-radius:4px;
	padding:0 19px 7px 19px;
}
.stage_details h1{
	font-size:14px;
	padding:0;
	margin:0;
	height:40px;
	line-height:49px;
	color:#595757;
	font-weight:bold;
}

.stage_details_grid .x-column-header{
	color: #999999;
	font: 12px/15px arial, Microsoft Yahei, helvetica, verdana, sans-serif;
	background-color: #ffffff;
	padding:8px 0 9px 0;
}
.stage_details_grid .x-column-header-inner {
	padding: 0 10px 0 10px;
	text-overflow: ellipsis;
	border-right: 1px solid #dcdddd;
}
.stage_details_grid .x-grid-header-ct {
	border: 0px solid #ffffff;
	border-bottom-color: #ffffff;
	background-color: #ffffff
}
.stage_details_grid .stage_last .x-column-header-inner {
	padding: 0 10px 0 10px;
	text-overflow: ellipsis;
	border-right: 0px solid #dcdddd;
}
.stage_details_grid .x-grid-body {
	background: white;
	border-width: 0px;
	border-style: solid;
	border-color: #ffffff
}
.stage_details_grid .x-grid-group-hd {
	border-width: 0 0 1px 0;
	border-style: solid;
	border-color: #dcdddd;
	padding: 8px 4px 7px 4px;
	background: #f8f8f8;
	cursor: pointer
}
.stage_details_grid .x-grid-group-title {
	color: #4d4d4d;
	font: bold 12px/15px arial, Microsoft Yahei, helvetica, verdana, sans-serif
}
.stage_details_grid .x-grid-cell {
	color: #666666;
	font: normal 12px/15px arial, Microsoft Yahei, helvetica, verdana, sans-serif;
	background-color: #ffffff;
	border-color: transparent;
	border-style: solid;
}
.stage_details_grid .x-grid-with-row-lines .x-grid-td {
	border-bottom-width: 3px;
	border-top-width:0px;
}
.stage_details_grid .x-grid-cell-inner {
	text-overflow: ellipsis;
	height:30px;
}
.stage_details_grid .x-grid-row-before-over .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.stage_details_grid .x-grid-row-over .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.stage_details_grid .x-grid-row-before-focused .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.stage_details_grid .x-grid-row-focused .x-grid-td {
	background-color: transparent;
}
.stage_details_grid .x-grid-row-over .x-grid-td {
	background-color: #f8f8f8
}
.stage_details_grid .x-grid-row-focused .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.stage_details_grid .x-grid-with-row-lines .x-grid-row-focused-first .x-grid-td {
	border-top: 1px solid #ffffff
}
.stage_details_grid .x-grid-row-focused .x-grid-row-summary .x-grid-td {
	border-bottom-color: #ffffff;
	border-top-width: 0
}
.stage_details_grid .x-grid-with-row-lines .x-grid-table-over-first {
	border-top-style: solid;
	border-top-color: #ffffff
}
.stage_details_grid .x-grid-with-row-lines .x-grid-table-focused-first {
	border-top-style: solid;
	border-top-color: #ffffff
}

.stage_details_grid .x-grid-row-selected .x-grid-td {
	background-color: #f8f8f8
}
.stage_details_grid .x-grid-row-before-selected .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.stage_details_grid .x-grid-row-selected .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.stage_details_grid .x-grid-row-selected .x-grid-row-summary .x-grid-td {
	border-bottom-color: #ffffff;
	border-top-width: 0
}
.stage_details_grid .x-grid-with-row-lines .x-grid-table-selected-first {
	border-top-style: solid;
	border-top-color: #ffffff
}
.stage_details_grid .x-grid-group-hd-collapsible .x-grid-group-title {
	background-repeat: no-repeat;
	background-position: left center;
	background-image: url(../images/group-collapse.png);
	padding: 0 0 0 17px
}
.stage_details_grid .x-grid-group-hd-collapsed .x-grid-group-title {
	background-image: url(../images/group-expand.png)
}
.stage_list_status{ 
	float:left;
	width:24px;
	height:24px;
	display:block;
	margin:-5px 0 0 0;
	}
.stage_list_status02{ 
	float:left;
	width:24px;
	height:24px;
	display:block;
	margin:4px 0 0 0;
	}
.kl_rocess{
	margin:8px 0 0 0;
}
.kl_operat{
	width:162px;
	margin:8px 0 0 11px;
}
.public_btn{
	background-color:#007bf5;
	border:1px solid #007bf5;
	border-radius:2px;
	width:86px;
	height:26px;
	line-height:26px;
	color:#ffffff;
	text-align:center;
	display:block;
	margin:0 auto 20px auto;
	cursor:pointer;
}
.public_btn_gray{
	background-color:#d2d2d2;
	border:1px solid #d2d2d2;
	border-radius:2px;
	width:86px;
	height:26px;
	line-height:26px;
	color:#ffffff;
	text-align:center;
	display:block;
	margin:0 auto 20px auto;
	cursor:pointer;
}
.public_btn:hover{
	background-color:#46c3fa;
}
.pub_btn_space{
	margin:0 auto;
}

.kl_bottom_state{
	width:100%;
}
.kl_state{
	border:1px solid #dcdddd;
	width:610px;
	height:37px;
	line-height:37px;
	border-radius:4px;
	color:#4d4d4d;
	font-size:12px;
	margin:0 0 0 9px;
	background-color:#ffffff;
	float:left;
}
.kl_state_pub{
	width:24px;
	height:24px;
	background-image: url(../images/kl_state_pub.png);
	float:left;
	display:block;
	margin:6px 10px 0 10px;
}
.kl_state_text{
	float:left;
}
.kl_state_green{
	background-position:0 0
}
.kl_state_yellow{
	background-position:0 -24px
}
.kl_state_purple{
	background-position:0 -48px
}
.kl_state_blue{
	background-position:0 -72px
}
.kl_state_gray{
	background-position:0 -96px
}
.kl_state_red{
	background-position:0 -120px
}
.kl_list_orange_skip{
	background-position:0 -144px
}
.kl_state_pause{
	background-image: url(../images/kl_state_pause.png);
	width:24px;
	height: 24px;
	display:block;
	float:left;
	margin-left:10px;
	margin-top:6px;
	margin-right:10px;
}
.refresh_time{
	float:right;
	width:540px;
}
#refresh_bottom{
	float:left;
}
.refresh_time_btn{
	float:left;
	border:1px solid #dcdddd;
	background-color:#ffffff;
	border-radius:2px;
	height:36px;
	line-height:35px;
	margin:5px 0 0 0;
	cursor:pointer;
}
.first_btn{
	padding:0 15px;
	float:left;
	border-right:1px solid #dcdddd
}
.first_btn02{
	padding:0 15px;
	float:left;
	border-right:0px solid #dcdddd
}
.refresh_btn_over:hover{
	background-color:#007bf5;
	color:#fff;
}
.kl_check_box {
	width: 25px;
	height: 40px;
	float: left;
	cursor: pointer;
	text-align: center;
	background-image: url(../images/kl_check_box.png);
	background-repeat: no-repeat;
	background-position: 0 0;
	position:relative;
	left:0;
	}
.kl_on_check {
	background-position: 0 -40px;
	}
.kl_selectAllChild {
	opacity: 0;
	cursor: pointer;
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	margin:12px 0 0 -5px;
	}
.m_process_panel .x-form-text {
	color: #999999;
	padding: 0 0 0 3px;
	background: white repeat-x 0 0;
	border-width: 1px;
	border-style: solid;
	border-color:#dedfdf;
	height: 26px;
	line-height: 15px;
	border-radius:4px
}
.m_process_panel .x-form-item-label {
	color: #4d4d4d;
	font: normal 12px/17px arial, Microsoft Yahei, helvetica, verdana, sans-serif;
	margin-top: 4px
}
.m_process_panel .x-grid-body {
	background: white;
	border-width: 0px;
	border-style: solid;
	border-color: #e5e5e5
}
.m_process_panel .x-toolbar-default {
	border-color: #e5e5e5;
	border-width: 0px;
	background-image: none;
	background-color: white
}
.m_process_panel .x-panel-body-default {
	/*background: white;*/
	border-color: #dddddd;
	color: #4d4d4d;
	font-size: 13px;
	font-size: normal;
	border-width: 0px;
	border-style: solid
}
.kl_output{
	position:absolute;
	border-style:solid; 
	border-radius:4px;
	border-width:1px; 
	border-color:#dcdddd; 
	top:5px; 
	left:0px;
	bottom:7px;
	right:0px; 
	width:100%;
	overflow:auto;
	white-space:pre-wrap;
	margin-top:0px;
	color:#666;
	padding:0 10px;
}
/*2019-04-25*/
.map_div{
	position:absolute; 
	z-index:10000000;
	display:none;
	pointer-events: auto;
	width:530px;
	height:300px;
	background-color:rgba(255,255,255,0.8);
	margin:120px 0 0 0;
	border-radius:4px;
	box-shadow: 0 0 5px #c9c9c9;
	-webkit-box-shadow: 0 0 5px #c9c9c9;
	-moz-box-shadow: 0 0 5px #c9c9c9;
}
.map_div_bg{
	background-color:#FBFBFB;
	width:380px; 
	height:380px;
	top:20px; 
	left:100px;
	position:relative;
	box-shadow: 0 0 5px #c9c9c9;
	-webkit-box-shadow: 0 0 5px #c9c9c9;
	-moz-box-shadow: 0 0 5px #c9c9c9;
}
.map_chart{
	width:550px;
	height:380px;
	position:absolute;
	margin:0 0 0 0px;
}
.monitor_graph{
	background-color:#f7f8f9;
	border:1px solid #d9d9d9; 
	margin:30px 30px 20px 30px
}
.inspection_line{
	width:90%;
	background-color:#d9d9d9;
	height:1px; 
	margin:0 auto
}
.program_name{
	background-image: url(../images/program_name.png);
	width:20px;
	height:20px;
	display:block;
	float:left;
	margin:8px 10px 0 0
}


.Change_LeftNEW{ 
	height:auto;
	margin:0 10px;
	}
.Change_LeftNEW ul li{
	margin:0 0 0 0;
	font-size:12px;
	}
.Change_LeftNEW ul li .System_info{ 
	float:left; 
	width:260px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
/*2019-07-18闂佽崵濮嶉崘顭戜还婵°倗濮村ú顓㈠箚閸モ晪鎷烽敐搴′簼缂佽鎷�*/

.asset_board{
	width:250px;
	height:332px;
	background-color:#ffffff;
	border-radius:8px;
	margin:0 15px 15px 0;
	float:left;
	}
.asset_board h1{
	color:#46545d;
	font-weight:bold;
	padding:18px 0;
	margin:0;
	font-size:16px;
	text-align:center
	}
.asset_board_icon{
	background-color:#af69ff;
	border-radius:100%;
	width:50px;
	height:50px;
	margin:0 auto 10px auto;
	text-align:center
	}
.asset_board_icon img{
	margin:10px 0 0 0;
}
.asset_board h2{
	font-size:30px;
	padding:0;
	margin:0;
	text-align:center;
	color:#2d3039
	}
.asset_total{
	font-size:12px;
	color:#666666;
	display:block;
	text-align:center
	}
.asset_enable{
	width:205px;
	margin: 13px auto 9px auto;
	}
.ass_en_part01{
	float:left;
	}
.ass_en_part01 h1{
	font-size:20px;
	color:#2d3039;
	padding:0;
	margin:0;
	line-height:22px;
	}
.ass_en_text{
	}
.ass_en_common{
	background-image:url(../images/asset_enable_icon.png);
	width:14px;
	height:14px;
	display:block;
	margin:3px 6px 0 0;
	float:left
	}
.ass_en_icon01{
	background-position:0 0;
	}
.ass_en_icon02{
	background-position:0 -14px;
	}
.ass_en_amount{
	color:#666666;
	font-size:14px;
	float:left;
	}
.ass_en_line{
	width:1px;
	height:20px;
	background-color:#dbdee2;
	float:left;
	margin:11px 25px 0 25px;
	}
.asset_des{
	width:214px;
	height:76px;
	margin:0 auto;
	position:relative;
	top:9px;
	background-color:#fafafa;
	border-radius:8px;
	clear:both;
	padding:5px 10px;
	}
.asset_des h1{
	padding:0;
	margin:0;
	font-size:12px;
	color:#979fac;
	font-weight:normal;
	}
.asset_des span{
	font-size:12px;
	color:#979fac;
	display:block;
	line-height:16px;
	margin:5px 0 0 0;
	width:100%;
	height:48px;
	overflow:hidden
	}
.form_images{
	background-image:url(../images/form_image.png);
	width:110px;
	height:86px;
	}
	
/*2020-05-09*/
.script_module_img{ 
	background-image:url(../images/IOVN.png); 
	overflow: hidden;
  	background-position: center center;
  	background-repeat: no-repeat;
  	background-size: cover;
  	-webkit-background-size: cover;
 	-moz-background-size: cover;
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.btnimg{
	background-image:url(../images/script_module_btnimg.png);
	width:12px;
	height:12px;
	cursor:pointer 
}
.btnimg:hover{
	opacity:0.5;
}
.leftbtn_pos{
	 background-position:0 0;
}
.rightbtn_pos{
	 background-position:0 -12px;
}
.script_module_td{
	padding:0 10px;
}

/*2020-05-19*/
.oper_sys_icon01{ 
	background-image:url(../images/oper_sys_icon01.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.oper_sys_icon02{ 
	background-image:url(../images/oper_sys_icon02.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.oper_sys_icon03{ 
	background-image:url(../images/oper_sys_icon03.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.oper_sys_icon04{ 
	background-image:url(../images/oper_sys_icon04.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
/*2020-07-24*/
.title_glint{
	font-family:Microsoft Yahei;
	font-size:17px;
	font-weight:bold;
	background-image: -webkit-linear-gradient(92deg, #fc6f5e, #ec4b5a);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	-webkit-animation: textcolor 2s infinite linear , changeshadow 1s ease-in infinite;
	-ms-animation: textcolor 2s infinite linear , changeshadow 1s ease-in infinite;
}
.serial_num{
		float:left;
		background-color:#007af4;
		width:24px;
		height:24px;
		border-radius:100%;
		display:block;
		font-size:14px;
		color:#fff;
		padding:0;
		font-weight:normal;
		line-height:24px;
		text-align:center;
		margin:10px 10px 0 10px;
		}

@keyframes changeshadow {  
        0%{ text-shadow: 0 0 4px #007af5}  
        50%{ text-shadow: 0 0 20px #007af5}  
        100%{ text-shadow: 0 0 4px #007af5}  
    }
@-ms-keyframes changeshadow {  
        0%{ text-shadow: 0 0 4px #007af5}  
        50%{ text-shadow: 0 0 20px #007af5}  
        100%{ text-shadow: 0 0 4px #007af5}  
    }
@-moz-keyframes changeshadow {  
        0%{ text-shadow: 0 0 4px #007af5}  
        50%{ text-shadow: 0 0 20px #007af5}  
        100%{ text-shadow: 0 0 4px #007af5}  
    }
@-webkit-keyframes textcolor {
	from {
	
	-webkit-filter: hue-rotate(0deg);
	-moz-filter: hue-rotate(0deg);
	}
	to {
	-webkit-filter: hue-rotate(-360deg);
	-moz-filter: hue-rotate(-360deg);
}
}
@-ms-keyframes textcolor {
	from {
	
	-webkit-filter: hue-rotate(0deg);
	-moz-filter: hue-rotate(0deg);
	}
	to {
	-webkit-filter: hue-rotate(-360deg);
	-moz-filter: hue-rotate(-360deg);
}
}
@-moz-keyframes textcolor {
	from {
	
	-webkit-filter: hue-rotate(0deg);
	-moz-filter: hue-rotate(0deg);
	}
	to {
	-webkit-filter: hue-rotate(-360deg);
	-moz-filter: hue-rotate(-360deg);
}
}
/*2020-09-23*/
.execution_blue{
	background-image:url(../images/ext_blue.png);
	width:22px;
	height:22px;
	margin-top:11px; 
	border-style:none;
}
.execution_gray{
	background-image:url(../images/ext_gray.png);
	width:22px;
	height:22px;
	margin-top:11px; 
	border-style:none;	
}
.finish_blue{
	background-image:url(../images/finish_blue.png);
	width:22px;
	height:22px;
	margin-top:11px; 
	border-style:none;		
}
.finish_gray{
	background-image:url(../images/finish_gray.png);
	width:22px;
	height:22px;
	margin-top:11px;
	border-style:none;		
}
.rollback{
	background-image:url(../images/rollback.png);
	width:18px;
	height:18px; 
	border-style:none;	
}
/*婵犳鍠楃缓鍧楀磹閺嶎厼钃熼柛銉ｅ妿椤╃兘鏌ㄥ┑鍡樺窛闁搞劌纾槐鎾诲礃閹勭亞缂備緡鐓堥崜鐔奉嚕椤掑嫬鐐婇柨鏃傜摂閸嬨劑鏌熼悡搴ｆ憼婵炶尙鍠栧畷鏇㈡晸閿燂拷*/
@keyframes k-loadingE {
	0 {
	transform:rotate(0deg)
}
100% {
	transform:rotate(360deg)
}
}
@-moz-keyframes k-loadingE {
	0 {
	transform:rotate(0deg)
}
100% {
	transform:rotate(360deg)
}
}
@-webkit-keyframes k-loadingE {
	0 {
	transform:rotate(0deg)
}
100% {
	transform:rotate(360deg)
}
}
.snapshot_black{
	background-image: url(../images/snapshot_black.png);
	width:20px;
	height:20px;
}
.text_box[type=checkbox],input[type=radio] {
            -webkit-appearance: none;
            appearance: none;
            outline: none;
            width: 12px;
            height: 12px;
            cursor: pointer;
            vertical-align: center;
            background: #fff;
            border: 1px solid #cccccc;
            position: relative;
        }

.text_box[type=checkbox]:checked::after {
            content: "\2713";
            display: block;
            position: absolute;
            top: -1px;
            left: -1px;
            right: 0;
            bottom: 0;
            width: 10px;
            height: 10px;
            line-height: 10px;
            border: 1px solid #007af5;
            color:#007af5;
            font-size: 14px;
        }
.Actmonitor_tip{
	border-radius:4px;
	background-color:rgba(0,0,0,0.4);
	padding:8px 20px;
	float:left;
	color:#fff;
	font-size:14px;
	display:block;
	}
.Actmonitor_ellipsis{
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}
/*健康巡检通用报表*/
.report_box{
	margin: auto;
	width:auto!important;
	padding-top:9px;
	}
.report_box .bootstrap-tagsinput {
  background-color: white;
  border: 1px solid #ebedef;
  border-radius: 6px;
  margin-bottom: 18px;
  padding: 6px 1px 1px 6px;
  text-align: left;
  font-size: 0;
  min-height:70px;
  max-height:100px;
  overflow-y:auto;
}

.report_box .bootstrap-tagsinput .tag {
  border-radius: 4px;
  background-color: #ebedef;
  color: #7b8996;
  font-size: 13px;
  cursor: pointer;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  overflow: hidden;
  margin: 0 5px 5px 0;
  padding: 6px 28px 6px 14px;
  transition: .25s linear;
}

.report_box .bootstrap-tagsinput .tag > span {
  color: white;
  padding: 0 10px 0 0;
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  right: 0;
  text-align: right;
  text-decoration: none;
  top: 0;
  width: 100%;
  bottom: 0;
  z-index: 2;
}

.report_box .bootstrap-tagsinput .tag > span:after {
  content: "x";
  font-family: "Flat-UI-Pro-Icons";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 27px;
}

/*@media (hover: hover) {
  .bootstrap-tagsinput .tag {
    padding: 6px 21px;
  }
  .bootstrap-tagsinput .tag > span {
    opacity: 1;
    filter: "alpha(opacity=1)";
    transition: opacity .25s linear;
  }
  .bootstrap-tagsinput .tag:hover {
    background-color: #16a085;
    color: white;
    padding-right: 28px;
    padding-left: 14px;
  }
  .bootstrap-tagsinput .tag:hover > span {
    padding: 0 10px 0 0;
    opacity: 1;
    -webkit-filter: none;
            filter: none;
  }
}*/


.report_box .bootstrap-tagsinput input[type="text"] {
  font-size: 14px;
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  margin: 0;
  width: auto !important;
  max-width: inherit;
  min-width: 80px;
  vertical-align: top;
  height: 29px;
  color: #34495e;
}

.report_box .tagsinput-primary {
  margin-bottom: 18px;
  display:flex;
  padding:0 5px
}

.report_box .tagsinput-primary .bootstrap-tagsinput {
  display:block;
	border: 1px solid #d9d9d9;
	border-radius: 2px;
	width:488px;
}

.report_box .tagsinput-primary .tag {
  background-color: #f7f7f7;
  color: #747474;
  border:1px solid #e6e6e6
}
.report_box .btn{background: #1ABC9C;border: none;color: #fff;padding: 10px;border-radius: 5px;margin-top: 10px;}

.report_box .s_tit{
	display:inline-block;
	width:80px;
	height:70px;
	text-align:right;
	margin-right:5px;
	display:flex;
	justify-content: flex-end;
	align-items: center;
}
.report_box .customize_additem{
	height:auto!important
}

.customize_additem .Common_Btn{
	top:25px!important
}
.customize_dist{
	margin-top:30px
}

.tp_screenshot{
	background-image: url(../images/screenshot.png);
	width: 30px;
	height: 30px;
	border: 0px;
	background-color: transparent;
	cursor: pointer;
	margin:0px 5px 0 0
}
.tp_screenshot:hover{
	background-image: url(../images/screenshot_hover.png);
}

.star_score {
	width:85px;
	height:17px;
	position:relative;
}
.star_score a{
	height:17px;
	display:block;
	text-indent:-999em;
	position:absolute;left:0;
}
.star_score {
	background:url(../images/starky.png);
}
.star_score a:hover{
	background:url(../images/starsy.png);
	left:0;
}
.star_score a.clibg{
	background:url(../images/starsy.png);
	left:0;
}

.star_score1 {
	width:85px;
	height:17px;
	position:relative;
}
.star_score1 a{
	height:17px;
	display:block;
	text-indent:-999em;
	position:absolute;
	left:0;
}
.star_score1 {
	background:url(../images/starky.png);
}
.star_score1 a.clibg{
	background:url(../images/starsy.png);
	left:0;
}
.suggest_input{
	border:0px;
	background-color:transparent;
	color:#46545d;
	font-size: 14px;
	width:100%;
}
.failure_btn{
	background-color:#dfdfdf;
	background-image: -webkit-linear-gradient(top, #dfdfdf 0%, #dfdfdf 100%);
  	background-image: -moz-linear-gradient(top, #dfdfdf 0%, #dfdfdf 100%);
  	background-image: -o-linear-gradient(top, #dfdfdf 0%, #dfdfdf 100%);
  	background-image: linear-gradient(to bottom, #dfdfdf 0%, #dfdfdf 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #dfdfdf;
	margin:5px 7px;
}
.failure_btn:hover{
	background-color:#e0e0e0;
	background-image: -webkit-linear-gradient(top, #e0e0e0 0%, #e0e0e0 100%);
  	background-image: -moz-linear-gradient(top, #e0e0e0 0%, #e0e0e0 100%);
  	background-image: -o-linear-gradient(top, #e0e0e0 0%, #e0e0e0 100%);
  	background-image: linear-gradient(to bottom, #e0e0e0 0%, #e0e0e0 100%);
  	border:1px solid #e0e0e0;
  	color:#fff;
	}
.failure_btn:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}

.general_attribute{
	width:18px;
	height: 18px;
	display: block;
	float: left;
}
.normal_green{
	background-color: #22b14c
}
.warning_yellow{
	background-color: #fff200
}
.error_orange{
	background-color: #ff7f27
}
.emergency_red{
	background-color: #ff0000
}
.uninspected_black{
	background-color: #000000
}
.noping_gray{
	background-color: #747474
}

.handbook_title{
	text-align:center;
	padding-top:15px;
	padding-bottom:5px;
}
.handbook_title h1{
	font-size:36px;
	font-weight:bold;
	color:#666666;
	padding:0px;
	margin:0px;
}
.toolbar_box.x-toolbar{
	font-size:16px;
	margin-left:20px;
	margin-top:10px;
}
.search_box{
	margin-left:20px;
}
.instruction_box .primary_title{
	margin-top:20px;
	margin-bottom:20px;
}
.instruction_box .primary_title .x-toolbar-text{
	font-size:18px;
}
.instruction_box .explain_cn .x-toolbar-text{
	font-size:16px;
	margin-left:15px;
	line-height:30px;
}
#descVal{
	font-size:16px;
	margin-left:15px;
	line-height:30px;
}
.instruction_box .case_cn.x-toolbar{
	font-size:16px;
	margin-left:15px;
}
.describe_box .x-form-text{
	width:100%;
	border-radius:4px !important;
	background-color:#fafafa !important;
}
.describe_box.x-toolbar-default{
	margin-top:10px;
	margin-bottom:10px;
}

.switch-btn{
	width: 65px;
	height: 25px;
	position: relative;
	top: -5px;
}
.hidden-checkbox,
.switch-area,
.switch-toggle{
	position: absolute;
	top: 0;
	left: 0;
}
.hidden-checkbox{
	width: 65px;
	height: 25px;
	opacity: 0;
	z-index: 10;
	cursor: pointer;
}
.switch-area{
	width: 100%;
	height: 100%;
	border-radius: 25px;
	background-color: #B3B3B3;
}
.switch-toggle{
	width: 25px;
	height: 25px;
	border: 1px solid #B3B3B3;
	border-radius: 50%;
	background-color: #fff;
}
.hidden-checkbox:checked ~ .switch-area{
	background-color: #4084F1;
}
.hidden-checkbox:checked ~ .switch-toggle{
	border: 1px solid #4084F1;
	left: 45px;
}
.switch-area,
.switch-toggle{
	-webkit-transition: All 0.3s ease;
	-moz-transition: All 0.3s ease;
	-o-transition: All 0.3s ease;
	transition: All 0.3s ease;
}
.switch-area:before{
	position:absolute;
	content:"关闭";
	top:6px;
	left: 30px;
	font-size: 12px;
	color: white;
}
.hidden-checkbox:checked ~ .switch-area:before{
	position:absolute !important;
	content:"开启" !important;
	top:6px !important;
	left:13px !important;
	font-size: 12px !important;
	color: white !important;
}
.warn_01{
	background-image: url(../images/warn_01.png);
	width:20px;
	height:20px;
}
.warn_02{
	background-image: url(../images/warn_02.png);
	width:20px;
	height:20px;
}
.warn_03{
	background-image: url(../images/warn_03.png);
	width:20px;
	height:20px;
}
.warn_04{
	background-image: url(../images/warn_04.png);
	width:20px;
	height:20px;
}
.warn_05{
	background-image: url(../images/warn_05.png);
	width:20px;
	height:20px;
}

.monitor_img{
	background-image:url(../images/monitor.png); 
	width:50px; 
	height:50px; 
	float:left;
	margin:35px 10px 0 0;
	}
.monitor_text{ 
	font-size:16px; 
	display:block;
	font-weight:normal;
	}
.monitor_title{
	margin:32px 0 0 0;
}
.tp_border3 .tp_help{
	background-image:url(../images/help.png);
	width:18px;
	height:18px;
	margin-left:6px;
	margin-top:-3px;
}

.tp_border3:hover .tp_help{
	background-image:url(../images/help_over.png);
	width:18px;
	height:18px;
}
/* 设置了浏览器宽度小于1600px时的滚动文字样式 */
@media screen and (max-width: 1600px) {
	.hd_top #scrollText{
		width:300px !important;
	}
}
/* 设置了浏览器宽度小于1366px时的滚动文字样式 */
@media screen and (max-width: 1367px) {
	.hd_top #scrollText{
		width:200px !important;
	}
}
.role_trendsIcon{
	background-image: url(../images/cicd_icon06.png);
	width:16px;
	height:16px;
	background-color:transparent;
}
.role_itemsIcon{
	background-image: url(../images/cicd_icon02.png);
	width:16px;
	height:16px;
	background-color: transparent;
}
.role_executesIcon{
	background-image: url(../images/cicd_icon03.png);
	width:16px;
	height:16px;
	background-color: transparent;
}
.pro_icon_bg{
	background-color:#99a6bb ;
	width:24px;
	height: 24px;
	border-radius: 100%;
	display: inline-flex;
	justify-content:center;
	align-items: center;
	cursor: pointer;
	position: relative;
	top: -4px;
}
.pro_icon_bg:hover{
	background-color:#007af5;
}
.pro_grid_bg .x-action-col-icon{
	background-color:#99a6bb ;
	width: 24px;
	height: 24px;
	border-radius:100%;
	position: relative;
	top: -4px;
	margin-right: 5px;
}
.pro_grid_bg .x-action-col-icon:hover{
	background-color:#007af5;
}
.pro_icon_bg input{
	border:0px;
}
.role_downloads{
	background-image: url(../images/cicd_icon10.png);
	width:24px;
	height:24px;
	background-repeat: no-repeat;
}
.monitor_upIcon{
	background-image: url(../images/cicd_icon09.png);
	width:24px;
	height:24px;
	background-repeat: no-repeat;
}
.role_removes{
	background-image: url(../images/cicd_icon08.png);
	width:24px;
	height:24px;
	background-repeat: no-repeat;
}
.pro_public{
	padding: 0 10px;
	height: 20px;
	display:inline-flex;
	align-items: center;
	color:#ffffff;
	font-size: 14px;
	border-radius: 30px;
	margin-left: 10px;
	position:relative;
	top: -2px;
}
.pro_red{
	background-color:#fd6e5e;
}
.pro_deepred{
	background-color:#ff011f;
}
.pro_green{
	background-color:#05c464;
}
.x-list-plain li{
	text-overflow:ellipsis;
	white-space:nowrap;
	overflow:hidden;
}

.cd_state_pub{
	width:24px;
	height:24px;
	background-image: url(../images/cd_state_pub.png);
	float:left;
	display:block;
	margin:6px 10px 0 10px;
}
.cd_state_text{
	float:left;
}
.cd_state_green{
	background-position:0 0
}
.cd_state_blue{
	background-position:0 -24px
}
.cd_state_gray{
	background-position:0 -48px
}
.cd_state_red{
	background-position:0 -72px
}
.cd_list_orange_skip{
	background-position:0 -96px
}
.cd_state_yellow{
	background-position:0 -120px
}
.cd_state_pause{
	background-position:0 -144px
}
.cd_state_mismatching{
	background-position:0 -168px
}
