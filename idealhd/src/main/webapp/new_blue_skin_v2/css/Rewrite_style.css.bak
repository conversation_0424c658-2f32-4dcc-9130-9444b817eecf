.customize_body{
	background:#ebeff5;
	color: #585a69;
	font-size: 14px;
	font-family: arial,Microsoft Yahei;
}
.customize_body .x-accordion-layout-ct{
	padding: 0
}
.customize_center{
	background:#f7f8fa;
}
.customize_body .x-panel-body-default{
	background:transparent;
	border-color: #dddfed;
	color: #46545d;
	font-size: 14px;
	font-size: normal;
	border-width: 0px;
	border-style: solid;
}
.customize_body .x-panel-header-text-container-default{
	color:#46545d;
	font-size: 18px;
	font-weight: normal;
	font-family: arial,Microsoft Yahei;
	padding: 0 0 0;
	text-transform: none;
	line-height:26px;
}
.customize_body .x-panel-header-icon{
	width: 0px;
	height: 16px;
	background-position: center center
}
.customize_body .x-panel-header-default-horizontal {
	padding: 0 10px 10px 0
}
.customize_body .x-panel-header-icon-before-title{
	margin: 0 10px 0 0
}
.customize_body .x-form-text {
	padding: 4px 0 3px 10px;
	color:#46545d;
	background: #ffffff repeat-x 0 0;
	border-width: 1px;
	border-style: solid;
	border-color:#d9d9d9;
	height: 34px;
	border-radius:2px;
	line-height: 34px;
}
@media screen and(-ms-high-contrast:active),(-ms-high-contrast:none){
	.customize_body .x-form-text {line-height:16px;}
}
.customize_body .x-form-file-wrap .x-form-trigger-wrap {
	border: 0
}
.customize_body .x-form-file-wrap .x-form-trigger-wrap .x-form-text {
	border: 1px solid;
	border-color: #dddfeb #dddfeb #dddfeb;
	height:38px
}
.customize_body .x-form-item, .x-form-field {
	font: normal 14px arial,Microsoft Yahei
}
.customize_body .x-form-empty-field, textarea.x-form-empty-field{
	color:#46545d;
}
.customize_body .x-btn-default-toolbar-small .x-btn-inner{
	font-size: 12px;
	font-weight:normal;
	font-family: arial,Microsoft Yahei;
	color: #fff;
	padding: 0 5px
}
.customize_body .x-column-header{
	border-right: 0px solid transparent;
	color: #929ea8;
	font: normal 14px/15px arial,Microsoft Yahei;
	background-color: #ffffff
}

.customize_body .x-form-checkbox {
	width: 15px;
	height: 15px;
	background: url(../rewrite_images/form/checkbox.png) no-repeat
}

.customize_body .x-form-cb-checked .x-form-checkbox {
	background-position: 0 -15px
}

.customize_body .x-form-checkbox-focus {
	background-position: -15px 0
}

.customize_body .x-form-cb-checked .x-form-checkbox-focus {
	background-position: -15px -15px
}

.customize_body .x-grid-row-checker, .x-column-header-checkbox .x-column-header-text {
	height: 15px;
	width: 15px;
	background-image: url(../rewrite_images/form/checkbox.png);
	line-height: 15px
}
.customize_body .x-form-item-label{
	color:#46545d;
	font: normal 14px/17px arial,Microsoft Yahei;
	margin-top: 9px
}
.customize_body .x-grid-cell-inner{
	text-overflow: ellipsis;
	padding:11px 0 0 5px;
	height:38px;
	line-height:100%;
}
.customize_body .x-grid-cell{
	color: null;
	font: normal 14px/15px arial,Microsoft Yahei;
	background-color: #fafafa;
	border-color: #ffffff;
	border-style: solid
}
.customize_body .x-grid-with-row-lines .x-grid-td {
	border-bottom-width:4px
}
.customize_body .x-grid-row-alt .x-grid-td {
	background-color: #fafafa
}
.customize_body .x-grid-row-before-over .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.customize_body .x-grid-row-over .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.customize_body .x-grid-row-before-focused .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.customize_body .x-grid-row-focused .x-grid-td {
	background-color: transparent;
}

.customize_body .x-grid-row-over .x-grid-td {
	background-color:#eff7fe
}
.customize_body .x-grid-row-focused .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.customize_body .x-grid-with-row-lines .x-grid-row-focused-first .x-grid-td {
	border-top: 1px solid #ffffff;
}
.customize_body .x-grid-row-focused .x-grid-row-summary .x-grid-td {
	border-bottom-color: #ffffff;
	border-top-width: 0
}
.customize_body .x-grid-with-row-lines .x-grid-table-over-first {
	border-top-style: solid;
	border-top-color: #ffffff
}
.customize_body .x-grid-with-row-lines .x-grid-table-focused-first {
	border-top-style: solid;
	border-top-color: #ffffff
}

.customize_body .x-panel-default-framed {
	border-color: #ffffff;
	padding: 0
}
.customize_body .x-panel-body-default-framed {
	background: white;
	border-color: #ffffff;
	color:#585a69;
	font-size: 14px;
	font-size: normal;
	border-width: 1px;
	border-style: solid
}
.customize_body .x-panel-default {
	border-color: #dddddd;
	padding: 0
}

.customize_body .x-grid-row-before-selected .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.customize_body .x-grid-row-selected .x-grid-td {
	border-bottom-style: solid;
	border-bottom-color: #ffffff
}
.customize_body .x-grid-row-selected .x-grid-td {
	background-color: #e0effe
}
.customize_body .x-grid-row-selected .x-grid-row-summary .x-grid-td {
	border-bottom-color: #ffffff;
	border-top-width: 0
}
.customize_body .x-grid-with-row-lines .x-grid-table-selected-first {
	border-top-style: solid;
	border-top-color: #ffffff
}
.customize_body .x-grid-row .x-grid-cell-selected {
	color: null;
	background-color: #e3ebff
}
.customize_body .x-column-header-over, .x-column-header-sort-ASC,
	.x-column-header-sort-DESC {
	background-image: none;
	background-color: #ffffff
}
.customize_body .x-column-header-over{
	color:#2d3039;
	}
.customize_body .x-column-header-trigger {
	background-image: url(../rewrite_images/grid/hd-pop.png);
	border-left: 0px solid #dddfed
}
.customize_body .x-column-header-open .x-column-header-text {
	color:#2d3039;
}
.customize_body .x-column-header-open .x-column-header-trigger {
	background-color: #ffffff
}
.customize_body .x-column-header-open{
	background-color: #ffffff
}


.customize_body .x-panel-header-default {
	background-image: none;
	background-color: #ffffff;
	border-radius:6px 6px 0 0;
}
.customize_body .x-panel-header-default {
	font-size: 14px;
	border: 0px solid #d9d9d9
}

.customize_body .x-column-header-inner {
	text-align:left;
	text-overflow: ellipsis;
	padding:0 0 0 4px;
	padding-top:19px !important;
}
.customize_body .x-grid-with-col-lines .x-grid-cell{
	border-right-width: 0px
}
.customize_body .x-toolbar-text{
	margin: 0 6px 0 4px;
	color: #46545d;
	line-height: 16px;
	font-family: arial,Microsoft Yahei;
	font-size: 14px;
	font-weight: normal
}
.customize_body .x-toolbar-default {
	border-color: #dddfed;
	border-width: 0px;
	background-image: none;
	background-color: white;
	padding:0;
}
#package_bottom .x-toolbar-default {
	border-color: #dddfed;
	border-width: 0px;
	background-image: none;
	background-color: transparent
}
.customize_body .x-toolbar-separator-horizontal {
	margin: 0 8px 0 0;
	height: 14px;
	border-style: solid;
	border-width: 0 0 0 0px;
	border-left-color: #dddfed;
	border-right-color: #dddfed
}
.customize_body .x-grid-header-ct{
	border: 0px solid #ffffff;
	border-bottom-color: #ffffff;
	background-color: #ffffff;
	z-index: 0;
}
.customize_body .x-grid-view {
	overflow: hidden;
	position: relative;
	background-color: #ffffff;
}
.customize_body .x-btn-default-toolbar-small-focus {
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
}
.customize_body .x-btn-default-toolbar-small-focus .x-btn-inner{
	color:#ffffff;
}
.customize_body .x-btn-default-toolbar-small-menu-active,
	.x-btn-default-toolbar-small-pressed {
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5)
}
.customize_body .x-btn-default-toolbar-small{
	
}
.customize_body .x-unselectable {
	user-select: none;
	-o-user-select: none;
	-ms-user-select: none;
	-moz-user-select: -moz-none;
	-webkit-user-select: none;
	/*cursor: default*/
}

/*radio*/
.customize_body .x-form-radio {
	width: 15px;
	height: 15px;
	background: url(../rewrite_images/form/radio.png) no-repeat
}

.customize_body .x-form-cb-checked .x-form-radio {
	background-position: 0 -15px
}

.customize_body .x-form-radio-focus {
	background-position: -15px 0
}

.customize_body .x-form-cb-checked .x-form-radio-focus {
	background-position: -15px -15px
}

.customize_body .x-form-trigger {
	cursor: pointer;
	overflow: hidden;
	background-repeat: no-repeat
}
.customize_body .x-form-trigger {
	background: url(../rewrite_images/form/trigger.png);
	width: 22px
}
.customize_body .x-form-trigger {
	height: 32px;
	background-position: -3px 0
}
.customize_body .x-form-trigger-over {
	background-position: -25px 0
}

.customize_body .x-form-trigger-wrap-focus .x-form-trigger {
	background-position: -91px 0
}

.customize_body .x-form-trigger-wrap-focus .x-form-trigger-over {
	background-position: -91px 0
}

.customize_body .x-form-trigger-click, .x-form-trigger-wrap-focus .x-form-trigger-click
	{
	background-position: -47px 0
}
.customize_body .x-form-trigger-wrap{
	vertical-align: top;
	border-collapse: separate
}
.customize_body .x-form-trigger-wrap {
	border: 1px solid;
	border-color: #d9d9d9;
	border-radius:2px;
	background-color:#ffffff;
}
.customize_body .x-form-trigger-wrap .x-form-text {
	border-width: 0;
	height: 34px
}
.customize_body .x-trigger-cell {
	background-color: #ffffff;
	width: 22px
}
.customize_body .x-form-spinner-up{
	background-image: url(../rewrite_images/form/spinner.png);
	background-color:#ffffff;
	width: 22px;
	height: 17px;
	background-position: 3px 0;
}
.customize_body .x-form-spinner-down {
	background-image: url(../rewrite_images/form/spinner.png);
	background-color:#ffffff;
	width: 22px;
	height: 17px;
	background-position: 3px 0;
}
.customize_body .x-form-spinner-down {
	background-position: 3px -17px
}

.customize_body .x-form-trigger-wrap-focus .x-form-spinner-down {
	background-position: -63px -17px
}
.customize_body .x-form-trigger-wrap-focus .x-form-spinner-up {
	background-position: -63px 0
}
.customize_body .x-form-trigger-wrap .x-form-spinner-down-over {
	background-position: -19px -17px
}

.customize_body .x-form-trigger-wrap-focus .x-form-spinner-down-over {
	background-position: -85px -17px
}

.customize_body .x-form-trigger-wrap .x-form-spinner-down-click {
	background-position: -41px -17px
}
.customize_body .x-form-trigger-wrap .x-form-spinner-up-click {
	background-position: -41px 0
}
.customize_body .customize_toolbar .fm-spinner .x-form-spinner-up{
	background-image: url(../rewrite_images/form/spinner_toolbar.png);
	background-color:#ffffff;
	width: 22px;
	height: 12px
}
.customize_body .customize_toolbar .fm-spinner .x-form-spinner-down {
	background-image: url(../rewrite_images/form/spinner_toolbar.png);
	background-color:#ffffff;
	width: 22px;
	height: 12px
}
.customize_body .customize_toolbar .fm-spinner .x-form-spinner-down {
	background-position: 0 -12px
}
.customize_body .customize_toolbar .fm-spinner .x-form-trigger-wrap-focus .x-form-spinner-down {
	background-position: -66px -12px
}
.customize_body .customize_toolbar .fm-spinner .x-form-trigger-wrap .x-form-spinner-down-over {
	background-position: -22px -12px
}
.customize_body .customize_toolbar .fm-spinner .x-form-trigger-wrap-focus .x-form-spinner-down-over {
	background-position: -88px -12px
}
.customize_body .customize_toolbar .fm-spinner .x-form-trigger-wrap .x-form-spinner-down-click {
	background-position: -44px -12px
}

.customize_body .x-form-date-trigger {
	background-image: url(../rewrite_images/form/date-trigger.png)
}

.customize_body .x-column-header-sort-ASC .x-column-header-text {
	background-image: url(../rewrite_images/grid/sort_asc-normal.png)
}
.customize_body .x-column-header-sort-DESC .x-column-header-text {
	background-image: url(../rewrite_images/grid/sort_desc-normal.png)
}

.customize_body .x-column-header-sort-ASC .x-column-header-over .x-column-header-text{
	background-image: url(../rewrite_images/grid/sort_asc.png)
}
.customize_body .x-column-header-sort-DESC .x-column-header-over .x-column-header-text{
	background-image: url(../rewrite_images/grid/sort_desc.png)
}

.customize_body .x-column-header-sort-ASC .x-column-header-open .x-column-header-text{
	background-image: url(../rewrite_images/grid/sort_asc.png)
}
.customize_body .x-column-header-sort-DESC .x-column-header-open .x-column-header-text{
	background-image: url(../rewrite_images/grid/sort_desc.png)
}

.customize_body .x-grid-checkcolumn {
	width: 15px;
	height: 15px;
	background: url(../rewrite_images/form/checkbox.png) 0 0 no-repeat
}
.customize_body .x-item-disabled .x-grid-checkcolumn {
	filter: alpha(opacity = 30);
	opacity: .3
}
.customize_body .x-grid-checkcolumn-checked {
	background-position: 0 -15px
}
.customize_body .x-btn-default-small {
	border-color: #007af5
}

.customize_body .x-btn-default-small {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding:8px 16px 8px 16px;
	border-width: 1px;
	border-style: solid;
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	cursor:pointer;
}
.customize_body .x-btn-default-small-over {
	border-color: #0067ce;
	background-image: none;
	background-color: #0067ce;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #0067ce),
		color-stop(50%, #0067ce), color-stop(51%, #0067ce),
		color-stop(100%, #0067ce));
	background-image: -webkit-linear-gradient(top, #0067ce, #0067ce 50%, #0067ce 51%, #0067ce);
	background-image: -moz-linear-gradient(top, #0067ce, #0067ce 50%, #0067ce 51%, #0067ce);
	background-image: -o-linear-gradient(top, #0067ce, #0067ce 50%, #0067ce 51%, #0067ce);
	background-image: linear-gradient(top, #0067ce, #0067ce 50%, #0067ce 51%, #0067ce);
	cursor:pointer;
}
.customize_body .x-btn-default-small-focus {
	border-color: #007af5;
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	cursor:pointer;
}

.customize_body .x-btn-default-small-menu-active, .x-btn-default-small-pressed {
	border-color: #bfbfbf;
	background-image: none;
	background-color: #bfbfbf;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #bfbfbf),
		color-stop(50%, #bfbfbf), color-stop(51%, #bfbfbf),
		color-stop(100%, #bfbfbf));
	background-image: -webkit-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: -moz-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: -o-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	cursor:pointer;
}
.customize_body .x-btn-default-small-menu-active, .x-btn-default-small-pressed .x-btn-inner{
	color:#ffffff;
}

.customize_body .x-toolbar-footer {
	background: transparent;
	border: 0;
	margin: 0;
	padding: 6px 0 6px 6px
}
.customize_body .x-btn-default-toolbar-small .x-btn-split-right {
	background-image: url(../rewrite_images/button/default-toolbar-small-s-arrow.png);
	padding-right: 23px
}
.customize_body .x-btn-default-toolbar-small .x-btn-split-bottom {
	background-image: url(../rewrite_images/button/default-toolbar-small-s-arrow-b.png);
	padding-bottom: 20px
}
.customize_body .x-btn-split {
	display: block;
	background-repeat: no-repeat
}
.customize_body .x-btn-split-right {
	background-position: right center
}

.customize_body .x-tab-bar-default {
	background-color: #ffffff
}
.customize_panel_gray .x-tab-bar-default{background-color: #ebeff5}
.customize_body .x-tab-bar-strip-default-top {
	border-width: 0;
	height: 0px
}
.customize_body .x-tab-bar-strip-default{
	border-style: solid;
	border-color: #ffffff;
	background-color: #ffffff
}
.customize_body .x-tab {
	display: block;
	white-space: nowrap;
	z-index: 1
}

.customize_body .x-tab-active {
	z-index: 3
}

.customize_body .x-tab-wrap {
	display: block;
	position: relative
}

.customize_body .x-tab-button {
	zoom: 1;
	display: block;
	outline: 0
}

.customize_body .x-tab-inner {
	display: block;
	text-align: center;
	white-space: nowrap;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	overflow: hidden;
	zoom: 1
}

.customize_body .x-btn-icon-el {
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	position: absolute;
	background-repeat: no-repeat;
	text-align: center
}

.customize_body .x-tab-bar {
	z-index: 1
}

.customize_body .x-tab-bar-body {
	z-index: 2;
	position: relative;
	padding-left:8px;
	padding-right:0px;
	padding-top:0px;
	padding-bottom:0px;
}
.customize_body .x-window-default .x-tab-bar-body{
	padding-left:0px;
	padding-right:0px;
	padding-top:0px;
	padding-bottom:0px;
}

.customize_body .x-tab-bar-strip {
	position: absolute;
	line-height: 0;
	font-size: 0;
	z-index: 1
}

.customize_body .x-tab-bar-horizontal .x-tab-bar-strip {
	width: 100%;
	left: 0
}

.customize_body .x-tab-bar-vertical .x-tab-bar-strip {
	height: 100%;
	top: 0
}

.customize_body .x-tab-bar-strip-top {
	bottom: 0
}

.customize_body .x-tab-bar-strip-bottom {
	top: 0
}

.customize_body .x-tab-bar-strip-left {
	right: 0
}

.customize_body .x-tab-bar-strip-right {
	left: 0
}

.customize_body .x-tab-bar-plain {
	background: transparent !important
}

.customize_body .x-tab-icon-el {
	position: absolute;
	background-repeat: no-repeat;
	top: 0;
	left: 0;
	right: auto;
	bottom: 0
}

.customize_body .x-tab-close-btn {
	display: block;
	position: absolute;
	font-size: 0;
	line-height: 0;
	background: no-repeat
}

.customize_body .x-tab-mc {
	overflow: visible
}
.customize_body .x-tab-default-top{
	-moz-border-radius-topleft: 2px;
	-webkit-border-top-left-radius: 2px;
	border-top-left-radius: 2px;
	-moz-border-radius-topright: 2px;
	-webkit-border-top-right-radius: 2px;
	border-top-right-radius: 2px;
	-moz-border-radius-bottomright: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
	-moz-border-radius-bottomleft: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
	padding: 11px 0 11px 0;
	border-width: 0;
	border-style: solid;
	background-color: transparent;
	margin-left:12px;
	margin-right:12px;
}
.customize_body .x-tab-default .x-tab-inner {
	font-size: 16px;
	font-weight: normal;
	font-family: arial,Microsoft Yahei;
	color:#585a69;
	line-height: 22px;
	cursor:pointer
}
.customize_body .x-tab-default .x-tab-inner:hover{
	color: #6c8bff;
}
.customize_body .x-tab-default-active {
	background-color: transparent;
	border-bottom:2px solid #007af5;
}
.customize_body .x-tab-default-over {
	background-color: transparent;
	color: #6c8bff
}
.customize_body .x-tab-default-over .x-tab-glyph {
	color: #6c8bff
}
.customize_body .x-tab-default-active .x-tab-inner {
	color: #007af5;
	font-weight:normal;
}
.customize_body .x-horizontal-box-overflow-body {
	float: left;
	border-bottom:0px solid #d9d9d9;
}



.customize_body .x-btn-split-bottom {
	background-position: center bottom
}
.customize_body .x-panel-header-default .x-tool-img {
	background-color: transparent
}
.customize_body .x-tool {
	cursor: pointer
}
/*.customize_body .x-tool-img {
	overflow: hidden;
	width: 16px;
	height: 16px;
	background-image: url(../rewrite_images/tools/tool-sprites.png);
	margin: 0
}*/

.customize_body .x-tool .x-tool-img {
	filter: alpha(opacity = 100);
	opacity: 1
}

.customize_body .x-tool-over .x-tool-img {
	filter: alpha(opacity = 60);
	opacity: .6
}

.customize_body .x-tool-pressed .x-tool-img {
	filter: alpha(opacity = 70);
	opacity: .7
}
.customize_body .x-window-header-default .x-tool-img {
	background-color: transparent
}
.customize_body .x-tool-placeholder {
	visibility: hidden
}
.customize_body .x-tool-close {
	background-image: url(../rewrite_images/tools/x-tool-close.png);
	width: 20px;
	height: 20px;
	background-size:100% 100%;
}

.customize_body .x-tool-minimize {
	background-position: 0 -16px
}

.customize_body .x-tool-maximize {
	background-image: url(../rewrite_images/tools/x-tool-maximize.png);
	width: 20px;
	height: 20px;
	background-position: 0 0;
}

.customize_body .x-tool-restore {
	background-image: url(../rewrite_images/tools/x-tool-restore.png);
	width: 20px;
	height: 20px;
	background-position: 0 0;
}

.customize_body .x-tool-toggle {
	background-position: 0 -64px
}

.customize_body .x-panel-collapsed .x-tool-toggle {
	background-position: 0 -80px
}

.customize_body .x-tool-gear {
	background-position: 0 -96px
}

.customize_body .x-tool-prev {
	background-position: 0 -112px
}

.customize_body .x-tool-next {
	background-position: 0 -128px
}

.customize_body .x-tool-pin {
	background-position: 0 -144px
}

.customize_body .x-tool-unpin {
	background-position: 0 -160px
}

.customize_body .x-tool-right {
	background-position: 0 -176px
}

.customize_body .x-tool-left {
	background-position: 0 -192px
}

.customize_body .x-tool-down {
	background-position: 0 -208px
}

.customize_body .x-tool-up {
	background-position: 0 -224px
}

.customize_body .x-tool-refresh {
	background-position: 0 -240px
}
.customize_body .x-tool-plus {
	background-position: 0 0px
}

.customize_body .x-tool-plus {
	background-image: url(../rewrite_images/tools/x-tool-plus.png);
	width:20px;
	height:20px;
}

.customize_body .x-tool-minus {
	background-image: url(../rewrite_images/tools/x-tool-minus.png);
	width:20px;
	height:20px;
}

.x-tool-minus {
	background-position: 0 0px
}
.customize_body .x-tool-search {
	background-position: 0 -288px
}

.customize_body .x-tool-save {
	background-position: 0 -304px
}

.customize_body .x-tool-help {
	background-image: url(../rewrite_images/tools/x-tool-help.png);
	width:20px;
	height:20px;
}

.customize_body .x-tool-print {
	background-image: url(../rewrite_images/tools/x-tool-print.png);
	width:20px;
	height:20px;
}
.customize_body .x-tool-print {
	background-position: 0 0px
}

.customize_body .x-tool-expand {
	background-position: 0 -352px
}

.customize_body .x-tool-collapse {
	background-position: 0 -368px
}

.customize_body .x-tool-resize {
	background-position: 0 -384px
}

.customize_body .x-tool-move {
	background-position: 0 -400px
}


.customize_body .x-tool-expand-bottom, .x-tool-collapse-bottom {
	background-image: url(../rewrite_images/tools/x-tool-collapse-bottom.png);
	width:20px;
	height:20px;
	background-position: 0 0;
}

.customize_body .x-tool-collapse-top {
	background-image: url(../rewrite_images/tools/x-tool-collapse-top.png);
	width:20px;
	height:20px;
	background-position: 0 0;
}

.customize_body .x-tool-collapse-left {
	background-image: url(../rewrite_images/tools/x-tool-collapse-left.png);
	width:20px;
	height:20px;
	background-position: 0 0;
}

.customize_body .x-tool-collapse-right {
	background-image: url(../rewrite_images/tools/x-tool-collapse-right.png);
	width:20px;
	height:20px;
	background-position: 0 0;
}

.customize_body .x-tool-expand-left, .x-tool-collapse-left {
	background-image: url(../rewrite_images/tools/x-tool-collapse-left.png);
	width:20px;
	height:20px;
	background-position: 0 0;
}

.customize_body .x-tool-expand-right, .x-tool-collapse-right {
	background-image: url(../rewrite_images/tools/x-tool-collapse-right.png);
	width:20px;
	height:20px;
	background-position: 0 0;
}

.customize_body .x-window-body-default {
	border-color: #ffffff;
	border-width: 1px;
	border-style: solid;
	background: white;
	color: #585a69
}
.customize_body .x-window-default {
	border-color: #5168fc;
	-webkit-border-radius: 6px;
	-moz-border-radius: 6px;
	-ms-border-radius: 6px;
	-o-border-radius: 6px;
	border-radius: 6px
}

.customize_body .x-window-default {
	-webkit-border-radius: 6px;
	-moz-border-radius: 6px;
	-ms-border-radius: 6px;
	-o-border-radius: 6px;
	border-radius: 6px;
	padding: 0 17px 20px 19px;
	border-width: 0px;
	border-style: solid;
	background-color: white
}

.customize_body .x-window-default-mc {
	background-color: white
}
.customize_body .x-window-header-text-container-default {
	color: #46545d;
	font-weight:bold;
	line-height:22px;
	font-family:arial,Microsoft Yahei;
	font-size: 18px;
	padding: 0 0 0;
	text-transform: none
}

.customize_body .x-window-header-default-top {
	-moz-border-radius-topleft: 6px;
	-webkit-border-top-left-radius: 6px;
	border-top-left-radius: 6px;
	-moz-border-radius-topright: 6px;
	-webkit-border-top-right-radius: 6px;
	border-top-right-radius: 6px;
	-moz-border-radius-bottomright: 0;
	-webkit-border-bottom-right-radius: 0;
	border-bottom-right-radius: 0;
	-moz-border-radius-bottomleft: 0;
	-webkit-border-bottom-left-radius: 0;
	border-bottom-left-radius: 0;
	padding: 22px 19px 12px 20px;
	border-width: 0px;
	border-style: solid;
	background-color: #5168fc
}

.customize_body .x-window-header-default-top-mc {
	background-color: #f7f8fa
}
.customize_body .x-window-header-default {
	font-size: 14px;
	border-color: #ffffff;
	zoom: 1;
	background-color: #ffffff;
	border-width: 0px !important;
	text-align:center;
}
.customize_body .x-toolbar-footer {
	background: #ffffff;
	border: 0;
	margin: 0;
	padding: 6px 0 6px 6px
}
.customize_body .x-form-display-field {
	font: normal 14px/17px arial,Microsoft Yahei;
	color: #585a69;
	margin-top: 4px;
	word-break:break-all;
}
/*tree*/
.customize_body .x-tree-checkbox {
	margin-right: 4px;
	top:10px;
	width: 15px;
	height: 15px;
	background-image: url(../rewrite_images/form/tree_checkbox.png)
}

.customize_body .x-tree-checkbox-checked {
	background-position: 0 -15px
}
.customize_body .x-tree-checkbox-no-checked {
	background-position: 0 -30px
}
.customize_body .x-tree-expander {
	cursor: pointer
}
.customize_body .x-tree-arrows .x-tree-expander {
	background-image: url(../rewrite_images/tree/arrows.png)
}

.customize_body .x-tree-icon-parent {
	background-image: url(../rewrite_images/tree/folder.png)
}

.customize_body .x-grid-tree-node-expanded .x-tree-icon-parent {
	background-image: url(../rewrite_images/tree/folder-open.png)
}

.customize_tree .x-grid-row, .x-grid-data-row {
	outline: 0;
	background-color: #ffffff
}
.customize_tree .x-grid-cell-inner{
	text-overflow: ellipsis;
	padding:5px 0 14px 5px;
}
.customize_tree .x-tree-elbow-img, .x-tree-icon {
	background-repeat: no-repeat;
	background-position: 0 center;
	vertical-align: top
}
.customize_tree .x-tree-arrows .x-tree-expander-over .x-tree-expander {
	background-position: -32px center
}

.customize_tree .x-tree-arrows .x-grid-tree-node-expanded .x-tree-expander {
	background-position: -16px center
}

.customize_tree .x-tree-arrows .x-grid-tree-node-expanded .x-tree-expander-over .x-tree-expander
	{
	background-position: -48px center
}

.customize_tree .x-tree-elbow {
	background-image: url(../rewrite_images/tree/elbow.png)
}

.customize_tree .x-tree-elbow-end {
	background-image: url(../rewrite_images/tree/elbow-end.png)
}

.customize_tree .x-tree-elbow-plus {
	background-image: url(../rewrite_images/tree/elbow-plus.png)
}

.customize_tree .x-tree-elbow-end-plus {
	background-image: url(../rewrite_images/tree/elbow-end-plus.png)
}

.customize_tree .x-grid-tree-node-expanded .x-tree-elbow-plus {
	background-image: url(../rewrite_images/tree/elbow-minus.png)
}

.customize_tree .x-grid-tree-node-expanded .x-tree-elbow-end-plus {
	background-image: url(../rewrite_images/tree/elbow-end-minus.png)
}

.customize_tree .x-tree-elbow-line {
	background-image: url(../rewrite_images/tree/elbow-line.png)
}

.customize_tree .x-tree-no-row-lines .x-tree-expander {
	background-image: url(../rewrite_images/tree/elbow-plus-nl.png)
}

.customize_tree .x-tree-no-row-lines .x-grid-tree-node-expanded .x-tree-expander {
	background-image: url(../rewrite_images/tree/elbow-minus-nl.png)
}
.customize_tree .x-tree-icon {
	width: 16px;
	height: 38px
}
.customize_tree .x-tree-elbow-img {
	width: 18px;
	height: 38px;
	margin-right: 2px
}
.customize_tree .x-tree-icon, .x-tree-elbow-img{
	margin-top: -5px;
	margin-bottom: -4px
}
.customize_tree .x-tree-node-text {
	font-size: 14px;
	line-height: 30px;
	color:#323a3d;
	padding-left: 4px
}
.customize_tree .x-grid-cell{
	color: null;
	font: normal 14px/15px arial,Microsoft Yahei;
	background-color: transparent;
	border-color: #ffffff;
	border-style: solid
}
.customize_tree .x-grid-row-selected .x-grid-td {
	background-color: #e0effe
}
.customize_tree .x-grid-row-over .x-grid-td {
	background-color: #e0effe
}
.customize_tree .x-tree-icon-leaf {
	background-image: url(../rewrite_images/tree/leaf.png)
}

/*group*/

.customize_body .x-group-by-icon {
	background-image: url(../rewrite_images/grid/group-by.png)
}

.customize_body .x-show-groups-icon {
	background-image: url(../rewrite_images/grid/group-by.png)
}

/*tip*/

.customize_body .x-tip-default {
	border-color: #dddfeb
}
.customize_body .x-tip-default {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding: 2px 2px 2px 2px;
	border-width: 1px;
	border-style: solid;
	background-color: #f7f8fa
}
.customize_body .x-tip-default-mc {
	background-color: #f7f8fa
}

/*message*/
.customize_body .x-message-box-info {
	background-image: url(../rewrite_images/shared/icon-info.png)
}

.customize_body .x-message-box-warning {
	background-image: url(../rewrite_images/shared/icon-warning.png)
}

.customize_body .x-message-box-question {
	background-image: url(../rewrite_images/shared/icon-question.png)
}

.customize_body .x-message-box-error {
	background-image: url(../rewrite_images/shared/icon-error.png)
}

/*date*/
.customize_body .x-btn-default-small .x-btn-inner {
	font-size: 14px;
	font-weight: normal;
	font-family: arial,Microsoft Yahei;
	color: white;
	padding: 0 5px
}
.customize_body .x-datepicker {
	border-width: 1px;
	border-style: solid;
	border-color: #dddfed;
	background-color: white;
	width: 222px
}

.customize_body .x-datepicker-header {
	padding: 0px 0px;
	text-align: center;
	background-image: none;
	background-color: #ffffff;
	border-bottom:1px solid #d9d9d9;
	margin:0 0 4px 0
}

.customize_body .x-datepicker-arrow {
	width: 8px;
	height: 12px;
	top: 11px;
	cursor: pointer;
	background-color: #ffffff;
	filter: alpha(opacity = 100);
	opacity: 1
}

.customize_body a.x-datepicker-arrow:hover {
	filter: alpha(opacity = 70);
	opacity: .7
}

.customize_body .x-datepicker-next {
	right: 20px;
	background-image: url(../rewrite_images/datepicker/arrow-right.png)
}

.customize_body .x-datepicker-prev {
	left: 20px;
	background-image: url(../rewrite_images//datepicker/arrow-left.png)
}

.customize_body .x-datepicker-month .x-btn, .x-datepicker-month .x-btn .x-btn-tc,
	.x-datepicker-month .x-btn .x-btn-tl, .x-datepicker-month .x-btn .x-btn-tr,
	.x-datepicker-month .x-btn .x-btn-mc, .x-datepicker-month .x-btn .x-btn-ml,
	.x-datepicker-month .x-btn .x-btn-mr, .x-datepicker-month .x-btn .x-btn-bc,
	.x-datepicker-month .x-btn .x-btn-bl, .x-datepicker-month .x-btn .x-btn-br
	{
	background: transparent;
	border-width: 0 !important
}

.customize_body .x-datepicker-month .x-btn-inner {
	color: #46545d
}

.customize_body .x-datepicker-month .x-btn-split-right {
	background-image: url(../rewrite_images/datepicker/month-arrow.png);
	padding-right: 10px
}

.customize_body .x-datepicker-column-header {
	width: 30px;
	color: #929ea8;
	font: 12px arial,Microsoft Yahei;
	text-align: right;
	background-image: none;
	background-color: white
}

.customize_body .x-datepicker-column-header-inner {
	line-height: 25px;
	padding: 0 9px 0 0
}

.customize_body .x-datepicker-cell {
	text-align: right;
	border-width: 1px;
	border-style: solid;
	border-color: white
}

.customize_body .x-datepicker-date {
	padding: 0 0 0 0;
	font: normal 12px arial,Microsoft Yahei;
	color: #46545d;
	cursor: pointer;
	line-height: 28px;
	border-radius:50%;
	text-align:center;
}

.customize_body a.x-datepicker-date:hover {
	color: #ffffff;
	background-color: #cce4fd
}

.customize_body .x-datepicker-selected {
	border-style: solid;
	border-color: #fff;
}

.customize_body .x-datepicker-selected .x-datepicker-date {
	background-color: #007af5;
	font-weight:normal;
	color:#fff;
}

.customize_body .x-datepicker-today {
	border-color: #fff;
	border-style: solid;
}

.customize_body .x-datepicker-prevday .x-datepicker-date, .x-datepicker-nextday .x-datepicker-date
	{
	color: #c1c1c1
}

.customize_body .x-datepicker-disabled a.x-datepicker-date {
	background-color: #e2e7fa;
	cursor: default;
	color: #3d404d
}

.customize_body .x-datepicker-disabled a.x-datepicker-date:hover {
	background-color: #cbd2ff
}

.customize_body .x-datepicker-footer, .x-monthpicker-buttons {
	padding: 14px 0 20px 0;
	background-image: none;
	background-color: #ffffff;
	text-align: center
}

.customize_body .x-datepicker-footer .x-btn, .x-monthpicker-buttons .x-btn {
	margin: 0 10px 0 10px
}

.customize_body .x-monthpicker {
	width: 222px;
	border-width: 1px;
	border-style: solid;
	border-color: #dddfed;
	background-color: white
}

.customize_body .x-monthpicker-months {
	border-width: 0 1px 0 0;
	border-color: #dddfed;
	border-style: solid;
	width: 105px
}

.customize_body .x-monthpicker-months .x-monthpicker-item {
	width: 52px
}

.customize_body .x-monthpicker-years {
	width: 105px
}

.customize_body .x-monthpicker-years .x-monthpicker-item {
	width: 52px
}

.customize_body .x-monthpicker-item {
	margin: 5px 0 5px;
	font: normal 12px arial,Microsoft Yahei;
	text-align: center
}

.customize_body .x-monthpicker-item-inner {
	margin: 0 5px 0 5px;
	color: #3d404d;
	border-width: 1px;
	border-style: solid;
	border-color: white;
	line-height: 26px;
	cursor: pointer
}

.customize_body a.x-monthpicker-item-inner:hover {
	background-color: #0263c6;
	color:#fff;
}

.customize_body .x-monthpicker-selected {
	background-color: #007af5;
	border-style: solid;
	border-color: #007af5;
	color:#fff;
}

.customize_body .x-monthpicker-yearnav {
	height: 34px
}

.customize_body .x-monthpicker-yearnav-button-ct {
	width: 52px
}

.customize_body .x-monthpicker-yearnav-button {
	height: 12px;
	width: 8px;
	cursor: pointer;
	margin-top: 11px;
	filter: alpha(opacity = 100);
	opacity: 1;
	background-color: white
}

.customize_body a.x-monthpicker-yearnav-button:hover {
	filter: alpha(opacity = 70);
	opacity: .7
}

.customize_body .x-monthpicker-yearnav-next {
	background-image: url(../rewrite_images//datepicker/arrow-right.png);
	background-position: 0 0
}

.customize_body .x-monthpicker-yearnav-next-over {
	background-position: 0 0
}

.customize_body .x-monthpicker-yearnav-prev {
	background-image: url(../rewrite_images//datepicker/arrow-left.png);
	background-position: 0 0
}

.customize_body .x-monthpicker-yearnav-prev-over {
	background-position: 0 0
}

.customize_body .x-monthpicker-small .x-monthpicker-item {
	margin: 2px 0 2px
}

.customize_body .x-monthpicker-small .x-monthpicker-item-inner {
	margin: 0 5px 0 5px
}

.customize_body .x-monthpicker-small .x-monthpicker-yearnav {
	height: 28px
}

.customize_body .x-monthpicker-small .x-monthpicker-yearnav-button {
	margin-top: 8px
}

/*.customize_body .x-btn-default-small {
	border-color: #3d55f6
}
.customize_body .x-btn-default-small {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding:9px 16px 9px 16px;
	border-width: 1px;
	border-style: solid;
	background-image: none;
	background-color: #5168fc;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #5168fc),
		color-stop(50%, #5168fc), color-stop(51%, #5168fc),
		color-stop(100%, #5168fc));
	background-image: -webkit-linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc);
	background-image: -moz-linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc);
	background-image: -o-linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc);
	background-image: linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc)
}
.customize_body .x-btn-default-small-over {
	border-color: #3d55f6;
	background-image: none;
	background-color: #6c8bff;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #6c8bff),
		color-stop(50%, #6c8bff), color-stop(51%, #6c8bff),
		color-stop(100%, #6c8bff));
	background-image: -webkit-linear-gradient(top, #6c8bff, #6c8bff 50%, #6c8bff 51%, #6c8bff);
	background-image: -moz-linear-gradient(top, #6c8bff, #6c8bff 50%, #6c8bff 51%, #6c8bff);
	background-image: -o-linear-gradient(top, #6c8bff, #6c8bff 50%, #6c8bff 51%, #6c8bff);
	background-image: linear-gradient(top, #6c8bff, #6c8bff 50%, #6c8bff 51%, #6c8bff)
}

.customize_body .x-btn-default-small-focus {
	border-color: #3d55f6;
	background-image: none;
	background-color: #5168fc;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #5168fc),
		color-stop(50%, #5168fc), color-stop(51%, #5168fc),
		color-stop(100%, #5168fc));
	background-image: -webkit-linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc);
	background-image: -moz-linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc);
	background-image: -o-linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc);
	background-image: linear-gradient(top, #5168fc, #5168fc 50%, #5168fc 51%, #5168fc)
}

.customize_body .x-btn-default-small-menu-active, .x-btn-default-small-pressed {
	border-color: #3d55f6;
	background-image: none;
	background-color: #c8d9fd;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #c8d9fd),
		color-stop(50%, #c8d9fd), color-stop(51%, #c8d9fd),
		color-stop(100%, #c8d9fd));
	background-image: -webkit-linear-gradient(top, #c8d9fd, #c8d9fd 50%, #c8d9fd 51%, #c8d9fd);
	background-image: -moz-linear-gradient(top, #c8d9fd, #c8d9fd 50%, #c8d9fd 51%, #c8d9fd);
	background-image: -o-linear-gradient(top, #c8d9fd, #c8d9fd 50%, #c8d9fd 51%, #c8d9fd);
	background-image: linear-gradient(top, #c8d9fd, #c8d9fd 50%, #c8d9fd 51%, #c8d9fd)
}*/

.customize_body .x-menu-item-text {
	font-size: 12px;
	color:#46545d;
	cursor: pointer;
	margin-right: 16px
}
.customize_body .x-menu-item-separator {
	height: 1px;
	border-top: solid 0px #dddfed;
	background-color: white;
	margin: 2px 0;
	padding: 0
}
.customize_body .x-menu {
	border-style: solid;
	border-width: 0px;
	border-color: #dddfed;
	background:#fff;
}
.customize_body .x-menu-icon-separator{
	left: 22px;
	border-left: solid 1px #dddfed;
	background-color: white;
	width: 1px
}
.customize_body .x-menu-item-link{
	line-height:30px;
	padding: 0 4px 0 27px;
	display: inline-block
}
.customize_body .x-menu-item-active {
	background-image: none;
	background-color: #f7f8f9;
	border-color: #f7f8f9
}
.customize_body .x-menu-item-arrow {
	width: 12px;
	height: 10px;
	top: 11px;
	right: 0;
	background-image: url(../rewrite_images/menu/menu-parent.png)
}
.customize_body .x-boundlist-item{
	padding: 0 6px;
	line-height: 32px;
	cursor: pointer;
	cursor: hand;
	position: relative;
	zoom: 1;
	border-width: 0px;
	border-style: dotted;
	border-color: white
}
.customize_body .x-boundlist-selected {
	background: #ffffff;
	border-color: #ffffff
}

.customize_body .x-boundlist-item-over {
	background: #007af5;
	border-color: #007af5;
	color:#fff;
}
.customize_body .x-menu-item-unchecked .x-menu-item-icon, .x-menu-item-unchecked .x-menu-item-icon-right
	{
	background-image: url(../rewrite_images/menu/unchecked.png)
}
.customize_body .x-menu-item-checked .x-menu-item-icon, .x-menu-item-checked .x-menu-item-icon-right
	{
	background-image: url(../rewrite_images/menu/checked.png)
}

.customize_body .x-menu-item-icon {
	width: 16px;
	height: 16px;
	top: 8px;
	left: 3px;
	background-position: center center
}

.tree_body_bg {
	border-color: #dddfeb;
	padding: 0;
	box-shadow: #e1e3f4 8px 0 8px -8px;
	-moz-box-shadow: #e1e3f4 8px 0 8px -8px;
	-webkit-box-shadow: #e1e3f4 8px 0 8px -8px;
}
.customize_body .tree_panelmain{
	background:#ffffff;
	margin:1px 0 0 0;  
	}
.customize_body .x-accordion-item{
	margin: 0 0 0;
}
.customize_body .x-grid-back-red {
	/* border-color:red; */
	background:#dddfed;
}
.customize-accordion-item .x-panel-body-default {
	background: white;
	border-color: #dddfed;
	color: #585a69;
	font-size: 14px;
	font-size: normal;
	border-width: 0px;
	border-style: solid
}
.customize-accordion-item .x-panel-header-default{
	background-image: none;
	background-color: #ffffff;
	cursor:pointer;
}
.customize-accordion-item .x-panel-header-default {
	font-size: 14px;
	border: 0px solid #ffffff
}
.customize-accordion-item .x-accordion-hd-over {
	background-color: #5168fc
}

.customize-accordion-item .x-panel-header-text-container {
	color: #303340;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	text-transform: none;
	font-size: 14px;
}
.customize-accordion-item .x-accordion-hd-over .x-panel-header-text-container{
	color: #ffffff;
}
.customize-accordion-item .x-tool-img{
	background-color:transparent;
	background-image: url(../rewrite_images/tools/tool-sprites-dark.png);
	filter: alpha(opacity = 100);
	opacity:1;
	position:relative;
	right:7px;
}
.customize-accordion-item .x-tool-collapse-top{
	background-position:0 -16px
}
.customize-accordion-item .x-tool-expand-bottom{
	background-position:0 0;
}
.customize-accordion-item .x-accordion-hd-over .x-tool-collapse-top{
	background-image: url(../rewrite_images/tools/tool-sprites-white.png);
	background-position:0 -16px
}
.customize-accordion-item .x-accordion-hd-over .x-tool-expand-bottom{
	background-image: url(../rewrite_images/tools/tool-sprites-white.png);
	background-position:0 0;
}
.customize-accordion-item .x-panel-header-icon{
	width: 20px;
	height: 20px;
	background-position: center center
}
.customize-accordion-item .x-panel-header-default-horizontal {
	padding: 9px 5px 10px 5px
}
.head_icon{
	/*width:0px;
	height:16px;
	background-color:#64b548;*/
}

.customize_toolbar{
	font-size: 14px;
	border-style: solid;
	padding: 9px 0 0 0;
	background:#fff;
	border:0px;
}

.customize_body .x-window-default .customize_toolbar{
	margin-bottom:20px;
}

.customize_toolbar .x-tbar-page-first{
	background-image: url(../rewrite_images/grid/page-first.png)
}
.customize_toolbar .x-tbar-page-prev {
	background-image: url(../rewrite_images/grid/page-prev.png)
}

.customize_toolbar .x-tbar-page-next {
	background-image: url(../rewrite_images/grid/page-next.png)
}
.customize_toolbar .x-tbar-page-last {
	background-image: url(../rewrite_images/grid/page-last.png)
}
.customize_toolbar .x-tbar-loading{
	background-image: url(../rewrite_images/grid/refresh.png)
}

.customize_toolbar .x-btn-default-toolbar-small {
	border-color: #007af5
}
.customize_toolbar .x-btn-default-toolbar-small {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding: 8px 8px 8px 8px;
	border-width: 1px;
	border-style: solid;
	background-image: none;
	background-color: #ffffff;
	cursor:pointer;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #ffffff),
		color-stop(50%, #ffffff), color-stop(51%, #ffffff),
		color-stop(100%, #ffffff));
	background-image: -webkit-linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff);
	background-image: -moz-linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff);
	background-image: -o-linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff);
	background-image: linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff)
}
.customize_toolbar .x-btn-default-toolbar-small-over {
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	border-color:#007af5;
}
.customize_toolbar .x-btn-default-toolbar-small-over .x-tbar-page-first{
	background-image: url(../rewrite_images/grid/page-first-over.png)
}
.customize_toolbar .x-btn-default-toolbar-small-over .x-tbar-page-prev {
	background-image: url(../rewrite_images/grid/page-prev-over.png)
}

.customize_toolbar .x-btn-default-toolbar-small-over .x-tbar-page-next {
	background-image: url(../rewrite_images/grid/page-next-over.png)
}
.customize_toolbar .x-btn-default-toolbar-small-over .x-tbar-page-last {
	background-image: url(../rewrite_images/grid/page-last-over.png)
}
.customize_toolbar .x-btn-default-toolbar-small-over .x-tbar-loading{
	background-image: url(../rewrite_images/grid/refresh-over.png)
}


.customize_toolbar .x-btn-default-toolbar-small-focus .x-tbar-page-first{
	background-image: url(../rewrite_images/grid/page-first-over.png)
}
.customize_toolbar .x-btn-default-toolbar-small-focus .x-tbar-page-prev {
	/*background-image: url(../rewrite_images/grid/page-prev-over.png)*/
}

.customize_toolbar .x-btn-default-toolbar-small-focus .x-tbar-page-next {
	/*background-image: url(../rewrite_images/grid/page-next-over.png)*/
}
.customize_toolbar .x-btn-default-toolbar-small-focus .x-tbar-page-last {
	background-image: url(../rewrite_images/grid/page-last-over.png)
}
.customize_toolbar .x-btn-default-toolbar-small-over .x-tbar-loading{
	background-image: url(../rewrite_images/grid/refresh-over.png)
}
.customize_toolbar .x-btn-default-toolbar-small-focus {
	/*background-image: none;
	
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	border-color:#007af5;*/
}

.customize_toolbar .x-btn-default-toolbar-small-menu-active,
	.x-btn-default-toolbar-small-pressed {
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	border-color:#007af5;
}
.customize_toolbar .x-btn-default-toolbar-small-disabled {
	background-image: none;
	background-color: #fafafa;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #fafafa),
		color-stop(50%, #fafafa), color-stop(51%, #fafafa),
		color-stop(100%, #fafafa));
	background-image: -webkit-linear-gradient(top, #fafafa, #fafafa 50%, #fafafa 51%, #fafafa);
	background-image: -moz-linear-gradient(top, #fafafa, #fafafa 50%, #fafafa 51%, #fafafa);
	background-image: -o-linear-gradient(top, #fafafa, #fafafa 50%, #fafafa 51%, #fafafa);
	background-image: linear-gradient(top, #fafafa, #fafafa 50%, #fafafa 51%, #fafafa);
	opacity: 1;
	border-color:#dddddd
}
.customize_toolbar .x-btn-default-toolbar-small-disabled  .x-tbar-page-first {
	background-image: url(../rewrite_images/grid/page-first-no.png)
}
.customize_toolbar .x-btn-default-toolbar-small-disabled  .x-tbar-page-prev{background-image: url(../rewrite_images/grid/page-prev-no.png)}
.customize_toolbar .x-btn-default-toolbar-small-disabled  .x-tbar-page-next{background-image: url(../rewrite_images/grid/page-next-no.png)}

.customize_toolbar .x-btn-default-toolbar-small-disabled  .x-tbar-page-last {
	background-image: url(../rewrite_images/grid/page-last-no.png)
}
.customize_toolbar .x-btn-default-toolbar-small-disabled  .x-tbar-loading{
	background-image: url(../rewrite_images/grid/refresh-no.png)
}
.customize_toolbar .x-form-text{
	padding: 0 0 0 0;
	color:#46545d;
	background: #ffffff repeat-x 0 0;
	border-width: 1px;
	border-style: solid;
	border-color:#dddfed;
	width:24px;
	height: 24px;
	line-height: 24px;
	text-align:center;
	border-radius:2px;
	-moz-border-radius:2px;
	-webkit-border-radius:2px;
}

/*toolbar spinner*/
.customize_body .customize_toolbar .x-form-spinner-up{
	background-image: url(../rewrite_images/form/spinner_toolbar.png);
	background-color:#f7f8fa;
	width: 22px;
	height: 12px
}
.customize_body .customize_toolbar .x-form-spinner-down {
	background-image: url(../rewrite_images/form/spinner_toolbar.png);
	background-color:#f7f8fa;
	width: 22px;
	height: 12px
}
.customize_body .customize_toolbar .x-form-spinner-down {
	background-position: 0 -12px
}
.customize_body .customize_toolbar .x-form-trigger-wrap-focus .x-form-spinner-down {
	background-position: -66px -12px
}
.customize_body .customize_toolbar .x-form-trigger-wrap .x-form-spinner-down-over {
	background-position: -22px -12px
}
.customize_body .customize_toolbar .x-form-trigger-wrap-focus .x-form-spinner-down-over {
	background-position: -88px -12px
}
.customize_body .customize_toolbar .x-form-trigger-wrap .x-form-spinner-down-click {
	background-position: -44px -12px
}

/*toolbar trigger*/
.customize_body .customize_toolbar .x-form-trigger {
	cursor: pointer;
	overflow: hidden;
	background-repeat: no-repeat
}
.customize_body .customize_toolbar .x-form-trigger {
	background: url(../rewrite_images/form/trigger_toolbar.png);
	width: 22px
}
.customize_body .customize_toolbar .x-form-trigger {
	height: 34px
}
.customize_body .customize_toolbar .x-form-trigger-over {
	background-position: -22px 0
}

.customize_body .customize_toolbar .x-form-trigger-wrap-focus .x-form-trigger {
	background-position: -66px 0
}

.customize_body .customize_toolbar .x-form-trigger-wrap-focus .x-form-trigger-over {
	background-position: -88px 0
}

.customize_body .customize_toolbar .x-form-trigger-click, .x-form-trigger-wrap-focus .x-form-trigger-click
	{
	background-position: -44px 0
}
.customize_body .customize_toolbar .x-form-trigger-wrap{
	vertical-align: top;
	border-collapse: separate
}
.customize_body .customize_toolbar .x-form-trigger-wrap {
	border: 1px solid;
	border-color: #cccccc #cccccc #cccccc;
	border-radius:2px;
}
.customize_body .customize_toolbar .x-trigger-cell {
	background-color: #ffffff;
	width: 22px
}


.customize_center .service_platform_bodybg{
	background:#ebeff5;
	border-radius:6px;
}

.customize_body .label_space{
	/*margin-top:0px;*/
}

.customize_body .service_platform_bodybg .x-form-display-field {
	font: normal 14px/17px arial,Microsoft Yahei;
	color: #585a69;
	margin-top: 4px;
	word-break:initial;
}
.fm_field_text{
	background-color:#f7f8fc;
	height:34px;
	width:34px;
	border-color:#ccc;
	border-width:1px;
	border-style:solid;
	font-size:14px;
	color:#303340;
}

.customize_body .customize_stbtn .x-btn-default-toolbar-small .x-btn-split-right {
	background-image: url(../rewrite_images/button/default-toolbar-small-s-arrow-stbtn.png);
	padding-right: 23px
}
.customize_body .customize_stbtn .x-btn-split {
	display: block;
	background-repeat: no-repeat
}
.customize_body .customize_stbtn .x-btn-split-right {
	background-position: right center
}

.customize_body .x-btn-default-toolbar-small .x-btn-arrow {
	background-image: url(../rewrite_images/button/default-toolbar-small-arrow.png)
}

.customize_body .x-btn-group-default-framed {
	border-color: #ffffff;
	-webkit-box-shadow: white 0 1px 0 0 inset, white 0 -1px 0 0 inset, white
		-1px 0 0 0 inset, white 1px 0 0 0 inset;
	-moz-box-shadow: white 0 1px 0 0 inset, white 0 -1px 0 0 inset, white
		-1px 0 0 0 inset, white 1px 0 0 0 inset;
	box-shadow: white 0 1px 0 0 inset, white 0 -1px 0 0 inset, white -1px 0
		0 0 inset, white 1px 0 0 0 inset
}

.customize_body .x-btn-default-toolbar-small-icon .x-btn-icon-el {
	width: 16px;
	height: 16px
}
.customize_body .x-btn-default-toolbar-small-icon .x-btn-inner {
	width: 16px;
	padding: 0
}

.customize_body .x-progress-default {
	background-color: #cce4fd;
	border-width: 0;
	height: 20px;
	border-color: #cce4fd
}
.customize_body .x-progress-default .x-progress-bar-default {
	background-image: none;
	background-color: #7fbcfa
}
.customize_body .x-progress-default .x-progress-text {
	color: #007af5;
	font-weight: bold;
	font-size: 14px;
	text-align: center;
	line-height: 20px
}
.customize_body .x-progress-default .x-progress-text-back {
	color: #007af5;
	line-height: 20px
}

.customize_body .x-tip-default {
	border-color: #dddfed
}
.customize_body .x-tip-form-invalid {
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	padding: 2px 2px 2px 2px;
	border-width: 1px;
	border-style: solid;
	background-color: #ffffff
}
.customize_body .x-tip-form-invalid-mc {
	background-color: #ffffff
}
.customize_body .x-tip-body-form-invalid {
	padding: 3px 3px 3px 22px;
	color: #3d404d;
	font-size: 12px;
	font-weight: normal
}
.customize_body .x-tip-form-invalid {
	border-color: #dddfed
}
.customize_body .x-form-type-text textarea.x-form-invalid-field, .x-form-type-text input.x-form-invalid-field,
	.x-form-type-password textarea.x-form-invalid-field,
	.x-form-type-password input.x-form-invalid-field, .x-form-type-number textarea.x-form-invalid-field,
	.x-form-type-number input.x-form-invalid-field, .x-form-type-email textarea.x-form-invalid-field,
	.x-form-type-email input.x-form-invalid-field, .x-form-type-search textarea.x-form-invalid-field,
	.x-form-type-search input.x-form-invalid-field, .x-form-type-tel textarea.x-form-invalid-field,
	.x-form-type-tel input.x-form-invalid-field {
	background-color: white;
	border-color: #f63508
}
.customize_body .x-tip-body-form-invalid {
	background: 1px 1px no-repeat;
	background-image: url(../rewrite_images/form/exclamation.png);
}
.customize_body .x-form-cb-wrap {
	height: 30px
}
.customize_body .x-form-cb-wrap {
	vertical-align: middle
}
.customize_body .x-mask-msg {
	padding: 8px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	background-image: none;
	background-color: #ffffff
}
.customize_body .x-mask-msg-inner {
	padding: 0;
	background-color: transparent;
	color: #3d404d;
	font: normal 14px arial,Microsoft Yahei
}
.customize_body .x-btn-default-small-icon .x-btn-inner, .x-btn-default-small-noicon .x-btn-inner
	{
	line-height: 16px
} 

.customize_body .oper_system .x-column-header-inner {
	text-align:left;
	padding: 8px 10px 8px 15px;
	text-overflow: ellipsis
}
.customize_body .oper_system .x-column-header{
	border-right: 0px solid transparent;
	color: #3d404d;
	font: 14px/15px arial,Microsoft Yahei;
	background-color: #ffffff
}
.customize_body .oper_system .x-grid-group-hd{
	border-width: 0 0 1px 0;
	border-style: solid;
	border-color: #dddfeb;
	padding: 8px 4px 8px 15px;
	background: #f7f8fa;
	cursor: pointer
}
.customize_body .oper_system .x-grid-group-title {
	color: #303340;
	font: 14px/15px arial,Microsoft Yahei
}
.customize_body .oper_system .x-grid-group-hd-collapsible .x-grid-group-title{
	background-repeat: no-repeat;
	background-position: left center;
	background-image: url(../rewrite_images/grid/group-collapse.png);
	padding: 0 0 0 17px
}
.customize_body .oper_system .x-grid-group-hd-collapsed .x-grid-group-title {
	background-image: url(../rewrite_images/grid/group-expand.png)
}
.customize_body .oper_system .x-grid-cell-inner{
	text-overflow: ellipsis;
	padding:10px 0 0 32px;
	height:38px;
	line-height:100%;
}

.customize_body .x-grid-row-expander {
	width: 11px;
	height: 11px;
	cursor: pointer;
	background-image: url(../rewrite_images/grid/group-collapse.png)
}
.customize_body .x-grid-row-collapsed .x-grid-row-expander {
	background-image: url(../rewrite_images/grid/group-expand.png)
}
.customize_body .x-grid-rowwrap {
	border-color: #ffffff;
	border-style: solid
}

.customize_body .x-grid-group-hd{
	border-width: 0 0 1px 0;
	border-style: solid;
	border-color: #dddfeb;
	padding: 9px 4px 9px 4px;
	background: #f7f8fa;
	cursor: pointer
}
.customize_body .x-grid-group-title {
	color: #303340;
	font:bold 14px/15px arial,Microsoft Yahei
}
.customize_body .x-grid-group-hd-collapsible .x-grid-group-title {
	background-repeat: no-repeat;
	background-position: left center;
	background-image: url(../rewrite_images/grid/group-collapse.png);
	padding: 0 0 0 17px
}
.customize_body .x-grid-group-hd-collapsed .x-grid-group-title {
	background-image: url(../rewrite_images/grid/group-expand.png)
}

.customize_body .x-tab-default-bottom {
	-moz-border-radius-topleft: 0;
	-webkit-border-top-left-radius: 0;
	border-top-left-radius: 0;
	-moz-border-radius-topright: 0;
	-webkit-border-top-right-radius: 0;
	border-top-right-radius: 0;
	-moz-border-radius-bottomright: 3px;
	-webkit-border-bottom-right-radius: 3px;
	border-bottom-right-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	-webkit-border-bottom-left-radius: 3px;
	border-bottom-left-radius: 3px;
	padding: 8px 12px 7px 12px;
	border-width: 0;
	border-style: solid;
	background-color: #ffffff
}
.customize_body .x-fieldset-header-text {
	font: 14px/16px bold arial,Microsoft Yahei;
	color: #585a69;
	padding: 1px 0
}
.customize_body .x-fieldset .x-tool-toggle {
	background-image: url(../rewrite_images/fieldset/collapse-tool.png);
	background-position: 0 0
}
.customize_body .x-fieldset .x-tool-over .x-tool-toggle {
	background-position: 0 -15px
}

.customize_body .x-fieldset-collapsed .x-tool-toggle {
	background-position: -15px 0
}

.customize_body .x-fieldset-collapsed .x-tool-over .x-tool-toggle {
	background-position: -15px -15px
}
.customize_body .x-fieldset {
	border: 1px solid #dddfeb;
	padding: 0 10px;
	margin: 0 0 10px
}
.toolbar_group_btn{
	background-color: #ffffff;
}
.toolbar_group_btn .x-btn-default-toolbar-small {
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	-ms-border-radius: 3px;
	-o-border-radius: 3px;
	border-radius: 3px;
	padding: 3px 3px 3px 3px;
	border-width: 1px;
	border-style: solid;
	background-image: none;
	border-color: #ffffff;
	background-color: #ffffff;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #ffffff),
		color-stop(50%, #ffffff), color-stop(51%, #ffffff),
		color-stop(100%, #ffffff));
	background-image: -webkit-linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff);
	background-image: -moz-linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff);
	background-image: -o-linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff);
	background-image: linear-gradient(top, #ffffff, #ffffff 50%, #ffffff 51%, #ffffff)
}
.toolbar_group_btn .x-btn-default-toolbar-small .x-btn-inner{
	color:#303340;
	font-size: 14px;
	font-weight: bold;
}

.customize_body .x-form-focus{
	border-color: #007af5
}
.customize_body .x-form-trigger-wrap-focus .x-form-trigger-wrap {
	border-color: #007af5
}
.customize_body td.x-grid-rowwrap .x-grid-cell {
	border-bottom: 3px;
	background-color: #fafafa
}
.customize_body .x-mask {
	filter: alpha(opacity = 60);
	opacity: .6;
	background: #000000
}
.customize_body .x-column-header-last .x-column-header-over .x-column-header-trigger {
	border-right: 0px solid silver
}

.customize_body .service_platform_bodybg .x-panel-header-default-horizontal{
	padding: 17px 20px 10px 20px;
}
.customize_body .service_platform_bodybg .x-panel-header-text-container-default{
	color:#46545d;
	font-size: 18px;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	padding: 1px 0 0;
	text-transform: none;
	line-height:27px;
}
.customize_body .customize_panel_back .x-panel-header-default-horizontal{
	padding: 17px 20px 0px 20px;
}
.customize_body .x-window-body-default .customize_panel_back .x-panel-header-default-horizontal{
	padding: 17px 20px 0px 0px;
}
.customize_body .customize_panel_back .x-panel-header-text-container-default{
	color:#46545d;
	font-size: 18px;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	padding: 1px 0 0 0;
	text-transform: none;
	line-height:27px;
}
.customize_body .customize_panel_header_arrow .x-panel-header-default {
	background-image: none;
	background-color: transparent;
	border-radius:0 0 0 0;
}
.customize_body .customize_panel_header_arrow .x-panel-body-default{
	color: #585a69;
	font-size: 12px;
	font-weight:normal
}
.customize_body .customize_panel_header_arrow .x-panel-header-text-container-default{
	color: #585a69;
	font-size: 18px;
	font-weight:normal
}
.customize_body .customize_panel_header_arrow .x-panel-header-default-horizontal{
	padding: 9px 0 10px 0
}
.customize_body .x-panel .x-panel-header-default-collapsed-border-top {
	border-bottom-width: 0px !important
}
.customize_body .x-boundlist {
	border-width: 1px;
	border-style: solid;
	border-color: #d9d9d9;
	background: white;
}
.customize_body .x-tab-bar-default .x-tabbar-scroll-left {
	background-image: url(../rewrite_images/tab-bar/default-scroll-left.png)
}
.customize_body .x-tab-bar-default .x-tabbar-scroll-right {
	background-image: url(../rewrite_images/tab-bar/default-scroll-right.png)
}
.customize_body .x-tab-bar-default .x-tabbar-scroll-top {
	background-image: url(../rewrite_images/tab-bar/default-scroll-top.png)
}
.customize_body .x-tab-bar-default .x-tabbar-scroll-bottom {
	background-image: url(../rewrite_images/tab-bar/default-scroll-bottom.png)
}
.customize_body .x-tab-bar-default .x-box-scroller {
	cursor: pointer;
	filter: alpha(opacity = 100);
	opacity: 1;
	background-color: transparent;
	margin:7px 0 0 0;
}

.customize_gray_back{
	background-color:#ebeff5;
	padding:5px 0;
}
.panel_space_right{
	margin-right:20px;
}
.panel_space_right_zb{
	margin-right:15px;
}
.panel_space_left{
	margin-left:20px;
}
.panel_space_top{
	margin-top:20px;
}
.panel_space_bottom{
	margin-bottom:20px;
}
.panel_space_top_right{
	margin:20px 20px 0 0;
}
.panel_space_top_bottom{
	margin:20px 0 20px 0;
}
.panel_space_top_left{
	margin:20px 0 0 20px;
}
.panel_space_left_right{
	margin:0 20px 0 20px;
}
.customize_panel_back{
	background-color:#ffffff;
	border-radius:6px;
}
.customize_panel_back02{
	background-color:#ffffff;
}
.window_border.custom_title .x-panel-header-text-container-default{
	margin-left:20px;
}
.window_border .x-panel-header-text-container-default{
	color:#323a3d;
	font-size: 16px;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	padding: 0px 0 0 0;
	text-transform: none;
	line-height:20px;
}
.customize_body .customize_panel_back .window_border .x-panel-header-default-horizontal{
	padding: 0 0 0 0;
}
.start_task .x-toolbar {
	font-size: 14px;
	border-style: solid;
	padding: 2px 0 2px 0
}
.start_task .x-horizontal-box-overflow-body{
	float: left;
	border-bottom:0px solid #d9d9d9;
}

.his_label_space .x-form-item-label{
	color:#46545d;
	font: normal 14px/17px arial,Microsoft Yahei;
	margin-top: 4px
}
.sc_tlbar_height .x-form-item-label{
	color:#46545d;
	font: normal 14px/17px arial,Microsoft Yahei;
	margin-top: 4px
}
.retry_config_toolbar .x-toolbar-footer {
	background: transparent;
	border: 0;
	margin: 0;
	padding: 6px 0 6px 6px
}
.toolbar_btn_common .x-form-trigger-wrap {
	background-color:transparent;
}
.toolbar_btn_common .x-btn-default-toolbar-small {
	border-color: #007af5
}
.toolbar_btn_common .x-btn-default-toolbar-small {
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-ms-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding:8px 16px 8px 16px;
	border-width: 1px;
	border-style: solid;
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5)
}

.toolbar_btn_common .x-btn-default-toolbar-small-over {
	background-image: none;
	background-color: #0263c6;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #0263c6),
		color-stop(50%, #0263c6), color-stop(51%, #0263c6),
		color-stop(100%, #0263c6));
	background-image: -webkit-linear-gradient(top, #0263c6, #0263c6 50%, #0263c6 51%, #0263c6);
	background-image: -moz-linear-gradient(top, #0263c6, #0263c6 50%, #0263c6 51%, #0263c6);
	background-image: -o-linear-gradient(top, #0263c6, #0263c6 50%, #0263c6 51%, #0263c6);
	background-image: linear-gradient(top, #0263c6, #0263c6 50%, #0263c6 51%, #0263c6)
}

.toolbar_btn_common .x-btn-default-toolbar-small-focus {
	background-image: none;
	background-color: #007af5;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #007af5),
		color-stop(50%, #007af5), color-stop(51%, #007af5),
		color-stop(100%, #007af5));
	background-image: -webkit-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -moz-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: -o-linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5);
	background-image: linear-gradient(top, #007af5, #007af5 50%, #007af5 51%, #007af5)
}

.toolbar_btn_common .x-btn-default-toolbar-small-menu-active,
	.x-btn-default-toolbar-small-pressed {
	background-image: none;
	background-color: #bfbfbf;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #bfbfbf),
		color-stop(50%, #bfbfbf), color-stop(51%, #bfbfbf),
		color-stop(100%, #bfbfbf));
	background-image: -webkit-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: -moz-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: -o-linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf);
	background-image: linear-gradient(top, #bfbfbf, #bfbfbf 50%, #bfbfbf 51%, #bfbfbf)
}
.customize_body .customize_panel_header_arrow_another .x-panel-header-default-horizontal{
	padding: 9px 10px 10px 20px;
}
.customize_body .customize_panel_header_arrow_another .x-panel-header-text-container-default{
	color:#323a3d;
	font-size: 16px;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	padding: 1px 0 0 0;
	text-transform: none;
	line-height:27px;
}
.alarm_tree .x-tree-node-text input{
	border-radius:2px;
	background-color:#fff;
	border:1px solid #d9d9d9;
	height:30px;
	width:100%;
	margin:0 0 0 10px;
	font-size:14px;
	color:#c1c1c1;
}

.customize_toolbar .Common_Btn{
	background-color:#007af5;
	background-image: -webkit-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -moz-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: -o-linear-gradient(top, #007af5 0%, #007af5 100%);
  	background-image: linear-gradient(to bottom, #007af5 0%, #007af5 100%);
	border-radius:2px; 
	text-align:center;
	font-size:12px;
	padding:8px 16px 8px 16px;
	color:#fff;
	cursor:pointer;
	border:1px solid #007af5;
	margin:5px 7px;
	cursor:pointer
	}
.customize_toolbar .Common_Btn:hover{
	background-color:#0263c6;
	background-image: -webkit-linear-gradient(top, #0263c6 0%, #0263c6 100%);
  	background-image: -moz-linear-gradient(top, #0263c6 0%, #0263c6 100%);
  	background-image: -o-linear-gradient(top, #0263c6 0%, #0263c6 100%);
  	background-image: linear-gradient(to bottom, #0263c6 0%, #0263c6 100%);
  	border:1px solid #0263c6;
  	color:#fff;
	}
.customize_toolbar .Common_Btn:active{
	background-color:#bfbfbf;
	background-image: -webkit-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -moz-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: -o-linear-gradient(top, #bfbfbf 0%, #bfbfbf 100%);
  	background-image: linear-gradient(to bottom, #bfbfbf 0%, #bfbfbf 100%);
  	border:1px solid #bfbfbf;
  	color:#ffffff;
	}
.mhome_tree .x-grid-cell{
	color: null;
	font: normal 14px/15px arial,Microsoft Yahei;
	background-color: #ffffff;
	border-color: #ffffff;
	border-style: solid
}
.mhome_tree .x-tree-icon {
	width: 21px;
	height: 21px
}
.common_tasks .x-grid-cell a{
	margin:0 15px 0 0;
}
.customize_body .x-form-invalid .x-form-trigger-wrap {
	border-color: #ff4006
}
.chagedbpwd_label_space .x-form-item-label{
	color:#46545d;
	font: normal 14px/17px arial,Microsoft Yahei;
	margin-top: 2px
}
.act_discolor .x-grid-yellow .x-grid-td{
	background:#ffe87c 
}

.act_discolor .x-grid-blue .x-grid-td{
	background:#87ceff 
}
.act_discolor .x-grid-red .x-grid-td{
	background:#ff7f7f
}
/*閻ц棄鎮曢崡鏇熷⒔鐞涳拷*/
.whitelist .x-toolbar {
	padding: 0px 0px 0px 0px
}
.customize_gray_back_whitelist{
	background-color:#ebeff5;
	padding:0px 0;
}
.whitelist{
	padding: 0px 0px 0px 0px
}
.customize_form_cn {
	padding: 0px 0px 0px 5px
}

.customize_body .ip_details .x-panel-header-text-container-default{
	color:#929ea8;
	font-size: 16px;
	font-family: arial,Microsoft Yahei;
	padding: 0px 0 0 10px;
	text-transform: none;
	line-height:20px;
}

.customize_body .x-grid-empty {
	padding: 0;
	color: gray;
	background-color: white;
	font: normal 12px arial,Microsoft Yahei;
	height:90%;
}

.customize_body .x-tab-default .x-tab-close-btn{
	width: 12px;
	height: 12px;
	background-image: url(../rewrite_images/tab/tab-default-close.png)
}
.customize_body .x-tab-default .x-tab-close-btn-over {
	background-position: -12px 0
}
.customize_body .x-tab-default .x-tab-close-btn {
	top: 15px;
	right: 0px
}
.customize_body .x-tab-default-disabled .x-tab-close-btn {
	filter: alpha(opacity = 30);
	opacity: .3;
	background-position: 0 0
}


.thumb-table img{display:block}
.thumb-table td{text-align:center;padding-top:8px;padding:8px 10px;;line-height:25px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;}
.x-portal.customize-hei .x-panel-header {
	padding:15px 20px 10px 20px;
}
.x-portal.customize-hei .x-box-inner{height:22px}

.customize_body .x-portal.customize-hei .x-tool-collapse-top {
	background-image: url(../rewrite_images/tools/x-tool-collapse-top.png);
	width:20px;
	height:20px;
	background-position: 0 0;
	background-size:100% 100%;
}

.customize_body .x-portal.customize-hei .x-tool-maximize {
    background-image: url(../rewrite_images/tools/x-tool-maximize.png);
    width: 20px;
    height: 20px;
    background-position: 0 0;
	background-size:100% 100%;
}
.customize_body .x-portal.customize-hei .x-tool-close {
    background-image: url(../rewrite_images/tools/x-tool-close.png);
    width: 20px;
    height: 20px;
	background-size:100% 100%;
}

.customize_body .x-collapse-el {
	cursor: pointer
}

.customize_body .x-layout-split-left, .x-layout-split-right {
	top: 50%;
	margin-top: -24px;
	width: 8px;
	height: 48px
}

.customize_body .x-layout-split-top, .x-layout-split-bottom {
	left: 50%;
	width: 48px;
	height: 8px;
	margin-left: -24px
}

.customize_body .x-layout-split-left {
	background-image: url(../rewrite_images/util/splitter/mini-left.png)
}

.customize_body .x-layout-split-right {
	background-image: url(../rewrite_images/util/splitter/mini-right.png)
}

.customize_body .x-layout-split-top {
	background-image: url(../rewrite_images/util/splitter/mini-top.png)
}

.customize_body .x-layout-split-bottom {
	background-image: url(../rewrite_images/util/splitter/mini-bottom.png)
}

.customize_body .x-splitter-collapsed .x-layout-split-left {
	background-image: url(../rewrite_images/util/splitter/mini-right.png)
}

.customize_body .x-splitter-collapsed .x-layout-split-right {
	background-image: url(../rewrite_images/util/splitter/mini-left.png)
}

.customize_body .x-splitter-collapsed .x-layout-split-top {
	background-image: url(../rewrite_images/util/splitter/mini-bottom.png)
}

.customize_body .x-splitter-collapsed .x-layout-split-bottom {
	background-image: url(../rewrite_images/util/splitter/mini-top.png)
}

.customize_body .x-splitter-active {
	background-color: #b4b4b4;
	filter: alpha(opacity = 80);
	opacity: .8
}

.customize_body .x-splitter-active .x-collapse-el {
	filter: alpha(opacity = 100);
	opacity: .3
}

.customize_body .x-tool-expand-top, .x-tool-collapse-top {
	background-image: url(../rewrite_images/tools/x-tool-collapse-top.png);
	width:20px;
	height:20px;
	background-position:0 0;
}



/*thumb-task*/
.thumb-task {
  padding-left: 17px;
  padding-top: 10px;
}
.thumb-task .thumb-task-demo {
  width: 100%;
  border: 1px solid #d7d4d4;
  height: auto;
  padding-bottom: 5px;
  position: relative;
  background: url(../../images/task.png) no-repeat left center;
  background-position: 10px 30px;
  bbackground-repeat: no-repeat;
  height: 140px;
}
.thumb-task .thumb-task-demo h2 {
  font-size: 14px;
  padding-top: 18px;
  padding-left: 50px;
  text-align: left;
}
.thumb-task .down-demo {
  position: absolute;
  display: block;
  right: 10px;
  top: 10px;
}
.thumb-task .down-demo .Common_Btn {
  background: url(../../images/task-download.png) no-repeat left center;
  border: none;
  width: 18px;
  height: 16px;
  padding: 0;
  margin: 0;
}
.thumb-task .found-demo {
  position: absolute;
  display: block;
  right: 0px;
  bottom: 3px;
}
.thumb-task .found-demo .Common_Btn {
}
.thumb-task .often-demo {
  position: absolute;
  display: block;
  left: 10px;
  bottom: 10px;
  font-size: 14px;
}
.thumb-task .often-demo a {
  color: #007af5;
}
.thumb-task .linedemo {
  margin-top: 30px;
  width: 100%;
  height: 1px;
  border-top: 1px solid #d7d4d4;
}


/*pfPortal*/
.customize_fPortal_addbtn.x-btn-default-small {
  background: none;
}
.customize_fPortal_addbtn {
  background: url(../../images/pfPortal/portal_addbtn.png) center center !important;
  width: 100%;
  height: 100%;
  background-position: 50% 50%;
  border-style: none !important;
  background-repeat: no-repeat !important;
}
/*taskScreen*/
.taskScreen-table {
  width: 100%;
  padding: 0;
}
.taskScreen-table td {
  padding-right: 20px;
  width: 20%;
}
.taskScreen-table td:nth-child(5) {
  padding-right: 0px;
}
.taskScreen-table .tddemo {
  background: url(../images/sc_back_img.png) no-repeat right,
    -webkit-linear-gradient(left, #5b85fd, #364de9);
  background-color: #7184fb;
  height: 120px;
  border-radius: 4px;
  width: 100%;
  color: #fff;
}
.taskScreen-table .tddemo .Script_special {
  font-size: 16px;
}
.taskScreen-table .tddemo .Script_font {
  margin-top: 30px;
}
.taskScreen-table td:nth-child(1) .Script_img {
  background-image: url(../images/ts-Script_img1.png);
}
.taskScreen-table td:nth-child(2) .Script_img {
  background-image: url(../images/ts-Script_img2.png);
}
.taskScreen-table td:nth-child(3) .Script_img {
  background-image: url(../images/ts-Script_img3.png);
}
.taskScreen-table td:nth-child(4) .Script_img {
  background-image: url(../images/ts-Script_img4.png);
}
.taskScreen-table td:nth-child(5) .Script_img {
  background-image: url(../images/ts-Script_img5.png);
}
.taskScreen-table .tddemo p {
  text-align: left;
  font-size: 40px;
  font-weight: bold;
  padding: 0;
  margin: 0;
  color: #fff;
}
.taskScreen-table-2 {
  padding: 20px 0 0 0;
  width: 100%;
  cursor: pointer;
}
.taskScreen-table-2 .taskScreen-main {
  position: relative;
  border: 1px solid #dddddd;
  border-radius: 5px;
  padding: 20px 12px;
  background: url(../images/Two_C_img_gray2.png) no-repeat center 10px;
  padding-top: 104px;
}
.taskScreen-table-2 .taskScreen-main .onicon {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  display: none;
}
.taskScreen-table-2 td.active .onicon {
  display: block;
}
.taskScreen-table-2 .taskScreen-main.box-show {
  color: #007af5;
}
.taskScreen-table-2 .taskScreen-main.box-show .zt-cont {
  background: #e5f1fe;
}
.taskScreen-table-2 .taskScreen-main.box-show em {
  color: #007af5;
}
.taskScreen-table-2 td {
  text-align: center;
  padding-top: 25px;
  width: 20%;
  padding: 0 10px;
}
.taskScreen-table-2 td:last-child {
  border-right: none;
}
.taskScreen-table-2 td h2 {
  font-size: 14px;
}
.taskScreen-table-2 td a {
  display: block;
}
.taskScreen-table-2 .zt-cont {
  height: 48px;
  padding: 10px 10px 0 10px;
}
.taskScreen-table-2 .zt-demo {
  width: 25%;
  float: left;
  text-align: left;
  padding: 5px 3px 5px 3px;
  position: relative;
  font-size: 12px;
}
.taskScreen-table-2 .zt-demo::before {
  content: "";
  position: absolute;
  top: 14px;
  right: 0;
  width: 1px;
  height: 20px;
  border-right: 1px solid #ddd;
}
.taskScreen-table-2 .zt-demo:nth-child(4)::before {
  display: none;
}
.taskScreen-table-2 .zt-demo em {
  font-style: normal;
  font-weight: bold;
  display: block;
  text-align: center;
  margin-bottom: 3px;
}
.taskScreen-table-2 .zt-demo span {
  display: block;
  text-align: center;
}
.taskScreen-piechart {
  position: absolute;
  top: 25%;
  left: 50%;
  width: 140px;
  margin: 0 auto;
  font-size: 16px;
  margin-left: -64px;
  margin-top: -45px;
}
.customize_taskScreen_pd {
  padding: 10px !important;
}
.taskScreen-table-2 .taskScreen-main.Selected-box {
  border: 1px solid #007af5;
  color: #007af5;
  background: url(../images/Two_C_img_blue2.png) no-repeat center 10px;
}

.taskScreen-table_back {
  background-color: #ffffff;
  border-radius: 6px 6px 0 0;
}
.taskScreen-table_back02 {
  background-color: #ffffff;
  border-radius: 0 0 6px 6px;
}

.customize_body .x-tree-icon-leaf {
	background-image: url(../rewrite_images/tree/leaf.png)
}


.scriptTipKeyWord .x-horizontal-box-overflow-body{
	border-bottom: 0px solid #d9d9d9;
	float: left;
}


.Actmonitor_right{
	background-color:#fafafa;
	margin-bottom:10px;
	border-radius:6px;
}
.customize_body .Actmonitor_right .x-panel-header-text-container-default{
	color:#323a3d;
	font-size: 16px;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	padding: 1px 0 0 0;
	text-transform: none;
	margin:6px 0 15px 0;
}
.Actmonitor_right_border{
	height:40px;
	line-height:40px;
	font-size:14px;
	margin:0 30px;
	border-top:1px solid #d9d9d9;
}
.Actmonitor_column{
	width:70px;
	text-align:left;
	float:left;
	display:block;
}
.Actmonitor_column02{
	float:right;
	width:170px;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
	text-align:right
}
.Actmonitor_actoutput{
	margin:0 25px 0 25px;
}
.Actmonitor_right .x-form-text {
	border-radius:6px;
	border-color:#d9d9d9;
}

.Actmonitor_left{
	margin:0 0 0 20px;
}
.Actmonitor_left .x-grid-cell{
	color: null;
	font: normal 14px/15px arial,Microsoft Yahei;
	background-color: #ffffff;
	border-bottom:5px solid #ffffff
}
.Actmonitor_left .x-grid-row-selected .x-grid-td {
	background-color: #fafafa
}
.Actmonitor_left .x-grid-row-over .x-grid-td {
	background-color: #fafafa
}

.Actmonitor_left .x-column-header{
	border-right: 0px solid transparent;
	color: #323a3d;
	font: bold 16px/15px arial,Microsoft Yahei;
	background-color: #ffffff;
	margin:10px 0 12px;
}

.Actmonitor_center{
	margin-top:30px;
}
.Actmonitor_color_common{
	width:8px;
	height:8px;
	border-radius:100%;
	margin-right:5px;
	margin-bottom:2px
}
.Actmonitor_color01{
	background-image:url(../images/Actmonitor_color01.png);/*瀵倸鐖�*/
	}
.Actmonitor_color02{
	background-image:url(../images/Actmonitor_color02.png);/*閹稿倽鎹�*/
	}
.Actmonitor_color03{
	background-image:url(../images/Actmonitor_color03.png);/*瀹告彃鐣幋锟�*/
	}
.Actmonitor_color04{
	background-image:url(../images/Actmonitor_color04.png);/*鏉╂劘顢�*/
	}
.Actmonitor_color05{
	background-image:url(../images/Actmonitor_color05.png);/*閺堫亣绻嶇悰锟�*/
	}
.Actmonitor_color06{
	background-image:url(../images/Actmonitor_color06.png);/*閹烘帡妲�*/
	}
.chart_list{
	width:100%;
	margin:7px 0 7px 0;
	float:left;
	}
.list_float{
	float:left;
	margin:4px 7px 0 0;
	}
.list_text{
	width:80px;
	float:left;
	font-size:12px;
	color:#616f78;
	}
.list_count{
	font-size:12px;
	color:#323a3d;
	font-weight:bold;
	}
.Actmonitor_left .x-tree-icon-leaf {
	margin-left:-20px;
}
/*2020-01-19*/
.script_right{
	margin-right:5px;
}
.script_left{
	margin-left:5px;
}
.script_top{
	margin-top:5px;
}
.script_bottom{
	margin-bottom:5px;
}

.left_edge{
	border-left:1px solid #dddddd;
}
.right_edge{
	border-right:1px solid #dddddd;
}
.bottom_edge{
	border-bottom:1px solid #dddddd;
}
.top_edge{
	border-top:1px solid #dddddd;
}
.customize_body .prompt_info_blue{
	background-color:#e5f1fe;
	border-radius:4px;
	padding:8px 0 8px 10px;
}
.customize_body .prompt_info_blue .x-toolbar-text{
	color:#007af5;
	font-size:12px;
}

.customize_body .prompt_info_green{
	background-color:#d4f7ef;
	border-radius:4px;
	padding:8px 0 8px 10px;
}
.customize_body .prompt_info_green .x-toolbar-text{
	color:#27d7b0;
	font-size:12px;
}

.customize_body .prompt_info_yellow{
	background-color:#fef0dc;
	border-radius:4px;
	padding:8px 0 8px 10px;
}
.customize_body .prompt_info_yellow .x-toolbar-text{
	color:#f8b551;
	font-size:12px;
}

.customize_body .prompt_info_red{
	background-color:#fbdbde;
	border-radius:4px;
	padding:8px 0 8px 10px;
}
.customize_body .prompt_info_red .x-toolbar-text{
	color:#ec4b5a;
	font-size:12px;
}

/*2021-03-09*/
.customize_body .x-form-trigger-wrap .x-form-text:focus { 
		outline-style: none;
	}
.customize_body .x-form-text:focus { 
		outline-style: none;
	}


.customize_body .x-btn-default-toolbar-small-disabled{
	opacity:0.5
}
.customize_body .Common_Btn.customize_btn_disabled{
	border: 1px solid #e0e0e0;
	background:#e0e0e0;
	
}
.customize_body .Common_Btn.customize_btn_disabled span{
	color:#666666;
}
.customize_body .bottom_margin{
	padding-bottom:15px;
}
/*2021-03-11*/
.customize_body .dbsourBtn{
	padding:3px 6px;
	margin:-6px 0 0 5px
}
.customize_calendar_cen{
	margin:10px auto 0px auto !important;
}
.customize_calendar_cen .customize_tim_1{
	width:49px !important
}
.customize_calendar_cen .customize_tim_2{
	width:66px !important
}
.customize_calendar_cen .customize_tim_3{
	width:66px !important
}

.customize_grid_back{
	background-color:#ffffff;
	border-radius:6px;
}
.customize_body .customize_grid_back .x-panel-header-default-horizontal{
	padding: 0px 0px 0px 0px;
}

.customize_body .x-panel-header-text-container-default-framed{
	color:#46545d;
	font-size:18px;
	font-weight: bold;
	font-family: arial,Microsoft Yahei;
	line-height:27px;
	padding: 1px 0 0;
	text-transform: none
}
.customize_body .x-panel-default-framed {
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	-ms-border-radius: 4px;
	-o-border-radius: 4px;
	border-radius: 4px;
	padding: 0;
	border-width: 0px;
	border-style: solid;
	background-color: white
}


.customize_body .alarm_module .x-form-text {
	padding: 4px 0 3px 10px;
	color:#46545d;
	background: #fafafa repeat-x 0 0;
	border-width: 1px;
	border-style: solid;
	border-color:#d9d9d9;
	height: 34px;
	border-radius:2px;
	line-height: 34px;
	font-weight:bold;
}
.customize_sso .x-panel-body-default{
	background:#ffffff;
}

/*2022-03-30*/
.customize_body .pro_editor .x-form-display-field{
	margin-top:9px
}
		
.customize_body .pro_editor .x-tool{
 	left:0px !important;
}
.customize_body .pro_editor .x-tool-close{
 	background-image:url(../rewrite_images/tools/x-tool-collapse-right.png);
}



.x-grid-row-red-em{
	color:red;
}

.x-grid-row-blue-em{
	color:blue;
}


.x-tool-import-icon{
	background-image:url(../rewrite_images/tools/import_icon.png);
	width:20px;
	height:20px;
	background-size:100% 100%;
	background-position:0 0;
	opacity:1;
}
.x-tool-export-icon{
	background-image:url(../rewrite_images/tools/export_icon.png);
	width:20px;
	height:20px;
	background-size:100% 100%;
	background-position:0 0;
	opacity:1;
}
/*open*/
.ux-uxSliderToggle-toggled .x-slider-horz{
	background-color:#007af5 !important;
}
.ux-uxSliderToggle-toggled .x-slider-horz, .x-slider-horz .x-slider-end, .x-slider-horz .x-slider-inner{
	background-image:none !important;
}
.ux-uxSliderToggle-toggled .x-slider-horz{
	border-radius:30px !important;
}
.ux-uxSliderToggle-toggled .x-slider-horz .x-slider-thumb{
	background-image:none !important;
	background-color:#ffffff !important;
	border-radius:100% !important;
	width:12px !important;
	height:12px !important;
	margin-top:3px !important;
	margin-left:-9px !important;
}
.ux-uxSliderToggle-toggled .x-slider-horz .x-slider-end{
	padding-right:6px !important;
}
.ux-uxSliderToggle-toggled .x-slider-inner:before{
	position:absolute !important;
	top:0px !important;
	left:0px !important;
	content: '开启' !important;
	color: #fff !important;
	font-size:12px !important;
}

/*close*/
.ux-uxSliderToggle{
	cursor:pointer;
}
.ux-uxSliderToggle .x-slider-horz .x-slider-inner{
	height:18px;
}
.ux-uxSliderToggle .x-slider-horz{
	background-color:#99a6bb;
}
.ux-uxSliderToggle .x-slider-horz, .x-slider-horz .x-slider-end, .x-slider-horz .x-slider-inner{
	background-image:none;
}
.ux-uxSliderToggle .x-slider-horz{
	border-radius:30px;
}
.ux-uxSliderToggle .x-slider-horz .x-slider-thumb{
	background-image:none;
	background-color:#ffffff;
	border-radius:100%;
	width:12px;
	height:12px;
	margin-top:3px;
	margin-left:-4px;
}
.ux-uxSliderToggle .x-slider-inner:before{
	position:absolute;
	top:0px;
	left:12px;
	content: '关闭';
	color: #fff;
	font-size:12px;
}
/*2022-11-2 工具执行*/
.customize_fywidth{
	width:430px !important;
}
.customize_body .customize_gdt{
	overflow-y:scroll;
}
.customize_gdt .x-panel-body{
	height:50px !important;
}
/*工具箱附件 添加透明背景*/
.customize_panel_trap .x-panel-header{
	background: none !important;
}
.customize_panel_trap .x-panel-header .x-header-text{
	font-size: 14px !important;
	padding-left:60px ;
}

.customize_body .x-tab-bar-default-horizontal {
	height: auto !important;
}
