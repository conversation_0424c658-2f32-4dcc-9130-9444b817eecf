.report_box{
	margin: auto;
	width:auto!important;
	padding-top:3px;
	}
.report_box .bootstrap-tagsinput {
  background-color: white;
  border: 1px solid #ebedef;
  border-radius: 6px;
  margin-bottom: 18px;
  padding: 6px 1px 1px 6px;
  text-align: left;
  font-size: 0;
  min-height:70px;
  max-height:100px;
  overflow-y:auto;
}

.report_box .bootstrap-tagsinput .tag {
  border-radius: 4px;
  background-color: #ebedef;
  color: #7b8996;
  font-size: 13px;
  cursor: pointer;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  overflow: hidden;
  margin: 0 5px 5px 0;
  padding: 6px 28px 6px 14px;
  transition: .25s linear;
}

.report_box .bootstrap-tagsinput .tag > span {
  color: #46545d;
  padding: 0 10px 0 0;
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  right: 0;
  text-align: right;
  text-decoration: none;
  top: -2px;
  width: 100%;
  bottom: 0;
  z-index: 2;
}

.report_box .bootstrap-tagsinput .tag > span:after {
  content: "x";
  font-family: "Flat-UI-Pro-Icons";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 27px;
}

.report_box .bootstrap-tagsinput input[type="text"] {
  font-size: 14px;
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  margin: 0;
  width: auto !important;
  max-width: inherit;
  min-width: 80px;
  vertical-align: top;
  height: 29px;
  color: #34495e;
}

.report_box .tagsinput-primary {
  margin-bottom: 18px;
  display:flex;
  padding:0 0;
  width:99.5%
}

.report_box .tagsinput-primary .bootstrap-tagsinput {
  display:block;
	border: 1px solid #dddfeb;
	border-radius: 2px;
	width:100%;
}

.report_box .tagsinput-primary .tag {
  background-color: #fafafa;
  color: #46545d;
  border:1px solid #dddddd
}
.report_box .btn{background: #1ABC9C;border: none;color: #fff;padding: 10px;border-radius: 5px;margin-top: 10px;}

.report_box .s_tit{
	display:inline-block;
	width:75px;
	height:70px;
	text-align:right;
	margin-right:5px;
	display:flex;
	justify-content: flex-end;
	align-items: center;
	padding-right:10px;
}
.report_box .customize_additem{
	height:auto!important
}

.customize_additem .Common_Btn{
	top:25px!important
}
.customize_dist{
	margin-top:30px
}
.report_box.unclickable .tagsinput-primary .tag{
    pointer-events:none;
    cursor:default;
}
.report_box.unclickable .bootstrap-tagsinput input{
    pointer-events:none;
    cursor:default;
}
