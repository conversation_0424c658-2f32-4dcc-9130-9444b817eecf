.product_box{
	position: relative;
	float:left;
}
.product_link {
	font-size: 12px;
	line-height:50px;
	color: #46545d;
	margin:0 0 0 2px;
	cursor:pointer;
	display: inline-block;
}
.product_layer {
	width:calc(100% - 90px);
	height: auto;
	background-color:rgba(255,255,255,0.9);
	box-shadow:0 1px 10px 1px rgba(0,122,245,0.3);
	position: fixed;
	z-index: 100000000;
	left:70px;
	top: 50px;
	padding: 5px;
	border-radius:8px;
}
.product_layer:before{
	content: "";
	border-style: solid;
	border-width: 0 10px 10px 10px;
	border-color: transparent transparent #ffffff transparent;
	height: 0px;
	position: absolute;
	top:-10px;
	left:60px;
	width: 0px;
}
.pro_menu_icon{
	width:30px;
	height:30px;
	display:block;
	margin:0 auto 5px auto;
}
.pro_menu_box{
	text-align:center;
	width:97px;
	height:97px;
	background-color:#455165;
	border-radius:4px;
	margin:10px 5px;
	display:inline-flex;
	color:#fff;
	flex-direction:column;
	align-items: center;
	flex-wrap: wrap;
	justify-content:center;
	cursor:pointer;
}
.pro_menu_box:hover{
	background-color:rgba(69,81,101,0.5);
}
.product_link:before{
	content: "";
	border-style: solid;
	border-width: 5px 5px 0 5px;
	border-color: #99a6bb transparent transparent transparent;
	height: 0px;
	position: absolute;
	right: -11px;
	top: 50%;
	margin-top: -3px;
	width: 0px;
	}
.product_link.active{
	color:#007af5;
}
.product_link.active:before{
	content: "";
	border-style: solid;
	border-width: 0 5px 5px 5px;
	border-color: transparent transparent #007af5 transparent;
	height: 0px;
	position: absolute;
	right: -11px;
	top: 50%;
	width: 0px;
	}

.product_layer .grid_panel{
	width: 87px;
	height: auto;
	border:0px solid #f6f6f7;
	margin:5px 0px;
}
.product_layer .grid_panel:hover{
	background-color:transparent;
}
.product_layer .grid_icon_bg{
	width:36px;
	height:36px;
}
.product_layer .grid_icon{
	width:18px;
	height:18px;
}
.product_layer .grid_panel:hover .grid_icon_bg{
	background-color:#74b8f9;
	background-image: -webkit-linear-gradient(top, #74b8f9 0%, #74b8f9 100%);
  	background-image: -moz-linear-gradient(top, #74b8f9 0%, #74b8f9 100%);
  	background-image: -o-linear-gradient(top, #74b8f9 0%, #74b8f9 100%);
  	background-image: linear-gradient(to bottom, #74b8f9 0%, #74b8f9 100%);
	box-shadow: #bbdcfc 0 0px 0px 0px;
}
.product_layer .grid_panel h1{
	font-size:12px;
}
.product_layer .grid_panel:hover h1{
	color: #007af5;
}
.product_layer .grid_panel:hover .grid_icon_bg .grid_icon{
	background-position:0px 0px !important;
}

@media screen and (min-width: 1000px) and (max-width: 1280px){
  .product_layer {
		width:calc(85% - 90px);
	}
	.product_layer .grid_panel{
		margin:10px 0px;
	}
}

@media screen and (min-width: 1360px) and (max-width: 1366px){
  .product_layer {
		width:calc(80% - 90px);
	}
	.product_layer .grid_panel{
		margin:10px 0px;
	}
}

@media screen and (min-width: 1400px) and (max-width: 1440px){
  .product_layer {
		width:calc(76% - 90px);
	}
	.product_layer .grid_panel{
		margin:10px 0px;
	}
}
@media screen and (min-width: 1600px) and (max-width: 1768px){
   .product_layer {
		width:calc(75% - 90px);
	}
}


