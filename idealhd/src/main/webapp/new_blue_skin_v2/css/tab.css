/*

bright: rgb(234,242,255);
normal: rgb(120,172,255);
dark:	rgb(0,66,174);

*/

.dynamic-tab-pane-control.tab-pane {
	width:99%;
	position: relative;
	top: 5px;
	text-align: left;
	margin-bottom: 5px;
}

.dynamic-tab-pane-control .tab-row .tab {
	font-family: Arial;
	font-size:		12px;
	cursor:			Default;
	display:		inline;
	margin:			1px -1px 1px 0px;
	float:			left;
	padding:		3px 10px 3px 10px!important;
	padding:		4px 10px 2px 10px;
	background: url(../images/tab.gif) repeat-x;
	border:1px solid #91b1df;
	cursor:hand;
	cursor:pointer;
	z-index:	1;
	position: relative;
	top:			0;
}

.dynamic-tab-pane-control .tab-row .tab.selected {
	border:	1px solid #91b1df;
	border-bottom:	0px;
	z-index:		3;
	padding:		4px 10px 3px 10px!important;
	padding:		5px 10px 2px 10px;
	margin:			1px 0px 0px 0px;
	top:			-1px;
	background: url(../images/tabSelected.gif) repeat-x;
	font-size: 12px;
	font-weight: normal;
}

.dynamic-tab-pane-control .tab-row .tab a {
	font-family:		Verdana, Helvetica, Arial;
	font-size:			12px;
	color: Black;
	text-decoration:	none;
	cursor:			hand;
	cursor:			pointer;
	font-weight:	normal;
}

.dynamic-tab-pane-control .tab-row .hover a {
	color: #1B3E76;
}

.dynamic-tab-pane-control .tab-row .tab.selected a {
	font-weight:	normal;
}

.dynamic-tab-pane-control .tab-page {
	clear:			both;
	background:		 white;
	z-index:		2;
	position:		relative;
	top:			-2px;
	color:			Black;
	font-family:	Verdana, Helvetica, Arial;
	font-size:		12px;
	margin: 0px;
	padding:0px;
	border:1px solid #91b1df;
	overflow: auto;
	text-align: left;
}
.dynamic-tab-pane-control .tab-page table{
	border-collapse:collapse;
	width:100%;
}
.dynamic-tab-pane-control .tab-page table th{
	border:1px solid White;
}
.dynamic-tab-pane-control .tab-row {
	z-index:		1;
	white-space:	nowrap;
	background: transparent;
	height:			1.85em;
	width:			100%;
}
