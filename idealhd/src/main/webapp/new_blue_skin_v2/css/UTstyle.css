/*
	CSS 
	Created 2006-9-25 by ds_wang
	For Entegor(User Task)
*/
/*
	Global Style
*/
body
{
background:#fff;}
a:link {
	color: #000000;
	font-family: Arial;
	font-size: 12px;
	text-decoration: none;
}
a:visited {
	color: #1B3E76;
	text-decoration: none;
}
a:hover {
	color: #1B3E76;
	text-decoration: underline;
}
a:active {
	color: #1B3E76;
}
body {
	font-family: Arial;
	font-size: 12px;
	margin: 0px;
	overflow: auto;
	padding: 0px;
	scrollbar-3dlight-color: #C7C7C7;
	scrollbar-arrow-color: #3749A1;
	scrollbar-base-color: #3749A1;
	scrollbar-darkshadow-color: #CACACA;
	scrollbar-face-color: #E4E4E4;
	scrollbar-highlight-color: #FFFFFF;
	scrollbar-shadow-color: #D7D7D7;
	scrollbar-track-color: #F5F5F5;
}

div {
	margin: 0px 0px 0px 0px;
	padding: 0px 0px 0px 0px;
}
form {
	margin: 0px 0px 0px 0px;
	padding: 0px 0px 0px 0px;
}
html>body .datagridcontainer table th .sortable{
	background: url(../images/sortable.gif) no-repeat right center;
}
html>body .layoutcontainer{
	width: 98.9%;
}
html>body .stateicon img{
	margin-left: 5px;
}
.operates
{
float:right;
margin-right:5px;

}
.showAll
{
float:left;
margin-left:10px;
margin-top:3px;

}
.format_excel_xml
{
width:150px;
height:30px;
margin-top:18px;
margin-left:10px;
float:left;
clear:left;
}
.excel_image
{
	width:14px;
	height:14px;
	margin-right:2px;
	vertical-align: middle;
	
}
.xml_image
{
width:14px;
height:14px;
vertical-align: middle;}
table.sortable {
	border: 1px solid #666666;
	margin: 20px 0 20px 0;
	width: 80%;
}
td {
	font-size: 12px;
}
ts {
	font-size: 18px;
}
th a,th a:visited {
	color: #000000;
	font-size: 12px;
}
th a:link,
th a:visited,
th a:hover,
th a:active {
	color: #000000;
}
th a:hover {
	color: #000000;
	font-size: 12px;
	text-decoration: underline;
}
.sortable{
	background: url(../images/sortable.gif) no-repeat right;
}
.sorted{
	background-color: #b9cfe9;
}
.order1 {
	background: url(../images/up.gif) no-repeat right center;	
}
.order2 {
	background: url(../images/down.gif) no-repeat right center;
}
thead tr {
	background-color: #C8DCFF;
}
tr.odd {
	background-color: #FFFFFF;
}
tr.tableroweven,tr.even {
	background-color: #EBF1FB;
}
.allflow{
	left: 5px;
	position: relative;
	top: 5px;
}
.allflow input{
	margin: 0px;
}
.allflow select{
	margin: 0px 5px 0px 0px;
}
.bell{
	float: right;
	margin-right: 11px!important;
	margin-right: 8px;
}
.bell img{
	border-left: thin groove;
	border-right: thin groove;
	border:0;
}
.bottom {
	background: url(../images/statebar_bg.gif);
	height: 54px;
	padding: 3px;
}
.btn_01 {
	background-color: transparent;
	background-image: url(../images/btnNormal.gif);
	background-repeat: no-repeat;
	border: 0px none;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	height: 21px;
	margin: 0px 3px 0px 3px;
	padding: 1px 0px 0px 0px;
	text-align: center;
	width: 117px;
}
.btn_02 {
	background-color: transparent;
	background-image: url(../images/btn_02.gif);
	background-repeat: no-repeat;
	border: 0px none;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	height: 21px;
	margin: 0px 3px 0px 3px;
	padding: 1px 0px 0px 0px;
	text-align: center;
	width: 80px;
}
.btn_03 {
	background-color: transparent;
	background-image: url(../images/btn_03.gif);
	background-repeat: no-repeat;
	border: 0px none;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	height: 21px;
	margin: 0px 3px 0px 3px;
	padding: 1px 0px 0px 0px;
	padding-top: 1px;
	width: 50px;
}
.btn_04 {
	background-color: transparent;
	background-image: url(../images/btn_04.gif);
	background-repeat: no-repeat;
	border: 0px none transparent;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	height: 21px;
	margin: 0px 3px 0px 3px;
	padding: 1px 0px 0px 0px;
	width: 30px;
}
.btn_update {
	background-color: transparent;
	background-image: url(../images/btn_a.gif);
	background-repeat: no-repeat;
	border: 0px none transparent;
	font-size: 12px;
	height: 21px;
	margin: 3px 3px 3px 3px;
	padding-top: 1px;
	width: 71px;
}
.btnlong{
	background-color: transparent;
	background-image: url(../images/btnLong.gif);
	background-repeat: no-repeat;
	border: 0px none transparent;
	font-size: 12px;
	height: 23px;
	margin: 3px 3px 3px 3px;
	padding: 1px 0px 3px 0px!important;
	padding: 1px 0px 0px 0px;
	width: 100px;
}
.btnnormal{
	background: url(../images/btnNormal.gif) no-repeat;
	border: 0px none transparent;
	font-size: 12px;
	height: 23px;
	margin: 3px 3px 3px 3px;
	padding: 1px 0px 3px 0px!important;
	padding: 1px 0px 0px 0px;
	width: 72px;
}
.btnNormal{
	background: url(../images/btnNormal.gif) no-repeat;
	border: 0px none transparent;
	font-size: 12px;
	height: 23px;
	margin: 3px 3px 3px 3px;
	padding: 1px 0px 3px 0px!important;
	padding: 1px 0px 0px 0px;
	width: 72px;
}
.btnnormal_promulgate{
	background:url(../images/btnNormal.gif) no-repeat;
	border: 0px none transparent;
	font-size: 12px;
	height: 23px;
	margin: 10px 3px 3px 88px;
	padding: 1px 0px 3px 0px!important;
	padding: 1px 0px 0px 0px;
	width: 72px;
}
.promulgate_btn
{

}
.calendartd input{
	float: left;
	margin: 2px 3px 1px 0px;
	padding: 2px 2px 0px 2px;
}
.clocmenu0{
	padding-top: 5px;
	text-align: center;
}
.datagridcontainer{
	clear: both;
	left: 5px;
	overflow: auto;
	position: relative;
	top: 5px;
	width: 99%;
	border: 1px solid #D8E2F1;
}

.datagridcontainer_w{
	clear: both;
	left: 5px;
	overflow: auto;
	position: relative;
	top: 5px;
	height:82%;
	width: 99%;
	border: 1px solid #D8E2F1;
}

.datagridcontainer_ww{
	clear: both;
	left: 5px;
	overflow: auto;
	position: relative;
	top: 5px;
	height:90%;
	width: 99%;
	border: 1px solid #D8E2F1;
}

.datagridcontainer table{
	background:#ffffff;
	border: 0px none;
	border-collapse: collapse;
	width: 100%;
	border: 1px solid #D8E2F1;
}
.datagridcontainer table td{
	padding: 1px 0px 1px 0px;
	border: 2px solid White;
}
.datagridcontainer table th{
	background-color: #D5DFF1;
	border: 2px solid #FFFFFF;
	border-collapse: collapse;
	color: #000000;
	font-weight: bold;
	text-align: center;
	padding: 3px 14px 3px 0px;
	white-space: nowrap;
}
.datagridcontainer table .sorted{
	background-color: #c5dbf6;
}
.dotline {
	background-image: url(../images/dotline.gif);
	height: 1px;
}
.edit_table_title {
	color: #2030A1;
	font-weight: bold;
}
/* enrollment */
.enrollment_table {
	background: #cae0ff;
	border: 1px solid #276DD2;
	filter: progid:DXImageTransform.Microsoft.Shadow 
(Color=#456796,Direction=140,strength=3);
	margin-left:auto;
	margin-right:auto;
	margin-top:20px;
	height: 450px!important;
	width: 450px!important;
	width: 450px;
	
}
.exit_table {
	background: #cae0ff;
	border: 1px solid #276DD2;
	filter: progid:DXImageTransform.Microsoft.Shadow 
(Color=#456796,Direction=140,strength=3);
margin-left:auto;
margin-right:auto;
margin-top:20px;
height: 180px!important;
	height: 200px;
	width: 450px!important;
	width: 450px;
	
}
.enrollment_user_image {
	background: url(../images/enrollment_user.jpg);
	background-repeat: no-repeat;
	height: 85px;
	width: 96px;
	position:relative;
	left:150px !important;
	left:160px;
	bottom:30px !important;
	bottom:10px;
}
.exit_user_images
{
	background: url(../images/enrollment_user.jpg);
	background-repeat: no-repeat;
	height: 85px;
	width: 96px;
	margin-left:345px !important;
	margin-left:330px;

}
.errors{
	background: url(../images/iconErrors.gif) no-repeat left top;
	color: #FF0000;
	height: 23px;
	left: 5px;
	line-height: 23px;
	padding: 0px 0px 0px 0px;
	position: relative;
	text-align: left;
	text-indent: 35px;
	top: 5px;
	width: 99%;
}
.individual_length_input {
	border: 1px solid #91b1df;
	padding: 2px 2px 0px 2px;
	line-height:12px;
	background:#ffffff;
	height:26px;
}
.fix_length {
	border: 1px solid #799dd2;
	padding: 2px 2px 0px 2px;
	width: 160px;
	line-height:12px;
}
.fix_length_taskdetail {
	border: 1px solid #799dd2;
	padding: 2px 2px 0px 2px;
	width: 160px;
	line-height:16px;
}
.fix_length_file {
	border: 1px solid #799dd2;
	padding: 2px 2px 0px 2px;
	width: 330px;
}
.fix_length_list {
	border: 1px solid #799dd2;
	height: 120px;
	padding: 2px 2px 0px 2px;
	width: 160px;

}
.fix_length_number {
	border-bottom: 1px solid #FFFFFF;
	border-left: 1px solid #6C92A8;
	border-right: 1px solid #FFFFFF;
	border-top: 1px solid #6C92A8;
	margin: 3px 5px 3px 5px;
	padding: 1px 2px 1px 2px;
	width: 60px;
}
.fieldset_td
{
width:99%;
text-align:left;
FONT-SIZE: 12px;
font-family: Verdana; 
position:relative;
top:5px;
left:2px;
}

.fix_length_select {
	width: 160px;
}
.fix_length_short {
	border: 1px solid #407FCA;
	margin: 8px 0px 8px 0px;
	padding: 3px 2px 0px 2px;
	width: 120px;
}
.fix_length_textarea {
	border: 1px solid #6C92A8;
	height: 80px;
	padding: 3px 2px 0px 2px;
	width: 160px;
}
.fix_length_time {
	border: 1px solid #407FCA;
	margin: 0px 5px 0px 5px;
	padding: 3px 2px 0px 2px;
	width: 30px;
}
.flownotes{
	border: 1px solid #6C92A8;
	clear: both;
	float: left;
	height: 122px;
	margin: 0px;
	padding: 2px 2px 0px 2px;
	width: 522px;
}
.format{
	background: url(../images/formatBg.gif) repeat-x;
	border: 1px solid #6C92A8;
	border-bottom: 0px none;
	clear: both;
	float: left;
	height: 23px;
	margin: 0px 0px 0px 0px;
	padding: 0px;
	width: 522px;
	width: 520px!important;
}
.fullwidth {
	width: 100%;
}
.header {
	margin: 0px;
	padding: 0px;
	width: 100%;
	border-collapse: collapse;
}
.header_bg {
	background: url(../images/header_top_bg.gif) repeat-x;
}
.header_bg_table {
	background: #D5DFF1;
	padding:3px;
	text-align:center;
}
.header_logo {
	width: 105px;
}
.header_menubar_bg {
	background: url(../images/header_menubar_bg.gif) repeat-x;
}
.header_menubar_nav {
	background: url(../images/header_menubar_bg.gif) repeat-x;
	font-family: arial;
	font-size: 12px;
	padding-left: 10px;
	height: 34px;
}
.header_menubar_right {
	background: url(../images/header_menubar_bg.gif) repeat-x;
}
.header_right{
	background: #e8f2ff;
	width: 147px;
}
.header_top {
	width: 471px;
	height: 36px;
}
.headtable {
	border: 1px solid #A3B2CC;
	font-size: 12px;
	padding: 0px;
}
.headtable th  a,.headtable th a:visited {
	color: #800080;
	font-size: 12px;
}
.headtable th a:link {
	color: #800080;
	font-family: Arial;
	font-size: 12px;
	text-decoration: none;
}
.headtable th a:hover {
	color: #800080;
	font-size: 12px;
	text-decoration: underline;
}
.import_text {
	height: 80%;
	position: relative;
	right: 45px !important;
	right: 28px;
	width: 90%;
}
.information {
background: #E8F2FF;
	border: 1px solid #91b1df;
	margin: 5px 0px 5px 0px;
	width: 100%;
}
.tab-pane
{
	margin: 3px 0px 0px 0px;
	width: 100%;
}
.information td {
	padding-bottom: 0px;
	padding-top: 3px;
}
.information th {
	color: #2030A1;
	font-size: 12px;
	font-weight: bold;
	padding-left: 1px;
	text-align: left;
}
.input_import {
	bottom: 60px;
	left: 20px!important;
	left: 0px;
	list-style-type: none;
	margin-top: 15px !important;
	margin-top: 12px;
	position: relative;
	text-align:center;
}
.label {
	color: #1B3E76;
	padding: 0px 3px 0px 3px;
	text-align: right;
	white-space: nowrap;
}
.label_left {
	color: #2030A1;
	padding: 0px 3px 0px 3px;
	text-align: left;
	white-space: nowrap;
}
.labelright {
	color: #000000;
	font-family: "Verdana", "Arial", "Helvetica", "sans-serif";
	font-size: 12px;
	font-weight: bold;
	text-align: right;
	white-space: nowrap;
	white-space: nowrap;
}
.layoutcontainer{
	background: white;
	left: 5px;
	margin-bottom: 5px;
	position: relative;
	text-align: center;
	top: 5px;
	width: 99%;
}
.layoutcontainer input{
	margin-right: 2px;
}
.layoutcontainer table{
	margin: 8px auto 0px auto;
}
.layoutcontainer td{
	padding: 1px 1px 1px 1px;
}
.layoutcontainer table .sorted{
}
.leftspace{
	background: url(../images/leftSpace.gif) repeat-y right;
	width: 15px;
}
.link {
	color: #800080;
	font-family: Arial;
	font-size: 12px;
	text-decoration: none;
}
.list_table_inner {
	border: 1px solid #407FCA;
	color: #000000;
	margin: 2px 2px 2px 2px;
	width: 98%;
}
.list_table_inner td {
	font-size: 12px;
	padding-bottom: 10px;
	padding-left: 3px;
	padding-right: 3px;
	padding-top: 10px;
	text-align: left;
}
.list_table_inner th {
	font-size: 12px;
	padding: 3px;
	text-align: left;
}
.list_table_items {
	background-color: #7CA4E0;
	border: 1px solid #407FCA;
	color: #000000;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	margin: 5px 0px 5px 0px;
	margin: 2px;
	width: 100%;
}
.list_table_items td {
	padding-bottom: 4px;
	padding-left: 4px;
	padding-right: 4px;
	padding-top: 4px;
	text-align: left;
}
.list_table_items th {
	background-color: #C8DCFF;
	text-align: left;
}
.login_button {
	background-image: url(../images/enter_login.jpg);
	background-repeat: no-repeat;
	border: 0px none;
	font-size: 12px;
	width:67px;
	height: 23px;
	padding: 0px 0px 3px 0px!important;
	padding: 1px 0px 0px 0px;

}
.login_button_place {
margin:auto
}
.login_font {
margin:auto;
	color: #000100;
	font-size: 12px;
	list-style-type: none;
}
.login_input {
	border: 1px solid #799dd2;
	height: 22px;
	width: 140px;
}
.login_table {
	background: #cae0ff;
	border: 1px solid #276DD2;
	filter: progid:DXImageTransform.Microsoft.Shadow(Color=#456796,Direction=140,strength=3);
	margin:auto;
	margin-top:70px;
	width: 370px !important;
	width: 360px;
	height: 219px;
}
.login_username {
	margin: auto;
	padding-top: 20px;
}
.main{
	background-color: White;
	height: 80%;
	left: 10px;
	position: relative;
	top: 0px;
	vertical-align: top;
	width: 98.3%;
}
.main_w{
	background-color: White;
	height: 98%;
	left: 10px;
	position: relative;
	top: 0px;
	vertical-align: top;
	width: 98%;
}
.notes {
	color: #000000;
	display: block;
	left: 8px;
	margin: 0px 0px 0px 0px;
	position: relative;
	text-align: left;
	top: 5px;
	width: 98%;
}
.notes_td {
	color: #000000;
	display: block;
	margin: 0px 0px 0px 0px;
	position: relative;
	text-align: left;
	width: 100%;
}
.operation_bar {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-weight: normal;
	margin: 10px 5px 5px 5px;
	text-align: right;
}
.operationbarindex {
	font-family: Arial;
	font-size: 12px;
	font-weight: normal;
	height: 35px;
	list-style: none;
	margin: 5px 0px 5px 5px;
	padding: 0px;
	width: 98%;
}
.operationbarindex input{
	margin: 5px 0px 3px 0px;
}
.operationbarindex li{
	float: left;
	height: 28px;
	line-height: 35px;
	margin-left: 5px;
	vertical-align: middle;
}
.remark_textarea {
	border: 1px solid #91b1df;
	height: 100px;
	overflow: auto;
	padding: 3px 5px 0px 5px;
	width: 400px;
}
.remark_textarea_100 {
	height: 150px;
	overflow: auto;
	width: 100%;
	border: 1px solid #91b1df;
	line-height:12px;
	background:#ffffff;
	margin-bottom:10px;
}
.remark_textarea_250 {
	height: 350px;
	overflow: auto;
	width: 100%;
	border: 1px solid #91b1df;
	line-height:12px;
	background:#ffffff;
	margin-bottom:10px;
}

.reportforms_copyright {
	background-image: url(../images/copyright.gif);
	background-repeat: no-repeat;
	height: 23px;
	margin-left: 80%;
	width: 196px;
}
.reportforms_input_text {
	border: 1px solid #799dd2;
	height: 21px;
	padding: 2px 2px 0px 2px;
	text-align: left;
	width: 174px;
}
.required{
	color: #FF0000;
}
.rightspace{
	background: url(../images/rightSpace.gif) repeat-y;
	width: 15px;
}
.rowdark {
	background-color:#EBF1FB;
}
.rowlight {
	background-color: #FFFFFF;
}
.startflow_table_length {
	width: 98%;
}
.statebar {
	background:#d4e3ff url(../images/bottom_bg.gif) repeat-x;
	width: 100%;
	color: #585858;
	padding-top:5px; 
	border-bottom:1px solid #d4e3ff;
}
.statebar td{
	padding-top:5px; 
}
.statebar_frequency {
	width: 125px;
}
.statebar_frequency input {
	border-bottom: 1px solid #FFFFFF;
	border-left: 1px solid #7CA4E0;
	border-right: 1px solid #FFFFFF;
	border-top: 1px solid #7CA4E0;
	color: #1B3E76;
	padding: 1px 3px 0px 4px;
	width: 70px;
}
.statebar_refresh {
	background: #E2EFFF;
	color: #1B3E76;
	height: 22px;
	padding: 1px 3px 0px 5px;
	vertical-align: middle;
	width: 70px;
}
.statebar_refresh td {
	color: #1B3E76;
}
.statebar_td {
	background: #E2EFFF;
	border-bottom: 1px solid #FFFFFF;
	border-left: 1px solid #7CA4E0;
	border-right: 1px solid #FFFFFF;
	border-top: 1px solid #7CA4E0;
	color: #1B3E76;
	padding: 1px 3px 0px 5px;
	vertical-align: middle;
}
.stateicon{
	padding: 2px 3px 2px 10px;
}
.stateicon img{
	margin-left: 0px;
}
.stateuser{
	background: url(../images/iconUser.gif) no-repeat 8px 4px;
	padding: 2px 3px 2px 32px;
	text-align: left;
	 vertical-align: top;
}
.summarytitle {
	border-bottom-style: solid;
	border-bottom-width: 1px;
	border-left-width: 1px;
	border-right-width: 1px;
	border-top-width: 1px;
	color: #0033FF;
	font-size: 12px;
	padding: 4px;
}
.table {
	border: 1px solid #A3B2CC;
	font-size: 12px;
	padding: 0px;
}
.on-line
{
width:98%;
height:98%;
border:1px solid #7F9DB9;}
.tablecontainer {
	border-left: 1px solid #407FCA;
	border-right: 1px solid #407FCA;
	border-top: 1px solid #407FCA;
	height: 370px;
	vertical-align: top;
}
.tablecontainer table {
	font-size: 12px;
	width: 100%;
	border-collapse: collapse;
}
.tableContainer table th {
	background-color: #D5DFF1;
	border: 2px solid #FFFFFF;
	border-collapse: collapse;
	color: #000000;
	font-weight: bold;
	text-align: center;
	padding: 3px 0px 3px 0px;
}
.tablecontainer textarea {
	overflow: auto;
	width: 100px;
}
.tableitem {
	font-size: 12px;
	text-align: center;
}
.tableoperate {
	background-color: #D5DFF1;
	border: 1px solid #50809B;
	padding: 3px;
	text-align: center;
	width: 100%;
}
.tabletitle {
	background-color: #D5DFF1;
	border: 1px solid #A3B2CC;
	border-bottom:none;
	font-size: 12px;
	font-weight: bold;
	text-align: center;
}

.tab-page table th{
	background-color: #D5DFF1;
	color: #000000;
	font-size: 12px;
	font-weight: normal;
	margin:0px;
	padding:0px;
	text-align:center;
	height:22px;
	vertical-align:middle;	
}
.td_right {
	text-align: right;
}

.top_td {
	height: 70px;
}
.user_login {
	background-image: url(../images/user_login.jpg);
	background-repeat: no-repeat;
	height: 60px;
	width: 63px;
	margin-top:30px;
	margin-left:20px;
	
	
}
.value {
	text-align: left;
}
.value1 {
	background-color: #FFFFFF;
	border: 1px solid #6C92A8;
	font-family: "Verdana", "Arial", "Helvetica", "sans-serif";
	font-size: 12px;
	height: 22px;
	margin: 2px;
	vertical-align: top;
	white-space: nowrap;
}
.valuelongtext {
	border: 1px solid #91b1df;
	padding: 2px 2px 0px 2px;
	line-height:12px;
	background:#ffffff;

}
.welcomeimg {
	background: url(../images/wellcome.jpg);
	height: 275px;
	margin: 40px auto 0px 80px;
	width: 520px;
}
.operatesBar{
	background:#EEF4F8;
	border: 1px solid #6CA1C7;
	border-bottom:0px none;
	height: 30px;
	left: 5px;
	position: relative;
	top: 5px;
	width: 98.9%;
}
.result{
	font-family: arial;
	vertical-align: baseline;
	background: url(../images/iconErrors.gif) no-repeat left top;
	border: 1px solid #d8e2f1;
	border-bottom: 0px none;
	color: black;
	height: 23px;
	line-height: 23px;
	padding: 0px 0px 0px 0px;
	position: relative;
	text-align: left;
	text-indent: 35px;
	top: 5px;
	left: 5px;
	width: 99%;
}
.result_individual{
	font-family: arial;
	vertical-align: baseline;
	background: url(../images/iconErrors.gif) no-repeat left top;
	border-bottom: 1px solid #91b1df;
	color: black;
	height: 23px;
	line-height: 23px;
	padding: 0px 0px 0px 0px;
	position: relative;
	text-align: left;
	text-indent: 35px;
	width: 100%;
}
.pagebanner
{
	font-family: arial;
	vertical-align: baseline;
	background: url(../images/iconErrors.gif) no-repeat left top;
	color: black;
	height: 23px;
	line-height: 23px;
	padding: 0px 0px 0px 0px;
	text-align: left;
	text-indent: 35px;
	width: 100%;
	display: block;
	border-bottom: 1px solid #D8E2F1;
}
.pagebanner_w
{
	font-family: arial;
	vertical-align: baseline;
	color: black;
	height: 20px;
	line-height: 15px;
	padding: 0px 0px 0px 0px;
	text-align: left;
	text-indent: 20px;
	width: 100%;
	display: block;
	border-bottom: 1px solid #D8E2F1;
}
.scollDiv
{
	clear: both;
	overflow: auto;
	width: 100%;
}
.pagination
{
	height:23px;
	float:right;
	margin: 5px 10px 0px 2px;
	width:260px;
}
.pagination a,
.pagination a:link,
.pagination a:visited,
.pagination a:active{
	text-decoration: none;
	margin: 0px 2px 0px 2px;
}
.pagination a:hover{
	text-decoration: underline;
}
.excel {
	background: url(../images/excel.jpg) no-repeat;
	display: block;
	color:trasnparent;
	width:36px;
	height: 14px;
	overflow: hidden;
	text-indent: 50px;
	cursor: hand;
	cursor: pointer;
	position:absolute;
	top:0px;
	left: 85px;
}
.xml {
	background: url(../images/rss.gif) no-repeat;
	display: block;
	color:trasnparent;
	width:36px;
	height: 14px;
	overflow: hidden;
	text-indent: 50px;
	cursor: hand;
	cursor: pointer;
	position:absolute;
	top:0px;
	left: 125px;
}
.pagebanner .excel {
	background: url(../images/excel.jpg) no-repeat;
	display: block;
	color:trasnparent;
	width:36px;
	height: 14px;
	overflow: hidden;
	text-indent: 50px;
	margin-top:5px;
	cursor: hand;
	cursor: pointer;
	position:absolute;
	left: 130px;
}
.pagebanner .xml {
	background: url(../images/rss.gif) no-repeat;
	display: block;
	color:trasnparent;
	width:36px;
	height: 14px;
	margin-top:5px;
	overflow: hidden;
	text-indent: 50px;
	cursor: hand;
	cursor: pointer;
	position:absolute;
	left: 170px;
}
.exportlinks
{
	float: left;
	margin: 5px;
	position:relative;
	width: 260px;
}
.icoCursor{
	cursor: hand;
	cursor: pointer;
}
 .bottom_left{
	height: 23px;
   width: 23px;
	background-image: url(../images/bottom_left.gif);
}
#bottom{
	height: 23px;
	background-image: url(../images/bottom_bg.gif);
}
.bottom_middle{
	color: #585858;
	padding: 3px 12px 0px 0px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
}
.bottomBar01
{
	position: relative;
	left:120px;
	top:5px;
}
.bottomBar02
{
position: relative;
	left:120px;
	top:10px;
}
.bottom_left_length{
	height: 91px;
   width: 23px;
	background-image: url(../images/bottom_left_length.gif);
}
#bottom_length{
	height: 91px;
	background-image: url(../images/bottom_bg_length.gif);
}
.bottom_middle_length{
	color: #585858;
	padding: 3px 12px 0px 0px;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
}
.formTable
{
position:absolute;
left:160px; 
}
.stateuser02{
background-image: url(../images/iconUser.gif);
background-repeat:no-repeat;
background-position:8px 40px !important;
background-position:8px 25px;
padding: 30px 3px 20px 32px !important;
padding: 2px 3px 20px 32px;
position:relative;
top:17px;
}


