@charset "utf-8";
/* CSS Document */
/*Graph1*/
ul{ padding:0; margin:0;}
ul li{ padding:0; margin:0; list-style:none}
img{ 
	border:0;
	cursor:pointer;
	}
.Change_Body{ width:100%; height:100%; font-family:"微软雅黑"; font-size:12px;background-color:#23272b}
.Change_Left{ 
	width:225px;
	height:100%; 
	border:1px solid #15181b;
	box-shadow:3px 3px 4px #16181b inset;
	-webkit-box-shadow:3px 3px 4px #16181b inset;
	-moz-box-shadow:3px 3px 4px #16181b inset;
	-o-box-shadow:3px 3px 4px #16181b inset;
	float:left;
	}
.Change_Left ul li{
	float:right;
	margin:8px 0 0 0;
	font-size:12px;
	line-height:31px;
	}
.Change_Left ul li .System_info{ 
	float:left; 
	width:195px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.Change_Left ul li .System{ 
	float:left;
	border-radius:50%;
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	-o-border-radius:50%;
	width:9px;
	height:9px;
	display:block;
	margin:10px 0 0 0;
	}
.Leftmenu_over_btn{
	cursor:pointer;
	width:209px;
	border-top:1px solid #000000;
	border-left:1px solid #000000;
	border-bottom:1px solid #000000;
	height:31px;
	color:#fff;
	background-color:#3c90cf;
	}
.C_Leftmenu_over_btn{
	border-radius: 5px 0 0 5px;
	-webkit-border-radius: 5px 0 0 5px;
	-moz-border-radius: 5px 0 0 5px;
	-o-border-radius: 5px 0 0 5px;
	}
.Leftmenu_btn{
	cursor:pointer;
	width:209px;
	border-top:1px solid #000000;
	border-left:1px solid #000000;
	border-bottom:1px solid #000000;
	height:31px;
	color:#b5b5b6;
	background:#282c2f;
	background-image: -webkit-linear-gradient(top, #3e4448 0%, #33383c 50%, #282c2f 100%);
  	background-image: -moz-linear-gradient(top, #3e4448 0%, #33383c 50%, #282c2f 100%);
  	background-image: -o-linear-gradient(top, #3e4448 0%, #33383c 50%, #282c2f 100%);
  	background-image: linear-gradient(to bottom, #3e4448 0%, #33383c 50%, #282c2f 100%);
	box-shadow:0px 0px 1px #575c5f inset;
	-webkit-box-shadow:0px 0px 1px #575c5f inset;
	-moz-box-shadow:0px 0px 1px #575c5f inset;
	-o-box-shadow:0px 0px 1px #575c5f inset;
	}
.C_Leftmenu_btn{
	border-radius: 5px 0 0 5px;
	-webkit-border-radius: 5px 0 0 5px;
	-moz-border-radius: 5px 0 0 5px;
	-o-border-radius: 5px 0 0 5px;
	}
.System_yellow{background-color:#fdd000;}
.System_red{ background-color:#ea5971;}
.System_green{ background-color:#27dd81;}
.System_purple{ background-color:#ce7bfc}
.System_blue{ background-color:#65cafa}
.System_gray{ background-color:#c9caca}

.Right_Common{
	width:99%;
	height:100%;
	border-radius:8px;
	-webkit-border-radius:8px;
	-moz-border-radius:8px;
	-o-border-radius:8px;
	border:1px solid #15181b;
	color:#fff;
	margin:0 10px;
	background-position: center;
	}
.Change_Right{
	background-image:url(../images/Change_Bg.png);
	}
.CR_Content{
	height:580px;
	}
.CR_package{ 
	width:234px;
	border-radius: 8px;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	-o-border-radius: 8px;
	border:1px solid #15181b;
	background-color: rgba(35,39,43,0.5);  
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	margin:10px 20px;
	float:left;
	height:auto;
	}
.Package_Title{
	width:100%;
	height:27px;
	border-radius: 8px 8px 0 0;
	background:#282c2f;
	background-image: -webkit-linear-gradient(top, #3e4448 0%, #33383c 50%, #282c2f 100%);
  	background-image: -moz-linear-gradient(top, #3e4448 0%, #33383c 50%, #282c2f 100%);
  	background-image: -o-linear-gradient(top, #3e4448 0%, #33383c 50%, #282c2f 100%);
  	background-image: linear-gradient(to bottom, #3e4448 0%, #33383c 50%, #282c2f 100%);
	box-shadow:0px 0px 1px #575c5f inset;
	-webkit-box-shadow:0px 0px 1px #575c5f inset;
	-moz-box-shadow:0px 0px 1px #575c5f inset;
	-o-box-shadow:0px 0px 1px #575c5f inset;
	color:#b5b5b6;
	line-height:27px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-align:center;
	}
.Package_Common{
	padding:15px;
	}
.Package{
	width:60px; 
	height:28px;
	color:#fff;
	line-height:30px;
	padding:0 0 0 60px;
	}
.Package_start{ 
	background-image:url(../images/Package_start.png); 
	}
.Package_end{
	background-image:url(../images/Package_end.png); 
	}
.Package_arrow{ 
	background-image:url(../images/Package_arrow.png); 
	width:18px; 
	height:18px;
	margin:10px 0 0 62px;
	}
.Package_color{
	width:208px;
	height:55px;
	}
.Package_Green{
	background-image:url(../images/Package_Green.png);
	}
.Package_Yellow{
	background-image:url(../images/Package_Yellow.png);
	}
.Package_Gray{
	background-image:url(../images/Package_Gray.png);
	}
.Package_Red{
	background-image:url(../images/Package_Red.png);
	}
.Package_Purple{
	background-image:url(../images/Package_Purple.png);
	}
.Package_Blue{
	background-image:url(../images/Package_Blue.png);
	}
.Package_Font{
	width:152px;
	color:#fff;
	text-align:center;
	float:left;
	margin:11px 0 0 0;
	}
.Package_Font span{
	display:block; 
	height:22px; 
	line-height:22px;
	}
.Package_Page{ 
	text-align:center; 
	padding:15px 0 0 0;
	}
.Package_Page img{ 
	padding:0 5px; 
	}
.Package_BodyPage{
	clear:both;
	padding:10px 0;
	margin:0 auto;
	width:auto;
	text-align:center;
	}
.Package_BodyPage img{
	padding:0 30px;
	}
.Package_Remark{
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	-o-border-radius:4px;
	border:1px solid #15181b;
	padding:10px 0;
	margin:0 auto;
	width:520px;
	color:#b5b5b6;
	height:20px;
	background-color:#23272b;
	clear:both;
	}
.Remark_List{
	display:block;
	float:left;
	}
.Remark{
	width:13px;
	height:13px;
	border-radius:2px;
	-webkit-border-radius:2px;
	-moz-border-radius:2px;
	-o-border-radius:2px;
	display:block;
	float:left;
	margin:2px 15px 0 15px;
	}
.Remark_Green{
	background:#038f49;
	background-image: -webkit-linear-gradient(top, #07b85e 0%, #038f49 100%);
  	background-image: -moz-linear-gradient(top, #07b85e 0%, #038f49 100%);
  	background-image: -o-linear-gradient(top, #07b85e 0%, #038f49 100%);
  	background-image: linear-gradient(to bottom, #07b85e 0%, #038f49 100%);
	}
.Remark_Yellow{
	background:#ce7f0d;
	background-image: -webkit-linear-gradient(top, #f6a32c 0%, #ce7f0d 100%);
  	background-image: -moz-linear-gradient(top, #f6a32c 0%, #ce7f0d 100%);
  	background-image: -o-linear-gradient(top, #f6a32c 0%, #ce7f0d 100%);
  	background-image: linear-gradient(to bottom, #f6a32c 0%, #ce7f0d 100%);
	}
.Remark_Purple{
	background:#8f22cc;
	background-image: -webkit-linear-gradient(top, #b648f4 0%, #8f22cc 100%);
  	background-image: -moz-linear-gradient(top, #b648f4 0%, #8f22cc 100%);
  	background-image: -o-linear-gradient(top, #b648f4 0%, #8f22cc 100%);
  	background-image: linear-gradient(to bottom, #b648f4 0%, #8f22cc 100%);
	}
.Remark_Blue{
	background:#05adff;
	background-image: -webkit-linear-gradient(top, #67c9f8 0%, #05adff 100%);
  	background-image: -moz-linear-gradient(top, #67c9f8 0%, #05adff 100%);
  	background-image: -o-linear-gradient(top, #67c9f8 0%, #05adff 100%);
  	background-image: linear-gradient(to bottom, #67c9f8 0%, #05adff 100%);
	}
.Remark_Gray{
	background:#8d8d8d;
	background-image: -webkit-linear-gradient(top, #bebebf 0%, #8d8d8d 100%);
  	background-image: -moz-linear-gradient(top, #bebebf 0%, #8d8d8d 100%);
  	background-image: -o-linear-gradient(top, #bebebf 0%, #8d8d8d 100%);
  	background-image: linear-gradient(to bottom, #bebebf 0%, #8d8d8d 100%);
	}
.Remark_Red{
	background:#ce354e;
	background-image: -webkit-linear-gradient(top, #fa6f85 0%, #ce354e 100%);
  	background-image: -moz-linear-gradient(top, #fa6f85 0%, #ce354e 100%);
  	background-image: -o-linear-gradient(top, #fa6f85 0%, #ce354e 100%);
  	background-image: linear-gradient(to bottom, #fa6f85 0%, #ce354e 100%);
	}
.Package_bottom{float:left; color:#FFF; height:30px;}

/*Graph2*/
.DR_Leftmenu_over_btn{
	border-radius: 15px 0 0 15px;
	-webkit-border-radius: 15px 0 0 15px;
	-moz-border-radius: 15px 0 0 15px;
	-o-border-radius: 15px 0 0 15px;
	}
.DR_Leftmenu_btn{
	border-radius: 15px 0 0 15px;
	-webkit-border-radius: 15px 0 0 15px;
	-moz-border-radius: 15px 0 0 15px;
	-o-border-radius: 15px 0 0 15px;
	}
.Disaster_Recovery_Right{
	background-image:url(../images/Disaster_Recovery_BG.png);
	}
.Disaster_Recovery_arrow{
	float:left;
	padding:150px 20px 0 20px;
	}
.Disaster_Recovery_One{
	width:310px;
	margin:0 0 20px 0;
	}
.Disaster_Recovery_Common{
	border:1px solid #15181b;
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	background-color: rgba(35,39,43,0.3);
	float:left;
	padding:10px;
	}
.Disaster_Recovery_Line{
	background-image:url(../images/Disaster_Recovery_line.png);
	width:100%;
	height:auto;
	float:left;
	}
.Disaster_Recovery_start{
	background-image:url(../images/Disaster_Recovery_start.png);
	width:77px;
	height:23px;
	line-height:23px;
	color:#fff;
	text-align:center;
	margin:0 0 0 126px;
	float:left;
	}
.Disaster_Recovery_List{
	margin:20px 0;
	clear:both;
	float:left;
	}
.Disaster_Recovery_List2{
	margin:15px 0;
	float:right;
	}
.Disaster_Recovery_Green{
	background:#088145;
	background-image: -webkit-linear-gradient(top, #15cb70 0%, #079d52 50%, #088145 100%);
  	background-image: -moz-linear-gradient(top, #15cb70 0%, #079d52 50%, #088145 100%);
  	background-image: -o-linear-gradient(top, #15cb70 0%, #079d52 50%, #088145 100%);
  	background-image: linear-gradient(to bottom, #15cb70 0%, #079d52 50%, #088145 100%);
	}
.Disaster_Recovery_Red{
	background:#ce354e;
	background-image: -webkit-linear-gradient(top, #fa6f85 0%, #da4059 50%, #ce354e 100%);
  	background-image: -moz-linear-gradient(top, #fa6f85 0%, #da4059 50%, #ce354e 100%);
  	background-image: -o-linear-gradient(top, #fa6f85 0%, #da4059 50%, #ce354e 100%);
  	background-image: linear-gradient(to bottom, #fa6f85 0%, #da4059 50%, #ce354e 100%);
	}
.Disaster_Recovery_Yellow{
	background:#e89217;
	background-image: -webkit-linear-gradient(top, #fcbf67 0%, #fab24c 50%, #e89217 100%);
  	background-image: -moz-linear-gradient(top, #fcbf67 0%, #fab24c 50%, #e89217 100%);
  	background-image: -o-linear-gradient(top, #fcbf67 0%, #fab24c 50%, #e89217 100%);
  	background-image: linear-gradient(to bottom, #fcbf67 0%, #fab24c 50%, #e89217 100%);
	}
.Disaster_Recovery_Blue{
	background:#0292d8;
	background-image: -webkit-linear-gradient(top, #78d1fc 0%, #35aee8 50%, #0292d8 100%);
  	background-image: -moz-linear-gradient(top, #78d1fc 0%, #35aee8 50%, #0292d8 100%);
  	background-image: -o-linear-gradient(top, #78d1fc 0%, #35aee8 50%, #0292d8 100%);
  	background-image: linear-gradient(to bottom, #78d1fc 0%, #35aee8 50%, #0292d8 100%);
	}
.Disaster_Recovery_Purple{
	background:#9023cd;
	background-image: -webkit-linear-gradient(top, #c96dfd 0%, #ad49e5 50%, #9023cd 100%);
  	background-image: -moz-linear-gradient(top, #c96dfd 0%, #ad49e5 50%, #9023cd 100%);
  	background-image: -o-linear-gradient(top, #c96dfd 0%, #ad49e5 50%, #9023cd 100%);
  	background-image: linear-gradient(to bottom, #c96dfd 0%, #ad49e5 50%, #9023cd 100%);
	}
.Disaster_Recovery_Gray{
	background:#737272;
	background-image: -webkit-linear-gradient(top, #c9caca 0%, #9f9f9f 50%, #737272 100%);
  	background-image: -moz-linear-gradient(top, #c9caca 0%, #9f9f9f 50%, #737272 100%);
  	background-image: -o-linear-gradient(top, #c9caca 0%, #9f9f9f 50%, #737272 100%);
  	background-image: linear-gradient(to bottom, #c9caca 0%, #9f9f9f 50%, #737272 100%);
	}
.Disaster_Recovery_Green_hover{
	background:#088145;
	background-image: -webkit-linear-gradient(top, #088145 0%, #2ac477 50%, #4eeb9c 100%);
  	background-image: -moz-linear-gradient(top, #088145 0%, #2ac477 50%, #4eeb9c 100%);
  	background-image: -o-linear-gradient(top, #088145 0%, #2ac477 50%, #4eeb9c 100%);
  	background-image: linear-gradient(to bottom, #088145 0%, #2ac477 50%, #4eeb9cs 100%);
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	}
.Disaster_Recovery_Red_hover{
	background:#cd334c;
	background-image: -webkit-linear-gradient(top, #cd334c 0%, #da4059 50%, #e64c65 100%);
  	background-image: -moz-linear-gradient(top, #cd334c 0%, #da4059 50%, #e64c65 100%);
  	background-image: -o-linear-gradient(top, #cd334c 0%, #da4059 50%, #e64c65 100%);
  	background-image: linear-gradient(to bottom, #cd334c 0%, #da4059 50%, #e64c65 100%);
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	}
.Disaster_Recovery_Yellow_hover{
	background:#e89217;
	background-image: -webkit-linear-gradient(top, #e89217 0%, #fab24c 50%, #fcbf67 100%);
  	background-image: -moz-linear-gradient(top, #e89217 0%, #fab24c 50%, #fcbf67 100%);
  	background-image: -o-linear-gradient(top, #e89217 0%, #fab24c 50%, #fcbf67 100%);
  	background-image: linear-gradient(to bottom, #e89217 0%, #fab24c 50%, #fcbf67 100%);
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	}
.Disaster_Recovery_Blue_hover{
	background:#0292d8;
	background-image: -webkit-linear-gradient(top, #0292d8 0%, #35aee8 50%, #78d1fc 100%);
  	background-image: -moz-linear-gradient(top, #0292d8 0%, #35aee8 50%, #78d1fc 100%);
  	background-image: -o-linear-gradient(top, #0292d8 0%, #35aee8 50%, #78d1fc 100%);
  	background-image: linear-gradient(to bottom, #0292d8 0%, #35aee8 50%, #78d1fc 100%);
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	}
.Disaster_Recovery_Purple_hover{
	background:#9023cd;
	background-image: -webkit-linear-gradient(top, #9023cd 0%, #ad49e5 50%, #c96dfd 100%);
  	background-image: -moz-linear-gradient(top, #9023cd 0%, #ad49e5 50%, #c96dfd 100%);
  	background-image: -o-linear-gradient(top, #9023cd 0%, #ad49e5 50%, #c96dfd 100%);
  	background-image: linear-gradient(to bottom, #9023cd 0%, #ad49e5 50%, #c96dfd 100%);
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	}
.Disaster_Recovery_Gray_hover{
	background:#737272;
	background-image: -webkit-linear-gradient(top, #737272 0%, #9f9f9f 50%, #c9caca 100%);
  	background-image: -moz-linear-gradient(top, #737272 0%, #9f9f9f 50%, #c9caca 100%);
  	background-image: -o-linear-gradient(top, #737272 0%, #9f9f9f 50%, #c9caca 100%);
  	background-image: linear-gradient(to bottom, #737272 0%, #9f9f9f 50%, #c9caca 100%);
	box-shadow:2px 2px 10px #0e1012;
	-webkit-box-shadow:2px 2px 10px #0e1012;
	-moz-box-shadow:2px 2px 10px #0e1012;
	-o-box-shadow:2px 2px 10px #0e1012;
	}
.DR_Color_Common{
	width:170px;
	height:40px;
	float:left;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	}
.DR_Color_Common_L{
	border-radius: 20px 0 0 20px;
	-webkit-border-radius: 20px 0 0 20px;
	-moz-border-radius: 20px 0 0 20px;
	-o-border-radius: 20px 0 0 20px;
	}
.DR_Color_Common_R{
	border-radius: 0 20px 20px 0;
	-webkit-border-radius: 0 20px 20px 0;
	-moz-border-radius: 0 20px 20px 0;
	-o-border-radius: 0 20px 20px 0;
	}
.Disaster_Recovery_Info{ float:left}
.Disaster_Recovery_Info span{
	display:block;
	color:#fff;
	padding: 0 15px;
	line-height:20px;
	width:110px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	}
.Disaster_Recovery_monitor_L{ 
	background-image:url(../images/Disaster_Recovery_Icon.png); 
	width:16px; 
	height:16px;
	float:left;
	margin:12px 0 0 0;
	}
.Disaster_Recovery_monitor_R{
	background-image:url(../images/Disaster_Recovery_Icon.png); 
	width:16px; 
	height:16px;
	float:left;
	margin:12px 0 0 10px;
	}
.Disaster_Recovery_Num{
	background-image:url(../images/Disaster_Recovery_Num.png);
	width:16px;
	height:16px;
	float:left;
	margin:10px 15px 0 15px;
	color:#c9caca;
	text-align:center;
	}
.Disaster_Recovery_Two{
	width:295px;
	margin:0 0 20px 0;
	}
.DR_Two_Common{
	border-radius:15px;
	height:25px;
	line-height:25px;
	color:#fff;
	border:1px solid #15181b;
	width:140px;
	text-align:center;
	float:left;
	margin:20px 0 0 5px;
	}
.DR_Two_Green{
	background:#088145;
	background-image: -webkit-linear-gradient(left, #088145 0%, #15cb70 100%);
  	background-image: -moz-linear-gradient(left, #088145 0%, #15cb70 100%);
  	background-image: -o-linear-gradient(left, #088145 0%, #15cb70 100%);
  	background-image: linear-gradient(to right, #088145 0%, #15cb70 100%);
	}
.DR_Two_Red{
	background:#ce354e;
	background-image: -webkit-linear-gradient(left, #ce354e 0%, #fa6f85 100%);
  	background-image: -moz-linear-gradient(left, #ce354e 0%, #fa6f85 100%);
  	background-image: -o-linear-gradient(left, #ce354e 0%, #fa6f85 100%);
  	background-image: linear-gradient(to right, #ce354e 0%, #fa6f85 100%);
	}
.DR_Two_Yellow{
	background:#e89217;
	background-image: -webkit-linear-gradient(left, #e89217 0%, #fcbf67 100%);
  	background-image: -moz-linear-gradient(left, #e89217 0%, #fcbf67 100%);
  	background-image: -o-linear-gradient(left, #e89217 0%, #fcbf67 100%);
  	background-image: linear-gradient(to right, #e89217 0%, #fcbf67 100%);
	}
.DR_Two_blue{
	background:#0292d8;
	background-image: -webkit-linear-gradient(left, #0292d8 0%, #78d1fc 100%);
  	background-image: -moz-linear-gradient(left, #0292d80%, #78d1fc 100%);
  	background-image: -o-linear-gradient(left, #0292d8 0%, #78d1fc 100%);
  	background-image: linear-gradient(to right, #0292d8 0%, #78d1fc 100%);	
}
.DR_Two_Purple{
	background:#9023cd;
	background-image: -webkit-linear-gradient(left, #9023cd 0%, #c96dfd  100%);
  	background-image: -moz-linear-gradient(left, #9023cd0%, #c96dfd  100%);
  	background-image: -o-linear-gradient(left, #9023cd 0%, #c96dfd  100%);
  	background-image: linear-gradient(to right, #9023cd 0%, #c96dfd  100%);
	}
.DR_Two_Gray{
	background:#737272;
	background-image: -webkit-linear-gradient(left, #737272 0%, #c9caca 100%);
  	background-image: -moz-linear-gradient(left, #737272 0%, #c9caca 100%);
  	background-image: -o-linear-gradient(left, #737272 0%, #c9caca 100%);
  	background-image: linear-gradient(to right, #737272 0%, #c9caca 100%);
	}
.Disaster_Recovery_Desc{
	width:210px;
	padding:0 10px;
	border:1px solid #15181b;
	background-color:#3a3e42;
	color:#b5b5b6;
	position:absolute;
	height:auto;
	left:900px;
	top:220px;
	}
.Disaster_Recovery_Desc tr{ height:30px;}