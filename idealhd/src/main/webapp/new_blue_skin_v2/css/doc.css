@charset "utf-8";
/* CSS Document */
body{
	 margin-bottom:0; 
	 margin-left:0; 
	 margin-right:0; 
	 margin-top:0; 
	 font-family: arial, Microsoft Yahei, helvetica, verdana, sans-serif;
	 background-color:#fff;
	 font-size:14px;
	 }
.main_grid{ 
	width:1170px; 
	padding:15px;
	margin:auto; 
	 }
.col_md{
	float:left}
.col_md-1{
	width:25%;
	}
.col_md-2{
	width:75%;
	padding:0 0 15px 0;
	}
.nav_list{ padding:0;
	margin:0;
	list-style:none;
	width:100%;
	}
.nav_list li{ 
	padding:5px 15px;
	}
.nav_list li a{ 
	text-decoration:none; 
	color:#595757
	}
.nav_list li:hover{
	border-left:2px solid #f01024;
	}
.nav_list li a:hover{
	color:#f01024;
	font-weight:bold;
	}
.page_header{
	border-bottom:1px solid #dcdddd;
	padding:0 0 10px 0;
	color:#333333;
	}
.page_header_h2{
	font-size:20px;
	color:#4d4d4d;}
.page_content{
	line-height:2;
	color:#888888;}
.highlight{
	background-color:#f7f7f9;
	border:1px solid #dcdddd;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	width:100%;
	margin:0;
	padding:10px;
	}
.highlight pre{
	margin:0;
	padding:0;
	word-break:normal;
	white-space:pre-line
	}
code{
	font-family:Arial, Helvetica, sans-serif;
	color:#333;
	line-height:2;
	text-align:left}