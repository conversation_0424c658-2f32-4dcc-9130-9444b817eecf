@charset "utf-8";
/* CSS Document */
@font-face {
  font-family: 'portal';
  src:  url('../fonts/portal.eot?bd6cl');
  src:  url('../fonts/portal.eot?bd6cl#iefix') format('embedded-opentype'),
    url('../fonts/portal.ttf?bd6cl') format('truetype'),
    url('../fonts/portal.woff?bd6cl') format('woff'),
    url('../fonts/portal.svg?bd6cl#portal') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'portal' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-app_icon01:before {
  content: "\e900";
}
.icon-app_icon02:before {
  content: "\e901";
}
.icon-app_icon03:before {
  content: "\e902";
}
.icon-app_icon04:before {
  content: "\e903";
}
.icon-app_icon05:before {
  content: "\e904";
}
.icon-app_icon06:before {
  content: "\e905";
}
.icon-app_icon07:before {
  content: "\e906";
}
.icon-app_icon08:before {
  content: "\e907";
}

body{
	padding: 0px;
	margin: 0px;
	box-sizing: border-box;
	font-family: Microsoft Yahei;
	width: 100%;
	height: 100%;
}
html,body{
	height: 100%;
}
ul,li,p,h1,h2{
	padding: 0px;
	margin: 0px;
	list-style: none;
}
.app_overall{
	width: 1030px;
	margin: 0 auto;
	position: relative;
	top:50%;
	transform:translateY(-50%);
	box-sizing: border-box;
}
.app_title h1{
	font-size: 40px;
	text-align: center;
	color:#46545d;
	font-weight: normal;
	margin-bottom: 20px;
}
.app_panel{
	margin: 0 auto;
	max-height: calc(100vh - 150px);
	overflow-y: auto;
	display: flex;
	display: -webkit-flex;
	flex-wrap:wrap;
	flex: 25%;
	justify-content: center;
}
.app_panel div{
	display: inline-block;
	width: 252px;
	height: 252px;
	border-right: 1px solid #e4e5e6;
	border-bottom: 1px solid #e4e5e6;
	text-align: center;
}
.app_panel .app_normal:hover{
	background-color: #e5f2fe;
	cursor: pointer;
}
.app_panel div:hover .app_icon{
	color: #ffffff;
}
.app_panel .app_normal:hover h2{
	color:#007af5;
	font-weight:bold;
}
.app_panel div:nth-child(4n+1){
	border-left: 1px solid #e4e5e6;
}
.app_panel div:nth-child(1),.app_panel div:nth-child(2),.app_panel div:nth-child(3),.app_panel div:nth-child(4){
	border-top: 1px solid #e4e5e6;
}
.app_panel div span{
	display: block;
	margin: 58px auto 0 auto;
}
.app_panel div img{
	margin: 38px auto 0 auto;

}
.app_panel div h2{
	font-weight: normal;
	color: #cccccc;
	font-size: 20px;
	margin-top: 45px;
}
.app_icon{
	font-size: 68px;
	color: #007af5;
}
.app_disabled{
	opacity: 0.7;
}
.app_disabled .app_icon{
	color: #dddddd
}
.app_panel .app_disabled:hover{
	background-color:#ffffff;
	cursor: pointer;
}
.app_panel .app_disabled:hover .app_icon{
	color: #dddddd
}
.app_panel .app_normal h2{
	color:#6a707d;
}
.app_disabled img{
	opacity: 0.3;
}