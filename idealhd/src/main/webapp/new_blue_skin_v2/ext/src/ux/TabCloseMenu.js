/**
 * Plugin for adding a close context menu to tabs. Note that the menu respects
 * the closable configuration on the tab. As such, commands like remove others
 * and remove all will not remove items that are not closable.
 */
// plugins : Ext.create('Ext.ux.TabCloseMenu', {
// closeTabText : '关闭本页',
// closeOthersTabsText : '关闭其他',
// closeAllTabsText : '关闭全部',
// closeLeftAllTabsText : '关闭左侧全部',
// closeRightAllTabsText : '关闭右侧全部'
// }),
Ext
		.define(
				'Ext.ux.TabCloseMenu',
				{
					alias : 'plugin.tabclosemenu',

					mixins : {
						observable : 'Ext.util.Observable'
					},

					/**
					 * @cfg {String} closeTabText The text for closing the
					 *      current tab.
					 */
					closeTabText : 'Close Tab',

					/**
					 * @cfg {<PERSON>olean} showCloseOthers Indicates whether to show
					 *      the 'Close Others' option.
					 */
					showCloseOthers : true,

					/**
					 * @cfg {String} closeOthersTabsText The text for closing
					 *      all tabs except the current one.
					 */
					closeOthersTabsText : 'Close Other Tabs',

					/**
					 * @cfg {Boolean} showCloseAll Indicates whether to show the
					 *      'Close All' option.
					 */
					showCloseAll : true,

					/**
					 * @cfg {String} closeAllTabsText The text for closing all
					 *      tabs.
					 */
					closeAllTabsText : 'Close All Tabs',

					showCloseLeftAll : true,

					/**
					 * @cfg {String} closeOthersTabsText The text for closing
					 *      all tabs except the current one.
					 */
					closeLeftAllTabsText : 'Close Left All Tabs',

					showCloseRightAll : true,

					/**
					 * @cfg {String} closeOthersTabsText The text for closing
					 *      all tabs except the current one.
					 */
					closeRightAllTabsText : 'Close Right All Tabs',
					/**
					 * @cfg {Array} extraItemsHead An array of additional
					 *      context menu items to add to the front of the
					 *      context menu.
					 */
					extraItemsHead : null,

					/**
					 * @cfg {Array} extraItemsTail An array of additional
					 *      context menu items to add to the end of the context
					 *      menu.
					 */
					extraItemsTail : null,
					itemsLeftMenus : [],
					itemsMenus : [],

					// public
					constructor : function(config) {
						this.addEvents('aftermenu', 'beforemenu');

						this.mixins.observable.constructor.call(this, config);
					},

					init : function(tabpanel) {
						this.tabPanel = tabpanel;
						this.tabBar = tabpanel.down("tabbar");

						this.mon(this.tabPanel, {
							scope : this,
							afterlayout : this.onAfterLayout,
							single : true
						});
					},

					onAfterLayout : function() {
						this.mon(this.tabBar.el, {
							scope : this,
							contextmenu : this.onContextMenu,
							delegate : '.x-tab'
						});
					},

					onBeforeDestroy : function() {
						Ext.destroy(this.menu);
						this.callParent(arguments);
					},

					// private
					onContextMenu : function(event, target) {
						var me = this, menu = me.createMenu(), disableAll = true, disableOthers = true, disableLeftAll = true, disableRightAll = true, tab = me.tabBar
								.getChildByElement(target), index = me.tabBar.items
								.indexOf(tab), counts = me.tabBar.items.length;
						itemsMenus = [];
						itemsLeftMenus = [];
						if (index > 0) {
							for (var k = 0; k < counts; k++) {
								itemsMenus.push(me.tabPanel.getComponent(k));
							}
							for (var k = 0; k < index; k++) {
								itemsLeftMenus
										.push(me.tabPanel.getComponent(k));
							}
						}

						me.item = me.tabPanel.getComponent(index);
						menu.child('#close').setDisabled(!me.item.closable);
						if (me.showCloseAll || me.showCloseOthers
								|| me.showCloseLeftAll || me.showCloseRightAll) {
							me.tabPanel.items
									.each(function(item) {
										if (item.closable) {
											disableAll = false;
											if (item != me.item) {
												disableOthers = false;
												if (itemsLeftMenus.length > 1) {
													disableLeftAll = false;
												} else {
													disableLeftAll = true;
												}
												if (counts > (itemsLeftMenus.length + 1)) {
													disableRightAll = false;
												} else {
													disableRightAll = true;
												}
												return false;
											}
										}
										return true;
									});
							if (me.showCloseAll) {
								menu.child('#closeAll').setDisabled(disableAll);
							}

							if (me.showCloseOthers) {
								menu.child('#closeOthers').setDisabled(
										disableOthers);
							}
							if (me.showCloseLeftAll) {
								menu.child('#closeLeftAll').setDisabled(
										disableLeftAll);
							}
							if (me.showCloseRightAll) {
								menu.child('#closeRightAll').setDisabled(
										disableRightAll);
							}

						}

						event.preventDefault();
						me.fireEvent('beforemenu', menu, me.item, me);

						menu.showAt(event.getXY());
					},

					createMenu : function() {
						var me = this;

						if (!me.menu) {
							var items = [ {
								itemId : 'close',
								text : me.closeTabText,
								scope : me,
								handler : me.onClose
							} ];
							// if (me.showCloseAll || me.showCloseOthers) {
							// items.push('-');
							// }
							items.push('-');
							if (me.showCloseOthers) {
								items.push({
									itemId : 'closeOthers',
									text : me.closeOthersTabsText,
									scope : me,
									handler : me.onCloseOthers
								});
							}
							items.push('-');
							// if (me.showCloseAll || me.showCloseOthers) {
							// items.push('-');
							// }
							if (me.showCloseAll) {
								items.push({
									itemId : 'closeAll',
									text : me.closeAllTabsText,
									scope : me,
									handler : me.onCloseAll
								});
							}
							items.push('-');
							if (me.showCloseLeftAll) {
								items.push({
									itemId : 'closeLeftAll',
									text : me.closeLeftAllTabsText,
									scope : me,
									handler : me.closeLeftAllTabs
								});
							}
							items.push('-');
							if (me.showCloseRightAll) {
								items.push({
									itemId : 'closeRightAll',
									text : me.closeRightAllTabsText,
									scope : me,
									handler : me.closeRightAllTabs
								});
							}

							if (me.extraItemsHead) {
								items = me.extraItemsHead.concat(items);
							}

							if (me.extraItemsTail) {
								items = items.concat(me.extraItemsTail);
							}

							me.menu = Ext.create('Ext.menu.Menu', {
								items : items,
								listeners : {
									hide : me.onHideMenu,
									scope : me
								}
							});
						}

						return me.menu;
					},

					onHideMenu : function() {
						var me = this;
						me.fireEvent('aftermenu', me.menu, me);
					},

					onClose : function() {
						this.tabPanel.remove(this.item);
					},

					onCloseOthers : function() {
						this.doClose(3);
					},

					onCloseAll : function() {
						this.doClose(4);
					},
					closeLeftAllTabs : function() {
						this.doClose(1);
					},
					closeRightAllTabs : function() {
						this.doClose(2);
					},

					doClose : function(excludeActive) {
						var items = [];
						if (excludeActive == 1) {
							this.tabPanel.items.each(function(item) {
								if (item.closable) {
									if (!excludeActive || item != this.item) {
										if (itemsLeftMenus.indexOf(item)>-1) {
											items.push(item);
										}
									}
								}
							}, this);
						} else if (excludeActive == 2) {
							this.tabPanel.items.each(function(item) {
								if (item.closable) {
									if (!excludeActive || item != this.item) {
										if (itemsLeftMenus.indexOf(item)<=-1) {
											items.push(item);
										}

									}
								}
							}, this);
						} else {
							this.tabPanel.items.each(
									function(item) {
										if (item.closable) {
											if (excludeActive == 4
													|| item != this.item) {
												items.push(item);
											}
										}
									}, this);
						}
						Ext.suspendLayouts();
						Ext.Array.forEach(items, function(item) {
							this.tabPanel.remove(item);
						}, this);
						Ext.resumeLayouts(true);
					}
				});
