body{ 
	margin-bottom:0; 
	margin-top:0; 
	margin-left:0; 
	margin-right:0; 
	background-color:#07071b;
	overflow:hidden;
	}
html,body{ height:100%;}
.body_bg{ 
	background-image:url(../images/body_bg.jpg); 
	background-position: center;
	height:100%; 
	width:100%;
	}
.hd_body_bg{ 
	width:1000px; 
	height:600px;
	margin:0 auto;
}
.hd_Login_Cn{ 
	margin:0 auto; 
	padding:170px 0 0 0;
	}
.hd_Logo{ 
	background-image:url(../images/hd_Logo.png); 
	width:650px; 
	height:60px; 
	float:left;
	}
.login_title{
	font-size:18px; 
	font-family:"微软雅黑";
	color:#c9caca;
	display:block;
	padding:20px;
}
.hd_Logozs{ 
	background-image:url(../images/hd_Logozs.png); 
	width:269px; 
	height:65px; 
	margin:auto
	}
.hd_Cn_bg{
	padding:170px 0 0 645px;
}
.hd_Content{ 
	background-image:url(../images/hd_Content.png); 
	width:352px; 
	height:271px;
	overflow:hidden;
	}
.hd_C_tab{width:335px;
	height:188px;
	margin:20px 0 0 440px;
	}
.User_Bg{
	background-image:url(../images/User_Bg.png); 
	width:273px;
	height:32px;
	background-position: 0px 0px;
	}
.Password_Bg{
	background-image:url(../images/User_Bg.png); 
	width:273px;
	height:32px;
	background-position: 0px -32px;
	margin:20px 0 0 0;
}
/*
.hd_C_tab table tr{ height:78px;}
.hd_C_tab table tr td{ padding:0 0 0 10px;}
.hd_C_tab table tr td img{ padding:0 5px 0 0;}*/
.hd_text{ 
	width:250px; 
	height:32px; 
	border:0; 
	border-radius:2px; 
	color:#fff; 
	padding:0 0 0 5px;
	line-height:28px;
	background-color: rgba(159,160,160,0);
	margin:0 0 0 30px;
}
.hd_buttonL{ 
	background-image:url(../images/hd_bt1.png); 
	width:160px;
	text-align:center;
	height:27px;
	border:0px solid #a8b3b9; 
	color:#fff; 
	font-family:"微软雅黑";
	cursor:pointer;
	font-size:12px;
	margin:25px 0 0 0;
	background-color: rgba(34,24,21,0);
	}
.hd_buttonRes{
	background-image:url(../images/hd_bt2.png);
	width:89px;
	text-align:center;
	height:27px;
	border:0px solid #a8b3b9; 
	color:#fff; 
	font-family:"微软雅黑";
	cursor:pointer;
	font-size:12px;
	margin:25px 0 0 22px;
	background-color: rgba(34,24,21,0);
	}
.login_User{ background-image:url(../images/login_user.png); width:20px; height:20px; float:left; margin:5px 5px 0 0}
.login_lock{background-image:url(../images/login_lock.png); width:20px; height:20px; float:left; margin:5px 5px 0 0}

.login_error{ font-size:12px; color:#dcdddd;position:relative;bottom:40px;left:-75px;}





.Change_body_bg{
	background-image:url(../images/Change_body_bg.jpg); 
	background-position: center;
	height:100%; 
	width:100%;
}
.uppassword{text-align:right; padding:8px 30px 0 0;}
.uppassword a{text-decoration:none;color:#0068b7; font-size:12px;font-family:"微软雅黑"}
.uppassword a:hover{ color:#231815;text-decoration:underline;}
.Uppassword_Bg{width:370px;height:370px;}
.Uppassword_Bg .Uppassword_Cn{padding: 60px 0 0 0;}
.Uppassword_Bg ul{padding:0;margin:0;font-family:"微软雅黑";list-style:none}
.Uppassword_Bg ul li{padding:0; margin:18px 0;width:291px;height:25px;}
.Uppassword_Bg ul li input{
	background-color: rgba(255,255,255,0.2);
	border:1px solid #9fa0a0;
	width:210px;
	line-height:25px; 
	color:#fff;
	height:25px;
	margin:0 0 0 10px;
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	padding:0 0 0 10px;
	}
.Upli_Span{float:left;line-height:34px;width:69px;font-size:12px;color:#dcdddd}
.Upli_Span2{float:left}
.change_Cn{padding:10px 0 0 40px;}
.Change_button01{
	background:#284459;
	background-image: -webkit-linear-gradient(top, #5ebcc5 0%, #284459 100%);
  	background-image: -moz-linear-gradient(top, #5ebcc5 0%, #284459 100%);
  	background-image: -o-linear-gradient(top, #5ebcc5 0%, #284459 100%);
  	background-image: linear-gradient(to bottom, #5ebcc5 0%, #284459 100%);
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	border:1px solid #15181b;
	box-shadow:0px 0px 1px #7dd9e1 inset;
	-webkit-box-shadow:0px 0px 1px #7dd9e1 inset;
	-moz-box-shadow:0px 0px 1px #7dd9e1 inset;
	-o-box-shadow:0px 0px 1px #7dd9e1 inset;
	width:127px;
	height:28px;
	line-height:28px; 
	text-align:center; 
	float:left;
	font-family:"微软雅黑";
	font-size:12px;
	color:#fff;
	cursor:pointer;
	}
.Change_button02{
	background:#8f2514;
	background-image: -webkit-linear-gradient(top, #f39105 0%, #8f2514 100%);
  	background-image: -moz-linear-gradient(top, #f39105 0%, #8f2514 100%);
  	background-image: -o-linear-gradient(top, #f39105 0%, #8f2514 100%);
  	background-image: linear-gradient(to bottom, #f39105 0%, #8f2514 100%);
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	border:1px solid #15181b;
	box-shadow:0px 0px 1px #f9ad3e inset;
	-webkit-box-shadow:0px 0px 1px #f9ad3e inset;
	-moz-box-shadow:0px 0px 1px #f9ad3e inset;
	-o-box-shadow:0px 0px 1px #f9ad3e inset;
	width:129px;
	height:28px;
	line-height:28px; 
	text-align:center; 
	float:left;
	font-family:"微软雅黑";
	font-size:12px;
	color:#fff;
	cursor:pointer;
	margin:0 0 0 35px;}
.change_Result{color:#dcdddd;font-family:"微软雅黑";font-size:14px;}
.change_error{ font-size:12px; position:absolute; color:#595757; margin:5px 0 0 -140px;clear:both}
.Change_Password{padding:20px 0 0 0;}
.Change_Password a{font-size:12px; color:#dcdddd;text-decoration:underline;}

/*图片变换*/
#full-screen-slider { width:100%; height:100%; position:relative; clear:both}
#slides { display:block; width:100%; height:100%; list-style:none; padding:0; margin:0; position:relative}
#slides li { display:block; width:100%; height:100%; list-style:none; padding:0; margin:0; position:absolute}
#slides li a { display:block; width:100%; height:100%; text-indent:-9999px}
#pagination { display:block; list-style:none; position:absolute; left:45%; bottom:60px; z-index:9900;  padding:0 15px 0 0; margin:0}
#pagination li { display:block; list-style:none; width:30px; height:8px; float:left; margin-left:15px; background:#c9caca;}
#pagination li a { display:block; width:100%; height:100%; padding:0; margin:0;  text-indent:-9999px;}
#pagination li.current { background:#f6a600}
.body_bg1{background:url(../images/body_bg01.jpg) no-repeat center center;}
.body_bg2{background:url(../images/body_bg02.jpg) no-repeat center center;}
.body_bg3{background:url(../images/body_bg03.jpg) no-repeat center center;}
.login_tab{position:absolute; color:#FFF;z-index:9800}
.login_bg{width:1000px; height:440px;margin:0 auto; color:#FFF;}