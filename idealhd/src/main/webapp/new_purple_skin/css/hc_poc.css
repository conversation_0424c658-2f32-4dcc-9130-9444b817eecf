@charset "utf-8";
/* CSS Document */
body,html{
	height:100%;
	margin:0;
	padding:0;
	}
.inspection_body{
	padding:0;
	margin:0;
	background-color:#eaeff3;
	font-family:<PERSON><PERSON>,Microsoft Yahei;
	font-size:13px;
	color:#4d4d4d;
	}
.inspection_top{
	background-color:#4699f7;
	background-image: -webkit-linear-gradient(left, #3ab7cb 0%, #4699f7 100%);
  	background-image: -moz-linear-gradient(left, #3ab7cb 0%, #4699f7 100%);
  	background-image: -o-linear-gradient(left, #3ab7cb 0%, #4699f7 100%);
  	background-image: linear-gradient(to right, #3ab7cb 0%, #4699f7 100%);
	width:100%;
	height:220px;
	color:#fff;
	position:relative;
	z-index:0;
	}
.top_bg{
	background-image:url(../images/hc_poc/insp_top_bg.png);
	width:1120px;
	height:119px;
	margin:auto;
	}
.insp_top_left{
	float:left;
	margin:40px 0 0 0;
	}
.insp_logo{
	background-image:url(../images/hc_poc/insp_logo.png);
	width:137px;
	height:36px;
	display:block;
	float:left;
	}
.insp_text{
	font-size:24px;
	height:36px;
	line-height:36px;
	margin:0 0 0 20px;
	}
.insp_top_right{
	float:right;
	height:36px;
	line-height:36px;
	margin:40px 0 0 0;
	}
.insp_date{
	float:left;
	}
.insp_user{
	background-image:url(../images/hc_poc/insp_tp_icon.png);
	width:16px;
	height:16px;
	display:block;
	float:left;
	cursor:pointer;
	}
.user_pos1{
	background-position:0 0;
	margin:10px 10px 0 10px;
	}
.user_pos2{
	background-position:0 -16px;
	margin:10px 0 0 10px;
	}
.insp_name{
	line-height:36px;
	float:left;
	}
.inspection_position{
	width:1120px;
	margin:0 auto;
	}
.inspection_center{
	background-color:#fff;
	border-radius:4px;
	width:1120px;
	position:absolute;
	z-index:1;
	margin:-100px 0 0 0;
	padding:20px 0;
	}
.insp_search{
	height:30px;
	}
.search_bar{
	float:left;
	display:block;
	position:relative;
	z-index:0;
	}
.search_bar input{
	background-color:#fff;
	height:26px;
	border:1px solid #e6e6e6;
	border-radius:2px;
	width:449px;
	color:#b5b5b6;
	}
.search_bar_img{
	background-image:url(../images/hc_poc/search_bar_img.png);
	width:28px;
	height:28px;
	float:right;
	position:absolute;
	z-index:1;
	margin:0 0 0 -28px;
	cursor:pointer;
	}
.img_button{
	float:right;
	}
.button_pos{
	background-image:url(../images/hc_poc/page_btn.png);
	width:24px;
	height:24px;
	display:block;
	float:left;
	margin:3px 10px 0 0;
	cursor:pointer;
	}
.refresh_btn{
	background-position:0 0;
	}
.previous_screen{
	background-position:0 -24px;
	}
.previous_btn{
	background-position:0 -48px;
	}
.next_btn{
	background-position:0 -72px;
	}
.next_screen{
	background-position:0 -96px;
	margin:3px 0 0 0;
	}
.system_module{
	padding:20px 0;
	float:left;
	}
.system_module01{
	width:134px;
	height:90px;
	border:1px solid #e6e6e6;
	border-radius:4px;
	float:left;
	background-color:#fff;
	cursor:pointer;
	margin:0 0 0 22.8px;
	}
.selected_effect{
	box-shadow: 0 0 10px 2px #666666;
	-webkit-box-shadow: 0 0 10px 2px #666666;
	-moz-box-shadow: 0 0 10px 2px #666666;
	}	
.normal_status{
	background-color:#03db48;
	background-image: -webkit-linear-gradient(top, #0cbf47 0%, #03db48 100%);
  	background-image: -moz-linear-gradient(top, #0cbf47 0%, #03db48 100%);
  	background-image: -o-linear-gradient(top, #0cbf47 0%, #03db48 100%);
  	background-image: linear-gradient(to bottom, #0cbf47 0%, #03db48 100%);
	border:1px solid #00d696;
	}
.normal_status .sys_icon{
	background-image:url(../images/hc_poc/select_icon.png);
	}
.normal_status .sys_text{
	color:#fff;
	}
	
.one_alarm{
	background-color:#3cf9b9;
	background-image: -webkit-linear-gradient(top, #00d696 0%, #3cf9b9 100%);
  	background-image: -moz-linear-gradient(top, #00d696 0%, #3cf9b9 100%);
  	background-image: -o-linear-gradient(top, #00d696 0%, #3cf9b9 100%);
  	background-image: linear-gradient(to bottom, #00d696 0%, #3cf9b9 100%);
	border:1px solid #00d696;
	}
.one_alarm .sys_icon{
	background-image:url(../images/hc_poc/select_icon.png);
	}
.one_alarm .sys_text{
	color:#fff;
	}
	
.two_alarm{
	background-color:#20bcf5;
	background-image: -webkit-linear-gradient(top, #0c84f5 0%, #20bcf5 100%);
  	background-image: -moz-linear-gradient(top, #0c84f5 0%, #20bcf5 100%);
  	background-image: -o-linear-gradient(top, #0c84f5 0%, #20bcf5 100%);
  	background-image: linear-gradient(to bottom, #0c84f5 0%, #20bcf5 100%);
	border:1px solid #0c84f5;
	}
.two_alarm .sys_icon{
	background-image:url(../images/hc_poc/select_icon.png);
	}
.two_alarm .sys_text{
	color:#fff;
	}
	
.three_alarm{
	background-color:#fec827;
	background-image: -webkit-linear-gradient(top, #ffa602 0%, #fec827 100%);
  	background-image: -moz-linear-gradient(top, #ffa602 0%, #fec827 100%);
  	background-image: -o-linear-gradient(top, #ffa602 0%, #fec827 100%);
  	background-image: linear-gradient(to bottom, #ffa602 0%, #fec827 100%);
	border:1px solid #ffa602;
	}
.three_alarm .sys_icon{
	background-image:url(../images/hc_poc/select_icon.png);
	}
.three_alarm .sys_text{
	color:#fff;
	}
	
.four_alarm{
	background-color:#fc892a;
	background-image: -webkit-linear-gradient(top, #ff7924 0%, #fc892a 100%);
  	background-image: -moz-linear-gradient(top, #ff7924 0%, #fc892a 100%);
  	background-image: -o-linear-gradient(top, #ff7924 0%, #fc892a 100%);
  	background-image: linear-gradient(to bottom, #ff7924 0%, #fc892a 100%);
	border:1px solid #ff7924;
	}
.four_alarm .sys_icon{
	background-image:url(../images/hc_poc/select_icon.png);
	}
.four_alarm .sys_text{
	color:#fff;
	}
	
.five_alarm{
	background-color:#fe766c;
	background-image: -webkit-linear-gradient(top, #fb594e 0%, #fe766c 100%);
  	background-image: -moz-linear-gradient(top, #fb594e 0%, #fe766c 100%);
  	background-image: -o-linear-gradient(top, #fb594e 0%, #fe766c 100%);
  	background-image: linear-gradient(to bottom, #fb594e 0%, #fe766c 100%);
	border:1px solid #fb594e;
	}
.five_alarm .sys_icon{
	background-image:url(../images/hc_poc/select_icon.png);
	}
.five_alarm .sys_text{
	color:#fff;
	}

.sys_icon{
	background-image:url(../images/hc_poc/system_icon.png);
	width:44px;
	height:44px;
	display:block;
	margin:10px auto;
	}
.sys_position0{
	background-position:0 0;
	}
.sys_position1{
	background-position:0 -44px;
	}
.sys_position2{
	background-position:0 -88px;
	}
.sys_position3{
	background-position:0 -132px;
	}
.sys_position4{
	background-position:0 -176px;
	}
.sys_position5{
	background-position:0 -220px;
	}
.sys_position6{
	background-position:0 -264px;
	}
.sys_space{
	margin:0;
	}
.sys_text{
	display:block;
	text-align:center;
	width:134px;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
	}
.system_details{
	border:1px solid #eaeaec;
	border-radius:4px;
	background-color:#fff;
	width:100%;
	float:left;
	margin:20px 0 0 0;
	}
.details_title{
	background-color:#efefef;
	background-image: -webkit-linear-gradient(top, #ffffff 0%, #efefef 100%);
  	background-image: -moz-linear-gradient(top, #ffffff 0%, #efefef 100%);
  	background-image: -o-linear-gradient(top, #ffffff 0%, #efefef 100%);
  	background-image: linear-gradient(to bottom, #ffffff 0%, #efefef 100%);
	border-top-left-radius:4px;
	border-top-right-radius:4px;
	border-bottom-right-radius:0px;
	border-bottom-left-radius:0px;
	height:34px;
	line-height:34px;
	}
.details_title span{
	margin:0 0 0 15px;
	}
.step_content{
	padding:22px 17px 12px 17px;
	}
.step_line{
	background-image:url(../images/hc_poc/step_line.png);
	background-repeat:repeat-x;
	height:66px;
	width:99%;
	}
.step_line table{
	width:100%;
	float:left;
	}
.circle_attribute{
	width:44px;
	height:44px;
	border-radius:100%;
	background-color:#fff;
	cursor:pointer;
	}
.normal_img{
	background-image:url(../images/hc_poc/step_in.png);
	width:44px;
	height:44px;
	display:block;
	}
.img_position0{
	background-position:0 0;
	margin:-1px 0 0 -1px;
	}
.img_position1{
	background-position:0 -44px;
	margin:-1px 0 0 -1px;
	}
.img_position2{
	background-position:0 -88px;
	margin:-1px 0 0 -1px;
	}
.img_position3{
	background-position:0 -132px;
	margin:-1px 0 0 -1px;
	}
.img_position4{
	background-position:0 -176px;
	margin:-1px 0 0 -1px;
	}
.img_position5{
	background-position:0 -220px;
	margin:-1px 0 0 -1px;
	}
.circle_text{
	width:44px;
	text-align:center;
	margin:10px 0;
	color:#4d4d4d;
	}
/*正常*/	
.circle_status{
	border:1px solid #1ddf5d;
	-webkit-box-shadow:0 0 8px 1px #1ddf5d;
  	-moz-box-shadow:0 0 8px 1px #1ddf5d; 
	-ms-box-shadow:0 0 8px 1px #1ddf5d;  
 	box-shadow:0 0 8px 1px #1ddf5d;
	}
.circle_status:hover{
	background-color:#e6fbed;
	}
.selected .circle_status{
	background-color:#06db4c;
	border:2px solid #e6fbed;
	width:44px;
	height:44px;
	}
.selected .img_position0{
	background-position:0 -264px;
	margin:-2px 0 0 -2px;
	}
/*一级*/
.circle_status1{
	border:1px solid #b9ffea;
	-webkit-box-shadow:0 0 8px 1px #00d696;
  	-moz-box-shadow:0 0 8px 1px #00d696; 
	-ms-box-shadow:0 0 8px 1px #00d696;  
 	box-shadow:0 0 8px 1px #00d696;
	
	-webkit-animation-name: darkgreenPulse;
	-moz-animation-name: darkgreenPulse;
	animation-name: darkgreenPulse;
	-webkit-animation-duration: 1.5s;
	-moz-animation-duration: 1.5s;
	animation-duration: 1.5s;
	-webkit-animation-iteration-count: infinite;
	-moz-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	}
.circle_status1:hover{
	background-color:#b9ffea;
	}
.selected .circle_status1{
	background-color:#00d696;
	border:2px solid #b9ffea;
	width:44px;
	height:44px;
	}
.selected .img_position1{
	background-position:0 -308px;
	margin:-2px 0 0 -2px;
	}
/*二级*/
.circle_status2{
	border:1px solid #20bcf5;
	-webkit-box-shadow:0 0 8px 1px #20bcf5;
  	-moz-box-shadow:0 0 8px 1px #20bcf5; 
	-ms-box-shadow:0 0 8px 1px #20bcf5;  
 	box-shadow:0 0 8px 1px #20bcf5;
	
	-webkit-animation-name: bluePulse;
	-moz-animation-name: bluePulse;
	animation-name: bluePulse;
	-webkit-animation-duration: 1.5s;
	-moz-animation-duration: 1.5s;
	animation-duration: 1.5s;
	-webkit-animation-iteration-count: infinite;
	-moz-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	}
.circle_status2:hover{
	background-color:#cbefff;
	}
.selected .circle_status2{
	background-color:#20bcf5;
	border:2px solid #cbefff;
	width:44px;
	height:44px;
	}
.selected .img_position2{
	background-position:0 -352px;
	margin:-2px 0 0 -2px;
	}
/*三级*/
.circle_status3{
	border:1px solid #ffa602;
	-webkit-box-shadow:0 0 8px 1px #ffa602;
  	-moz-box-shadow:0 0 8px 1px #ffa602; 
	-ms-box-shadow:0 0 8px 1px #ffa602;  
 	box-shadow:0 0 8px 1px #ffa602;
	
	-webkit-animation-name: yellowPulse;
	-moz-animation-name: yellowPulse;
	animation-name: yellowPulse;
	-webkit-animation-duration: 1.5s;
	-moz-animation-duration: 1.5s;
	animation-duration: 1.5s;
	-webkit-animation-iteration-count: infinite;
	-moz-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	}
.circle_status3:hover{
	background-color:#ffeac4;
	}
.selected .circle_status3{
	background-color:#ffa602;
	border:2px solid #ffeac4;
	width:44px;
	height:44px;
	}
.selected .img_position3{
	background-position:0 -396px;
	margin:-2px 0 0 -2px;
	}
/*四级*/
.circle_status4{
	border:1px solid #ff7924;
	-webkit-box-shadow:0 0 8px 1px #ff7924;
  	-moz-box-shadow:0 0 8px 1px #ff7924; 
	-ms-box-shadow:0 0 8px 1px #ff7924;  
 	box-shadow:0 0 8px 1px #ff7924;
	
	-webkit-animation-name: orangePulse;
	-moz-animation-name: orangePulse;
	animation-name: orangePulse;
	-webkit-animation-duration: 1.5s;
	-moz-animation-duration: 1.5s;
	animation-duration: 1.5s;
	-webkit-animation-iteration-count: infinite;
	-moz-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	}
.circle_status4:hover{
	background-color:#ffcaa8;
	}
.selected .circle_status4{
	background-color:#ff7924;
	border:2px solid #ffcaa8;
	width:44px;
	height:44px;
	}
.selected .img_position4{
	background-position:0 -440px;
	margin:-2px 0 0 -2px;
	}

/*五级*/
.circle_status5{
	border:1px solid #fe756b;
	-webkit-box-shadow:0 0 8px 1px #fe756b;
  	-moz-box-shadow:0 0 8px 1px #fe756b; 
	-ms-box-shadow:0 0 8px 1px #fe756b;  
 	box-shadow:0 0 8px 1px #fe756b;
	
	-webkit-animation-name: redPulse;
	-moz-animation-name: redPulse;
	animation-name: redPulse;
	-webkit-animation-duration: 1.5s;
	-moz-animation-duration: 1.5s;
	animation-duration: 1.5s;
	-webkit-animation-iteration-count: infinite;
	-moz-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	}
.circle_status5:hover{
	background-color:#ffdfdd;
	}
.selected .circle_status5{
	background-color:#fe756b;
	border:2px solid #ffdfdd;
	width:44px;
	height:44px;
	}
.selected .img_position5{
	background-position:0 -484px;
	margin:-2px 0 0 -2px;
	}
.step_circle{
	width:10px;
	height:10px;
	border-radius:100%;
	background-color:#fff;
	border:2px solid #dcdddd;
	display:block;
	position:absolute;
	margin:17px 0 0 0;
	}
.circle_l{
	float:left;
	}
.note_pos{
	float:right;
	margin:0 0 0 2px;
}
.exclam_tab{
	width:100%;
	height:100%;
	font-size:13px;
	}
.exclam_div{
	width:150px;
	height:39px;
	line-height:39px;
	}
.exclamation_mark{
	background-image:url(../images/hc_poc/exclamation_mark.png);
	width:39px;
	height:39px;
	margin:0 10px;
	float:left;
	}
.top_panel .x-toolbar-default {
	border-color: #ffffff;
	border-width: 0px;
	background-image: none;
	background-color: white
}
.top_panel 	.x-panel-body-default {
	border-color: #ffffff;
	color: #4d4d4d;
	font-size: 13px;
	font-size: normal;
	border-width: 0px;
	border-style: solid
}
.top_panel .x-btn-default-toolbar-small-disabled {
	background-image: none;
	background-color: #3bc3ff;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #3bc3ff),
		color-stop(50%, #3bc3ff), color-stop(51%, #3bc3ff),
		color-stop(100%, #3bc3ff));
	background-image: -webkit-linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff);
	background-image: -moz-linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff);
	background-image: -o-linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff);
	background-image: linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff)
}
.top_panel .x-btn-default-toolbar-small{
	border-color: #3bc3ff;
	background-color: #3bc3ff;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #3bc3ff),
		color-stop(50%, #3bc3ff), color-stop(51%, #3bc3ff),
		color-stop(100%, #3bc3ff));
	background-image: -webkit-linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff);
	background-image: -moz-linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff);
	background-image: -o-linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff);
	background-image: linear-gradient(top, #3bc3ff, #3bc3ff 50%, #3bc3ff 51%, #3bc3ff)
}
.top_panel .x-btn-default-toolbar-small-over {
	background-image: none;
	border-color: #14b1f6;
	background-color: #14b1f6;
	background-image: -webkit-gradient(linear, 50% 0, 50% 100%, color-stop(0%, #14b1f6),
		color-stop(50%, #14b1f6), color-stop(51%, #14b1f6),
		color-stop(100%, #14b1f6));
	background-image: -webkit-linear-gradient(top, #14b1f6, #14b1f6 50%, #14b1f6 51%, #14b1f6);
	background-image: -moz-linear-gradient(top, #14b1f6, #14b1f6 50%, #14b1f6 51%, #14b1f6);
	background-image: -o-linear-gradient(top, #14b1f6, #14b1f6 50%, #14b1f6 51%, #14b1f6);
	background-image: linear-gradient(top, #14b1f6, #14b1f6 50%, #14b1f6 51%, #14b1f6)
}
.top_panel .x-form-trigger-wrap .x-form-text {
	border-width: 0;
	height: 22px;
}
.top_panel .x-tbar-page-first {
	background-image: url(../images/hc_poc/page-first.png)
}
.top_panel .x-tbar-page-next{
	background-image: url(../images/hc_poc/page-next.png)
}
.top_panel .x-tbar-page-prev {
	background-image: url(../images/hc_poc/page-prev.png)
}
.top_panel .x-tbar-page-last {
	background-image: url(../images/hc_poc/page-last.png)
}

.top_panel .x-tbar-loading {
	background-image: url(../images/hc_poc/refresh.png)
}
.top_panel .x-toolbar-text{
	font-size: 13px;
	color:#4d4d4d;
}
.common_another{
	background-image: url(../images/hc_poc/common_another.png);
	width:16px;
	height:16px;
	margin:0 6px;
	
}
.normal_another{
	background-position:0 0;
}
.green_light_another{
	background-position:0 -16px;
}
.blue_light_another{
	background-position:0 -32px;
}
.purple_light_another{
	background-position:0 -48px;
}
.orange_light_another{
	background-position:0 -64px;
}
.red_light_another{
	background-position:0 -80px;
}	
	
@-webkit-keyframes darkgreenPulse {
  from {-webkit-box-shadow: 0 0 10px 2px #00d696;}
  50% {-webkit-box-shadow: 0 0 30px 6px #3cf9b9; }
  to {-webkit-box-shadow: 0 0 10px 2px #00d696;}
}
@-moz-keyframes darkgreenPulse {
  from {-moz-box-shadow: 0 0 10px 2px #00d696; }
  50% {-moz-box-shadow: 0 0 30px 6px #3cf9b9; }
  to {-moz-box-shadow: 0 0 10px 2px #00d696; }
}
@keyframes darkgreenPulse {
  from {box-shadow: 0 0 10px 2px #00d696; }
  50% {box-shadow: 0 0 30px 6px #3cf9b9; }
  to {box-shadow: 0 0 10px 2px #00d696; }
}

@-webkit-keyframes bluePulse {
  from {-webkit-box-shadow: 0 0 10px 2px #0c84f5;}
  50% {-webkit-box-shadow: 0 0 30px 6px #45a5ff; }
  to {-webkit-box-shadow: 0 0 10px 2px #0c84f5;}
}
@-moz-keyframes bluePulse {
  from {-moz-box-shadow: 0 0 10px 2px #0c84f5; }
  50% {-moz-box-shadow: 0 0 30px 6px #45a5ff; }
  to {-moz-box-shadow: 0 0 10px 2px #0c84f5; }
}
@keyframes bluePulse {
  from {box-shadow: 0 0 10px 2px #0c84f5; }
  50% {box-shadow: 0 0 30px 6px #45a5ff; }
  to {box-shadow: 0 0 10px 2px #0c84f5; }
}

@-webkit-keyframes yellowPulse {
  from {-webkit-box-shadow: 0 0 10px 2px #ea5504;}
  50% {-webkit-box-shadow: 0 0 30px 6px #ffa825; }
  to {-webkit-box-shadow: 0 0 10px 2px #ea5504;}
}
@-moz-keyframes yellowPulse {
  from {-moz-box-shadow: 0 0 10px 2px #ea5504; }
  50% {-moz-box-shadow: 0 0 30px 6px #ffa825; }
  to {-moz-box-shadow: 0 0 10px 2px #ea5504; }
}
@keyframes yellowPulse {
  from {box-shadow: 0 0 10px 2px #ea5504; }
  50% {box-shadow: 0 0 30px 6px #ffa825; }
  to {box-shadow: 0 0 10px 2px #ea5504; }
}

@-webkit-keyframes orangePulse {
  from {-webkit-box-shadow: 0 0 10px 2px #ff7924;}
  50% {-webkit-box-shadow: 0 0 30px 6px #ffa973; }
  to {-webkit-box-shadow: 0 0 10px 2px #ff7924;}
}
@-moz-keyframes orangePulse {
  from {-moz-box-shadow: 0 0 10px 2px #ff7924; }
  50% {-moz-box-shadow: 0 0 30px 6px #ffa973; }
  to {-moz-box-shadow: 0 0 10px 2px #ff7924; }
}
@keyframes orangePulse {
  from {box-shadow: 0 0 10px 2px #ff7924; }
  50% {box-shadow: 0 0 30px 6px #ffa973; }
  to {box-shadow: 0 0 10px 2px #ff7924; }
}


@-webkit-keyframes redPulse {
  from {-webkit-box-shadow: 0 0 10px 2px #ef1023;}
  50% {-webkit-box-shadow: 0 0 30px 6px #fb594e; }
  to {-webkit-box-shadow: 0 0 10px 2px #ef1023;}
}
@-moz-keyframes redPulse {
  from {-moz-box-shadow: 0 0 10px 2px #ef1023; }
  50% {-moz-box-shadow: 0 0 30px 6px #fb594e; }
  to {-moz-box-shadow: 0 0 10px 2px #ef1023; }
}
@keyframes redPulse {
  from {box-shadow: 0 0 10px 2px #ef1023; }
  50% {box-shadow: 0 0 30px 6px #fb594e; }
  to {box-shadow: 0 0 10px 2px #ef1023; }
}

