<!--
  Copyright (c) 2006-2013, JGraph Ltd
  
  JQuery example for mxGraph. This example demonstrates using
  a JQuery plugin to generate labels for vertices on the fly.
-->
<html>
<head>
	<title>JQuery plugin for labels</title>
	
	<!-- Sets the basepath for the library if not in same directory -->
	<script type="text/javascript">
		mxBasePath = '../src';
	</script>

	<!-- Loads and initializes the library -->
	<script type="text/javascript" src="../src/js/mxClient.js"></script>
	
    <script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
    <script type="text/javascript" src="http://www.chartjs.org/assets/Chart.js"></script>

	<!-- Example code -->
	<script type="text/javascript">
		// Program starts here. Creates a sample graph in the
		// DOM node with the specified ID. This function is invoked
		// from the onLoad event handler of the document (see below).
		function main(container)
		{
			// Checks if the browser is supported
			if (!mxClient.isBrowserSupported())
			{
				// Displays an error message if the browser is not supported.
				mxUtils.error('Browser is not supported!', 200, false);
			}
			else
			{
				// Fixes possible clipping issues in Chrome
				mxClient.NO_FO = true;
				
				// Disables the built-in context menu
				mxEvent.disableContextMenu(container);

				// Creates the graph inside the given container
				var graph = new mxGraph(container);
				
				// Adds custom HTML labels
				graph.setHtmlLabels(true);
				
				var data = {
					labels : ['January','February','March','April','May','June','July'],
					datasets : [
						{
							fillColor : 'rgba(220,220,220,0.5)',
							strokeColor : 'rgba(220,220,220,1)',
							pointColor : 'rgba(220,220,220,1)',
							pointStrokeColor : '#fff',
							data : [65,59,90,81,56,55,40]
						},
						{
							fillColor : 'rgba(151,187,205,0.5)',
							strokeColor : 'rgba(151,187,205,1)',
							pointColor : 'rgba(151,187,205,1)',
							pointStrokeColor : '#fff',
							data : [28,48,40,19,96,27,100]
						}
					]
				};
			
				// Returns canvas with dynamic chart for vertex labels
				var graphConvertValueToString = graph.convertValueToString;
				graph.convertValueToString = function(cell)
				{
					if (this.model.isVertex(cell))
					{
						var node = document.createElement('canvas');
						node.setAttribute('width', cell.geometry.width);
						node.setAttribute('height', cell.geometry.height);
						
						// Document for empty output if not in DOM
						document.body.appendChild(node);
						
						var ctx = node.getContext("2d");
						new Chart(ctx).Line(data);
						
						return node;
					}
					
					return graphConvertValueToString.apply(this, arguments);
				};
				
				// Enables rubberband selection
				new mxRubberband(graph);
				
				// Gets the default parent for inserting new cells. This
				// is normally the first child of the root (ie. layer 0).
				var parent = graph.getDefaultParent();

				// Adds cells to the model in a single step
				graph.getModel().beginUpdate();
				try
				{
					var v1 = graph.insertVertex(parent, null, 'Hello,', 20, 20, 220, 180,
						'overflow=fill;fillColor=none;fontColor=#000000;');
					var v2 = graph.insertVertex(parent, null, 'Hello,', 380, 240, 220, 180,
						'overflow=fill;fillColor=none;fontColor=#000000;');
					var e1 = graph.insertEdge(parent, null, '', v1, v2);
				}
				finally
				{
					// Updates the display
					graph.getModel().endUpdate();
				}
				
				document.body.appendChild(mxUtils.button('+', function()
				{
					graph.zoomIn();
				}));
				document.body.appendChild(mxUtils.button('-', function()
				{
					graph.zoomOut();
				}));
			}
		};
	</script>
</head>

<!-- Page passes the container for the graph to the program -->
<body onload="main(document.getElementById('graphContainer'))">

	<!-- Creates a container for the graph with a grid wallpaper -->
	<div id="graphContainer"
		style="position:relative;overflow:hidden;width:621px;height:461px;cursor:default;border:1px solid gray;">
	</div>

</body>
</html>
