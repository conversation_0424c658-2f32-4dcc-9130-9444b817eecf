<!--
  Copyright (c) 2006-2013, JGraph Ltd
  
  FixedIcon example for mxGraph. This example demonstrates
  customizing the icon position in the mxLabel shape.
-->
<html>
<head>
	<title>Fixed icon example for mxGraph</title>

	<!-- Sets the basepath for the library if not in same directory -->
	<script type="text/javascript">
		mxBasePath = '../src';
	</script>

	<!-- Loads and initializes the library -->
	<script type="text/javascript" src="../src/js/mxClient.js"></script>

	<!-- Example code -->
	<script type="text/javascript">
		// Overrides the image bounds code to change the position
		mxLabel.prototype.getImageBounds = function(x, y, w, h)
		{
			var iw = mxUtils.getValue(this.style, mxConstants.STYLE_IMAGE_WIDTH, mxConstants.DEFAULT_IMAGESIZE);
			var ih = mxUtils.getValue(this.style, mxConstants.STYLE_IMAGE_HEIGHT, mxConstants.DEFAULT_IMAGESIZE);
			
			// Places the icon
			var ix = (w - iw) / 2;
			var iy = h - ih;
			
			return new mxRectangle(x + ix, y + iy, iw, ih);
		};

		// Program starts here. Creates a sample graph in the
		// DOM node with the specified ID. This function is invoked
		// from the onLoad event handler of the document (see below).
		function main(container)
		{
			// Checks if the browser is supported
			if (!mxClient.isBrowserSupported())
			{
				// Displays an error message if the browser is not supported.
				mxUtils.error('Browser is not supported!', 200, false);
			}
			else
			{
				// Makes the shadow brighter
				mxConstants.SHADOWCOLOR = '#C0C0C0';
				
				// Creates the graph inside the given container
				var graph = new mxGraph(container);

				// Uncomment the following if you want the container
				// to fit the size of the graph
				//graph.setResizeContainer(true);
				
				// Enables rubberband selection
				new mxRubberband(graph);
				
				// Gets the default parent for inserting new cells. This
				// is normally the first child of the root (ie. layer 0).
				var parent = graph.getDefaultParent();
								
				// Adds cells to the model in a single step
				graph.getModel().beginUpdate();
				try
				{
					var v1 = graph.insertVertex(parent, null, 'Fixed icon', 20, 20, 80, 50,
						'shape=label;image=images/plus.png;imageWidth=16;imageHeight=16;spacingBottom=10;' +
						'fillColor=#adc5ff;gradientColor=#7d85df;glass=1;rounded=1;shadow=1;');
				}
				finally
				{
					// Updates the display
					graph.getModel().endUpdate();
				}
			}
		};
	</script>
</head>

<!-- Page passes the container for the graph to the program -->
<body onload="main(document.getElementById('graphContainer'))">

	<!-- Creates a container for the graph with a grid wallpaper -->
	<div id="graphContainer"
		style="overflow:hidden;position:relative;width:321px;height:241px;background:url('editors/images/grid.gif');cursor:default;">
	</div>
</body>
</html>
