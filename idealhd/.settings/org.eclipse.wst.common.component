<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
    <wb-module deploy-name="idealhd">
        <wb-resource deploy-path="/" source-path="/hd"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/test/java"/>
        <dependent-module archiveName="idealutils-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/idealutils/idealutils">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="communication-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/communication/communication">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="core-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/core/core">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="commonadaptor-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/commonadaptor/commonadaptor">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="clientapi-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/clientapi/clientapi">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="studio-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/studio/studio">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="server-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/server/server">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="webservice-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/webservice/webservice">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="interface-0.1-SNAPSHOT.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/interface/interface">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <property name="java-output-path" value="/idealhd/build/classes"/>
        <property name="context-root" value="idealhd"/>
    </wb-module>
</project-modules>
