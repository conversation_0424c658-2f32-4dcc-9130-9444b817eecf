/*
 * Created on 2005-5-11
 */
package com.ideal.ieai.server.repository.idgenerator;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import net.sf.hibernate.HibernateException;
import net.sf.hibernate.engine.SessionImplementor;
import net.sf.hibernate.id.IdentifierGenerator;
import org.apache.log4j.Logger;

import java.io.Serializable;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * a generator used to generate an unique id.
 * 
 * <AUTHOR> Shi
 */
public class IdGenerator implements IdentifierGenerator
{
    /**
     * Logger for this class
     */
    private static final Logger _log = Logger.getLogger(IdGenerator.class);

    /**
     * 
     */
    public IdGenerator()
    {

        super();
    }

    /**
     * create id for a special class,every class can use it's class to create a id. every time
     * calling this method with a same Class , different unique result returned.
     * 
     * 
     * @return
     * @throws RepositoryException
     */
    public static long createIdFor ( Class aClass ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                String className = aClass == null ? DEFAULT_NAME : aClass.getName();
                Connection con = null;
                PreparedStatement ps = null;
                CallableStatement cs = null;
                ResultSet rs = null;
                long id = 2;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    if (DBManager.Orcl_Faimily())
                    {

                        ps = con.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, className);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else
                    {
                        cs = con.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, className);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }

                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + className, e);
                    try
                    {
                        con.rollback();
                    } catch (SQLException ex)
                    {
                        throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closeConn(con, rs, ps, "createIdFor_class", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 
     * <li>Description: 针对mysql内无自治事务，因此要区分调用创建主键id的方法</li> 
     * <AUTHOR>
     * 2018年1月4日 
     * @param aClass
     * @param type
     * @return
     * @throws RepositoryException
     * return long
     */
    public static long createIdForAll ( Class aClass, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            long id = 1;
            try
            {
                if (DBManager.Orcl_Faimily())
                { // oracle
                    id = createIdForExecActForOracle(aClass, type);
                } else if (DBManager.DB2_Faimily())
                { // db2
                    id = createIdForExecActForDB2(aClass, type);
                } else if (DBManager.Mysql_Faimily())
                { // mysql
                    id = createIdForExecActForMysql(aClass, type);
                } else
                {
                    throw new RepositoryException(ServerError.ERR_DB_TYPE);
                }
                return id;
            } catch (RepositoryException e)
            {
                if (i > 9)
                {
                    _log.error("error while connect to db", e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                }
                DBRetryUtil.waitForNextTry(i, e);
            }
        }
    }

    public static long createIdFor ( Class aClass, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                String className = aClass == null ? DEFAULT_NAME : aClass.getName();
                Connection con = null;
                PreparedStatement ps = null;
                CallableStatement cs = null;
                ResultSet rs = null;
                long id = 2;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(type);
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = con.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, className);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else // if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        cs = con.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, className);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }
                    // else
                    // {// add by rsj for mysql get pk 2017-05-27
                    // String sql_select_class = "SELECT NAME FROM SEQUENCE WHERE NAME =?";
                    // ps = con.prepareStatement(sql_select_class);
                    // ps.setString(1, className);
                    // ResultSet rs = ps.executeQuery();
                    // String name = null;
                    // if (rs.next())
                    // {
                    // name = rs.getString("name");
                    // }
                    // if (null == name)
                    // {
                    // String sql_insert_class = "INSERT INTO SEQUENCE VALUES (?, 0, 1);";
                    // ps = con.prepareStatement(sql_insert_class);
                    // ps.setString(1, className);
                    // ps.execute();
                    // }
                    //
                    // ps = con.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk from idual ");
                    // ps.setString(1, className);
                    // // ps.executeQuery();
                    // ResultSet rs_pk = ps.executeQuery();
                    // if (rs_pk.next())
                    // {
                    // String id_s = rs_pk.getString(1);
                    // id = Integer.valueOf(id_s);
                    // System.err.println(id);
                    // }
                    // con.commit();
                    // }
                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + className, e);
                    try
                    {
                        con.rollback();
                    } catch (SQLException ex)
                    {
                        throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closeConn(con, rs, ps, "createIdFor_class", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:
     * @datea:Sep 2, 2009
     * @param aClass
     * @param conn
     * @return
     * @throws RepositoryException
     */
    public static long createIdForExecAct ( Class aClass, Connection conn ) throws RepositoryException
    {
        String className = aClass == null ? DEFAULT_NAME : aClass.getName();
        PreparedStatement ps = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        long id = 1;
        try
        {
            if (DBManager.Orcl_Faimily())
            {
                ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                ps.setString(1, className);
                rs = ps.executeQuery();
                if (rs.next())
                {
                    id = rs.getLong("pk");
                }
            } else // if (JudgeDB.IEAI_DB_TYPE == 2)
            {
                cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                cs.setString(1, className);
                cs.registerOutParameter(2, java.sql.Types.INTEGER);
                cs.execute();
                id = cs.getLong(2);
            }

            return id;
        } catch (SQLException e)
        {
            _log.error("error while generating id for class " + className, e);
            throw new RepositoryException(ServerError.ERR_DB_ERROR);
        } finally
        {
            DBResource.closeCallableStatement(cs, "createIdForExecAct", _log);
            DBResource.closePSRS(rs, ps, "createIdForExecAct", _log);
        }
    }

    /**
     * 
     * <li>Description:根据类名获取单个主键的的方法，针对MySql数据库生效</li> 
     * <AUTHOR>
     * 2018年1月4日 
     * @param aClass
     * @param type
     * @return
     * @throws RepositoryException
     * return long
     */
    public static long createIdForExecActForMysql ( Class aClass, int type ) throws RepositoryException
    {
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        String className = aClass == null ? DEFAULT_NAME : aClass.getName();
        CallableStatement cs = null;
        Connection conn = null;
        long id = 1;
        try
        {
            conn = DBResource.getConnection(methodName, _log, type);
            cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
            cs.setString(1, className);
            cs.registerOutParameter(2, java.sql.Types.INTEGER);
            cs.execute();
            id = cs.getLong(2);
            conn.commit();
            return id;
        } catch (SQLException e)
        {
            _log.error("error while generating id for class " + className, e);
            throw new RepositoryException(ServerError.ERR_DB_ERROR);
        } finally
        {
            DBResource.closeCallableStatement(cs, methodName, _log);
            DBResource.closeConnection(conn, methodName, _log);
        }
    }

    /**
     * 
     * <li>Description:根据类名获取单个主键的的方法，针对DB2数据库生效</li> 
     * <AUTHOR>
     * 2018年1月4日 
     * @param aClass
     * @param type
     * @return
     * @throws RepositoryException
     * return long
     */
    public static long createIdForExecActForDB2 ( Class aClass, int type ) throws RepositoryException
    {
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        String className = aClass == null ? DEFAULT_NAME : aClass.getName();
        CallableStatement cs = null;
        Connection conn = null;
        long id = 1;
        try
        {
            conn = DBResource.getConnection(methodName, _log, type);
            cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
            cs.setString(1, className);
            cs.registerOutParameter(2, java.sql.Types.INTEGER);
            cs.execute();
            id = cs.getLong(2);
            conn.commit();
            return id;
        } catch (SQLException e)
        {
            _log.error("error while generating id for class " + className, e);
            throw new RepositoryException(ServerError.ERR_DB_ERROR);
        } finally
        {
            DBResource.closeCallableStatement(cs, methodName, _log);
            DBResource.closeConnection(conn, methodName, _log);
        }
    }

    /**
     * 
     * <li>Description:根据类名获取单个主键的的方法，针对ORACLE数据库生效</li> 
     * <AUTHOR>
     * 2018年1月4日 
     * @param aClass
     * @param type
     * @return
     * @throws RepositoryException
     * return long
     */
    public static long createIdForExecActForOracle ( Class aClass, int type ) throws RepositoryException
    {
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        String className = aClass == null ? DEFAULT_NAME : aClass.getName();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        long id = 1;
        try
        {
            conn = DBResource.getConnection(methodName, _log, type);
            ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
            ps.setString(1, className);
            rs = ps.executeQuery();
            if (rs.next())
            {
                id = rs.getLong("pk");
            }
            return id;
        } catch (SQLException e)
        {
            _log.error("error while generating id for class " + className, e);
            throw new RepositoryException(ServerError.ERR_DB_ERROR);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, methodName, _log);
        }
    }

    public static long createIdFor ( String tableName ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                CallableStatement cs = null;
                ResultSet rs = null;
                long id = 2;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = con.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, tableName);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else
                    {
                        cs = con.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, tableName);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }

                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + tableName, e);
                    try
                    {
                        con.rollback();
                    } catch (SQLException ex)
                    {
                        throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        DBResource.closeConn(con, rs, ps, "createId_conn", _log);
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <li>Description:获取跳号主键，BD2专用</li>
     * 
     * <AUTHOR> 2015-11-19
     * @param aClass
     * @param length
     * @param conn
     * @return
     * @throws RepositoryException return long
     */
    public static long createIdForExecActBig ( String aClass, long length, Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                CallableStatement cs = null;
                long id = 1;
                try
                {
                    cs = conn.prepareCall("{call PROC_GET_NEXT_PK_BIG(?,?,?)}");
                    cs.setString(1, aClass);
                    cs.setLong(2, length);
                    cs.registerOutParameter(3, java.sql.Types.INTEGER);
                    cs.execute();
                    id = cs.getLong(3);

                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + aClass, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public static long createIdForBigNoConn ( String aClass, long length,int dataSourceType) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                CallableStatement cs = null;
                long id = 1;
                try
                {
                    conn =  DBManager.getInstance().getJdbcConnection(dataSourceType);
                    cs = conn.prepareCall("{call PROC_GET_NEXT_PK_BIG(?,?,?)}");
                    cs.setString(1, aClass);
                    cs.setLong(2, length);
                    cs.registerOutParameter(3, java.sql.Types.INTEGER);
                    cs.execute();
                    id = cs.getLong(3);

                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + aClass, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }


    /**
     * @Description: 获取最大主键，不传递数据库连接
     * @Param: [aClass, length]
     * @return: long
     * @Author: zuochao_wang
     * @Date: 2023/7/11 11:26
     */
    public static long createIdForBigNoConn ( String aClass, long length) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                CallableStatement cs = null;
                long id = 1;
                try
                {
                    conn =  DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    cs = conn.prepareCall("{call PROC_GET_NEXT_PK_BIG(?,?,?)}");
                    cs.setString(1, aClass);
                    cs.setLong(2, length);
                    cs.registerOutParameter(3, java.sql.Types.INTEGER);
                    cs.execute();
                    id = cs.getLong(3);

                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + aClass, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 取基线源
     * @param aClass
     * @param length
     * @return
     * @throws RepositoryException
     */
    public static long createIdForBigOpmConn ( String aClass, long length) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                CallableStatement cs = null;
                long id = 1;
                try
                {
                    conn =  DBManager.getInstance().getJdbcConnection(Constants.IEAI_OPM);
                    cs = conn.prepareCall("{call PROC_GET_NEXT_PK_BIG(?,?,?)}");
                    cs.setString(1, aClass);
                    cs.setLong(2, length);
                    cs.registerOutParameter(3, java.sql.Types.INTEGER);
                    cs.execute();
                    id = cs.getLong(3);
                    _log.info(conn.getMetaData().getURL());
                    conn.commit();
                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + aClass, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public static long createIdForExecActBig ( String aClass, long length) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                CallableStatement cs = null;
                long id = 1;
                try
                {
                    conn = DBResource.getConnection("createIdForExecActBig", _log, Constants.IEAI_SUS);
                    cs = conn.prepareCall("{call PROC_GET_NEXT_PK_BIG(?,?,?)}");
                    cs.setString(1, aClass);
                    cs.setLong(2, length);
                    cs.registerOutParameter(3, java.sql.Types.INTEGER);
                    cs.execute();
                    id = cs.getLong(3);

                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + aClass, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                        if (ps != null)
                        {
                            ps.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException ex)
                    {
                        ps = null;
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    public static synchronized long createIdForExecAcDbaas ( String aClass, long length, Connection conn )
            throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                CallableStatement cs = null;
                long id = 1;
                try
                {
                    cs = conn.prepareCall("{call PROC_GET_NEXT_PK_BIG(?,?,?)}");
                    cs.setString(1, aClass);
                    cs.setLong(2, length);
                    cs.registerOutParameter(3, java.sql.Types.INTEGER);
                    cs.execute();
                    id = cs.getLong(3);

                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + aClass, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <AUTHOR>
     * @des:锟斤拷锟斤拷ID
     * @datea:2011-8-1
     * @param tablename
     * @param conn
     * @return
     * @throws RepositoryException
     */
    public static long createId ( String tablename, Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                CallableStatement cs = null;
                ResultSet rs = null;
                long id = 1;
                try
                {// 增加oracle与DB2的区分，如果是DB2走else，否则走if。
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, tablename);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else // if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, tablename);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }
                    // else
                    // {// add by rsj for mysql get pk 2017-05-27
                    // String sql_select_class = "SELECT NAME FROM SEQUENCE WHERE NAME =?";
                    // ps = conn.prepareStatement(sql_select_class);
                    // ps.setString(1, tablename);
                    // ResultSet rs = ps.executeQuery();
                    // String name = null;
                    // if (rs.next())
                    // {
                    // name = rs.getString("name");
                    // }
                    // if (null == name)
                    // {
                    // String sql_insert_class = "INSERT INTO SEQUENCE VALUES (?, 0, 1);";
                    // ps = conn.prepareStatement(sql_insert_class);
                    // ps.setString(1, tablename);
                    // ps.execute();
                    // }
                    //
                    // ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk from idual ");
                    // ps.setString(1, tablename);
                    // // ps.executeQuery();
                    // ResultSet rs_pk = ps.executeQuery();
                    // if (rs_pk.next())
                    // {
                    // String id_s = rs_pk.getString(1);
                    // id = Integer.valueOf(id_s);
                    // System.err.println(id);
                    // }
                    // conn.commit();
                    // }
                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + tablename, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "createId_conn", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        cs = null;
                        _log.error("error while close cs connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 
     * <li>Description:自建connection获取主键</li>
     * 
     * <AUTHOR> 2017年6月13日
     * @param tablename
     * @param type
     * @return
     * @throws RepositoryException return long
     */
    public static long createIdNoConnection ( String tablename, int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                CallableStatement cs = null;
                ResultSet rs = null;
                long id = 1;
                try
                {
                    conn = DBResource.getConnection("createIdNoConnection", _log, type);
                    // 增加oracle与DB2的区分，如果是DB2走else，否则走if。
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, tablename);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else
                    // if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, tablename);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                        conn.commit();
                    }
                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + tablename, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "createIdNoConnection", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        cs = null;
                        _log.error("error while close cs connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    // add start by yan_wang
    /**
     * 锟斤拷锟斤拷锟斤拷锟絠d
     * 
     * @param tableName
     * @param conn
     * @return
     * @throws RepositoryException
     */

    public static long createIdForGroupMessage ( String tableName, Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement cs = null;
                long id = 2;
                try
                {
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, tableName);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else // if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, tableName);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }
                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + tableName, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "createIdForGroupMessage", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        cs = null;
                        _log.error("error while close cs connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * 锟斤拷锟斤拷锟斤拷锟絠d
     * 
     * @param tableName
     * @param conn
     * @return
     * @throws RepositoryException
     */
    public static long createIdForTimeSet ( String tableName, Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs =null;
                CallableStatement cs = null;
                long id = 2;
                try
                {
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                        ps.setString(1, tableName);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else // if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                        cs.setString(1, tableName);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }
                    // else
                    // {// add by rsj for mysql get pk 2017-05-27
                    // String sql_select_class = "SELECT NAME FROM SEQUENCE WHERE NAME =?";
                    // ps = conn.prepareStatement(sql_select_class);
                    // ps.setString(1, tableName);
                    // ResultSet rs = ps.executeQuery();
                    // String name = null;
                    // if (rs.next())
                    // {
                    // name = rs.getString("name");
                    // }
                    // if (null == name)
                    // {
                    // String sql_insert_class = "INSERT INTO SEQUENCE VALUES (?, 0, 1);";
                    // ps = conn.prepareStatement(sql_insert_class);
                    // ps.setString(1, tableName);
                    // ps.execute();
                    // }
                    //
                    // ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk from idual ");
                    // ps.setString(1, tableName);
                    // // ps.executeQuery();
                    // ResultSet rs_pk = ps.executeQuery();
                    // if (rs_pk.next())
                    // {
                    // String id_s = rs_pk.getString(1);
                    // id = Integer.valueOf(id_s);
                    // System.err.println(id);
                    // }
                    // conn.commit();
                    // }
                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + tableName, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "createIdForTimeSet", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        cs = null;
                        _log.error("error while close cs connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    // add end by yan_wang

    public static synchronized String getID ()
    {
        String str = new SimpleDateFormat("yyyyMMddHHmmssSSSS").format(new Date());
        while (str.equals(new SimpleDateFormat("yyyyMMddHHmmssSSSS").format(new Date())))
        {
        }
        return str;
    }

    private static final String DEFAULT_NAME = "default_key";

    public Serializable generate ( SessionImplementor session, Object aClass ) throws SQLException, HibernateException
    {
        for (int i = 0;; i++)
        {
            try
            {
                return new Long(createIdFor(aClass.getClass()));
            } catch (RepositoryException ex)
            {
                try
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                } catch (RepositoryException ex1)
                {
                    _log.error("error while get premriy key" + ex1);
                }
            }
        }
    }

    public static long createIdForTable ( String tablename, Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs =null;
                long id = 1;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                    ps = con.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                    ps.setString(1, tablename);
                    rs = ps.executeQuery();
                    if (rs.next())
                    {
                        id = rs.getLong("pk");
                    }
                    return id;
                } catch (DBException ex)
                {
                    if (i > 9)
                    {
                        _log.error("error while connect to db", ex);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } catch (SQLException e)
                {
                    _log.error("error while generating id for table " + tablename, e);
                    try
                    {
                        con.rollback();
                    } catch (SQLException ex)
                    {
                        throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closeConn(con, rs, ps, "createIdNoConnection", _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }

    }

    public static long updateIdForTable ( String tablename, Long size, Connection conn ) throws SQLException
    {
        PreparedStatement ps = null;
        long id = 0;
        try
        {
            ps = conn.prepareStatement("UPDATE IEAI_ID SET IVALUE = ? WHERE ICLASSNAME = ?");
            ps.setLong(1, size);
            ps.setString(2, tablename);
            ps.executeUpdate();
        } catch (SQLException e)
        {
            _log.error("error while generating id for table " + tablename, e);
            throw e;
        } finally
        {
            try
            {
                if (ps != null)
                {
                    ps.close();
                }
            } catch (SQLException ex)
            {
                _log.error("error while close jdbc connection", ex);
            }
        }
        return id;
    }

    public static long createIdFor ( String tableName, Connection conn ) throws Exception
    {
        PreparedStatement ps = null;
        CallableStatement cs = null;
        ResultSet rs = null;
        try
        {

            long id = 2;
            if (DBManager.Orcl_Faimily())
            {
                ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK(?) as pk FROM IDUAL");
                ps.setString(1, tableName);
                rs = ps.executeQuery();
                if (rs.next())
                {
                    id = rs.getLong("pk");
                }
            } else // if (JudgeDB.IEAI_DB_TYPE == 2)
            {
                cs = conn.prepareCall("{call PROC_GET_NEXT_PK(?, ?)}");
                cs.setString(1, tableName);
                cs.registerOutParameter(2, java.sql.Types.INTEGER);
                cs.execute();
                id = cs.getLong(2);
            }
            return id;
        } catch (SQLException e)
        {
            _log.error("error while generating id for class " + tableName, e);
            try
            {
                conn.rollback();
            } catch (SQLException ex)
            {
                throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
            }
            throw new RepositoryException(ServerError.ERR_DB_ERROR);
        } finally
        {
            DBResource.closePSRS(rs, ps, "createIdFor", _log);
            try
            {
                if (cs != null)
                {
                    cs.close();
                }
            } catch (SQLException ex)
            {
                _log.error("error while close cs connection", ex);
            }
        }
    }

    public static long createId_MulWirit_big ( String tableName,int size, Map<String, Connection> mapCon ) throws Exception{
        String method =Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        boolean isFirstTime = false; //判断第一次和无异常
        Long iid=0L;
        if(size > 0) {
            for (Map.Entry<String, Connection> mapConnection : mapCon.entrySet())
            {
                try
                {
                    conn = mapConnection.getValue();
                    iid = IdGenerator.createIdForExecActBig( tableName, size, conn);
                } catch (Exception e)
                {
                    _log.error(method+" is error ,messages " + e.getMessage());
                    break;
                }
            }
            if(iid == 0) {
                _log.error("主键生成异常");
                throw new Exception();
            }
        }
        return iid;
    }

    public static long createId_MulWirit_logic ( String tableName, long iid,Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                PreparedStatement psId = null;
                CallableStatement cs = null;
                long id = 2;
                try
                {
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = conn.prepareStatement("SELECT FUN_GET_NEXT_PK_MUL(?,?) as pk FROM IDUAL");
                        ps.setString(1, tableName);
                        ps.setLong(2, iid);
                        ResultSet rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else
                    {
                        cs = conn.prepareCall("{call FUN_GET_NEXT_PK_MUL(?, ?)}");
                        cs.setString(1, tableName);
                        cs.setLong(2, iid);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }
                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + tableName, e);
                    try
                    {
                        con.rollback();
                    } catch (SQLException ex)
                    {
                        throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
                    }
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    try
                    {
                        if (ps != null)
                        {
                            ps.close();
                        }
                        if (psId != null)
                        {
                            psId.close();
                        }
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        _log.error("error while close jdbc connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }
	 /**
     * <li>Description:</li> 
     * <AUTHOR>
     * @DESC 创建时间戳 年月日时分+0001 同一个分钟时间递增
     * 2023-4-12 
     * @param tablename
     * @param conn
     * @return
     * @throws RepositoryException
     * return long
     */
    public static long createTime ( String Time, Connection conn ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                CallableStatement cs = null;
                ResultSet rs = null;
                long id = 1;
                try
                {// 增加oracle与DB2的区分，如果是DB2走else，否则走if。
                    if (DBManager.Orcl_Faimily())
                    {
                        ps = conn.prepareStatement("SELECT FUN_GET_NEXT_TIME(?) as pk FROM IDUAL");
                        ps.setString(1, Time);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("pk");
                        }
                    } else {
                        cs = conn.prepareCall("{call PROC_GET_NEXT_TIME(?, ?)}");
                        cs.setString(1, Time);
                        cs.registerOutParameter(2, java.sql.Types.INTEGER);
                        cs.execute();
                        id = cs.getLong(2);
                    }
                    return id;
                } catch (SQLException e)
                {
                    _log.error("error while generating id for class " + Time, e);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "createId_conn", _log);
                    try
                    {
                        if (cs != null)
                        {
                            cs.close();
                        }
                    } catch (SQLException ex)
                    {
                        cs = null;
                        _log.error("error while close cs connection", ex);
                    }
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

}