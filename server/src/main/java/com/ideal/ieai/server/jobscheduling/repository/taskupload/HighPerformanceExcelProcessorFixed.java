package com.ideal.ieai.server.jobscheduling.repository.taskupload;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.jobscheduling.repository.actmutexconfig.ActMutexConfigManager;
import com.ideal.ieai.server.jobscheduling.repository.createexcel.StartCreatePrj;
import com.ideal.ieai.server.jobscheduling.repository.excelmodelscan.ExcelModelScanManager;
import com.ideal.ieai.server.jobscheduling.repository.taskCrossMainline.TaskCrossMainlineManager;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActInfoBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBSourceMonitor;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.repository.project.ProjectSaveUtilBean;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 批量保存结果类
 */
class BatchSaveResult {
    boolean overallResult;
    List<String> listNotMainLine;

    BatchSaveResult(boolean overallResult, List<String> listNotMainLine) {
        this.overallResult = overallResult;
        this.listNotMainLine = listNotMainLine;
    }
}

/**
 * 高性能Excel处理器 - 完全按照conlockTable逻辑，去掉所有校验，保留所有业务逻辑
 */
public class HighPerformanceExcelProcessorFixed {

    private static final Logger log = Logger.getLogger(HighPerformanceExcelProcessorFixed.class);

    // 单例模式
    private static HighPerformanceExcelProcessorFixed instance = new HighPerformanceExcelProcessorFixed();

    // 原有Manager实例 - 重用原有逻辑
    private UpLoadExcelManager uploadExcelManager = UpLoadExcelManager.getInstance();
    private UpLoadTempExcleManager upLoadTempExcleManager = UpLoadTempExcleManager.getInstance();
    private TaskUploadManager taskUploadManager = TaskUploadManager.getInstance();
    private ExcelModelScanManager scanManager = ExcelModelScanManager.getInstance();
    private DbUtilUpLoadExcel dbUtilUpLoadExcel = new DbUtilUpLoadExcel();

    /**
     * 私有构造函数
     */
    private HighPerformanceExcelProcessorFixed() {
    }

    /**
     * 获取单例实例
     */
    public static HighPerformanceExcelProcessorFixed getInstance() {
        return instance;
    }

    /**
     * 高性能Excel处理 - 完全按照conlockTable逻辑，去掉所有校验，保留所有业务逻辑
     */
    public String processExcelHighPerformance(File fileNew, CommonsMultipartFile file, String fileName,
                                              UserInfo user, String userName) throws RepositoryException {

        long startTime = System.currentTimeMillis();
        Connection basicConn = null;
        Connection mysqlConn = null;
        Connection csconn = null;
        String messages = "";
        String deleteLag = "";
        String fileNameFinal = fileName;

        try {
            long totalStartTime = System.currentTimeMillis();
            log.info("【性能】=== " + fileNameFinal + "【高性能Excel导入】开始！用户: " + userName + " ===");

            // 1. 获取数据库连接
            long connStart = System.currentTimeMillis();
            basicConn = getBasicConnection();
            if (basicConn == null) {
                return "无法获取数据库连接";
            }

            // 2. 获取MySQL连接（如果需要）
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                mysqlConn = getMysqlConnection();
            }

            // 3. 获取基础类型
            int basicType = getBasicType();
            log.info("【性能】数据库连接获取耗时: " + (System.currentTimeMillis() - connStart) + "ms");

            // 4. 文件路径处理
            String testExcelPath = fileNew.getAbsolutePath() + File.separator + fileName;
            String filePath = fileNew.getAbsolutePath();

            // 5. 获取工程名称（去掉校验，直接获取）
            String projectName = getProjectNameFromExcelNoValidation(testExcelPath);
            if (projectName == null || projectName.isEmpty()) {
                return "无法获取工程名称，请确定Excel文件的正确性";
            }

            // 6. 初始化ProjectSaveUtilBean
            ProjectSaveUtilBean projectSaveUtilBean = new ProjectSaveUtilBean();
            projectSaveUtilBean.setFileNameFinal(fileNameFinal);
            projectSaveUtilBean.setUser(user);
            projectSaveUtilBean.setBasicConnection(basicConn);
            projectSaveUtilBean.setFileName(projectName);
            projectSaveUtilBean.setFilePath(filePath);
            projectSaveUtilBean.setUserName(userName);
            projectSaveUtilBean.setUserId(user.getId());
            projectSaveUtilBean.setTestExcelPath(testExcelPath);

            // 7. IEAI_RECORD表处理（保留业务逻辑）
            long recordStart = System.currentTimeMillis();
            handleRecordTable(projectName, basicConn, basicType, projectSaveUtilBean);
            log.info("【性能】IEAI_RECORD表处理耗时: " + (System.currentTimeMillis() - recordStart) + "ms");

            // 8. Excel解析（使用高性能解析方法）
            long parseStart = System.currentTimeMillis();
            Map<String, Object> returnMap = getActInfoCompatibleHighPerformance(testExcelPath, projectName,
                    uploadExcelManager, basicConn, user.getFullName());

            String deleteFlag = (String) returnMap.get("deleteFlag");
            Map<String, ActInfoBean> mapList = (Map<String, ActInfoBean>) returnMap.get("mapList");

            if (mapList == null || mapList.isEmpty()) {
                return "Excel文件无数据或解析失败";
            }

            log.info("【性能】Excel高性能解析耗时: " + (System.currentTimeMillis() - parseStart) + "ms，共 " + mapList.size() + " 条记录");

            // 调试：检查Excel中的依赖关系数据
            long depStart = System.currentTimeMillis();
            debugDependencyData(mapList);

            // 根据前继关系推导后继关系
            buildSuccessorRelationships(mapList);
            log.info("【性能】依赖关系分析和推导耗时: " + (System.currentTimeMillis() - depStart) + "ms");

            // 9. 增量导入处理（保留业务逻辑，去掉校验）
            long incrementalStart = System.currentTimeMillis();
            Map<String, Long> allActMap = new HashMap<>();
//            String incrementalImport = "false"; // 默认全量导入
//            if (!incrementalImport.isEmpty() && incrementalImport.equals("false")) {
//                deleteFlag = "";
//                // 去掉校验，直接删除
//                allActMap = ExcelActUtil.getInstance().removeExcelWithTableNew(projectName, basicConn, 0);
//            }
//            log.info("【性能】增量导入处理耗时: " + (System.currentTimeMillis() - incrementalStart) + "ms，删除了 " + allActMap.size() + " 个作业");

            // 10. 设置mapList到projectSaveUtilBean
            projectSaveUtilBean.getMapList().add(mapList);

            // 11. 跳过临时表逻辑，直接使用内存存储（性能优化）
            log.info("【调试】跳过临时表逻辑，使用内存存储，mapList大小: " + mapList.size());

            // 12. Agent信息保存（使用超高性能方法）
//            long agentStart = System.currentTimeMillis();
//            saveAgentInfoToTableUltraHighPerformance(mapList, projectSaveUtilBean, basicConn, basicType);
//            log.info("【性能】Agent信息保存耗时: " + (System.currentTimeMillis() - agentStart) + "ms");

            // 13. 核心保存逻辑（保留原有逻辑，去掉校验）
            long saveStart = System.currentTimeMillis();
            messages = saveExcelDataNoValidation(mapList, projectName, basicConn, mysqlConn, csconn,
                    projectSaveUtilBean, user, userName, testExcelPath, filePath, basicType,
                    deleteFlag, allActMap);
            log.info("【性能】核心保存逻辑耗时: " + (System.currentTimeMillis() - saveStart) + "ms");

            if (!"导入成功".equals(messages) && !"上传到延时导入表成功".equals(messages)) {
                return messages;
            }

            long totalTime = System.currentTimeMillis() - totalStartTime;
            double recordsPerSecond = (double) mapList.size() * 1000 / totalTime;

            log.info("【性能】=== 高性能Excel处理完成: " + fileName +
                    ", 处理 " + mapList.size() + " 条记录" +
                    ", 总耗时: " + totalTime + "ms" +
                    ", 速度: " + String.format("%.2f", recordsPerSecond) + " 记录/秒 ===");

            return "处理成功，共处理 " + mapList.size() + " 条记录，总耗时 " + totalTime + "ms";

        } catch (Exception e) {
            log.error("高性能Excel处理失败: " + fileName, e);
            try {
                if (basicConn != null) {
                    basicConn.rollback();
                }
                if (mysqlConn != null) {
                    mysqlConn.rollback();
                }
                if (csconn != null) {
                    csconn.rollback();
                }
            } catch (SQLException se) {
                log.error("回滚失败", se);
            }
            return "处理失败: " + e.getMessage();
        } finally {
            // 关闭连接
            if (basicConn != null) {
                try {
                    basicConn.close();
                } catch (SQLException e) {
                    log.error("关闭基础连接失败", e);
                }
            }
            if (mysqlConn != null) {
                try {
                    mysqlConn.close();
                } catch (SQLException e) {
                    log.error("关闭MySQL连接失败", e);
                }
            }
            if (csconn != null) {
                try {
                    csconn.close();
                } catch (SQLException e) {
                    log.error("关闭CS连接失败", e);
                }
            }
        }
    }

    /**
     * 获取基础数据库连接
     */
    private Connection getBasicConnection() throws RepositoryException {
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                .getDBsourceList(Constants.IEAI_IEAI_BASIC);

        for (DBSourceMonitor monitor : dbList) {
            if (monitor.getBasic() == 1) {
                return DBResource.getConnection("processExcelHighPerformance", log, (int) monitor.getGroupId());
            }
        }
        throw new RepositoryException(504); // 未找到基础数据源
    }

    /**
     * 获取基础类型
     */
    private int getBasicType() throws RepositoryException {
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                .getDBsourceList(Constants.IEAI_IEAI_BASIC);

        for (DBSourceMonitor monitor : dbList) {
            if (monitor.getBasic() == 1) {
                return (int) monitor.getGroupId();
            }
        }
        throw new RepositoryException(504); // 未找到基础类型
    }

    /**
     * 获取MySQL连接 - 按照原有conlockTable逻辑
     */
    private Connection getMysqlConnection() throws RepositoryException {
        return DBResource.getSchedulerConnection("processExcelHighPerformance", log, Constants.IEAI_IEAI_BASIC);
    }

    /**
     * 获取工程名称（去掉校验）
     */
    private String getProjectNameFromExcelNoValidation(String testExcelPath) {
        try {
            // 直接调用原有方法的解析部分，跳过校验
            Map<String, Object> m = dbUtilUpLoadExcel.getProjectNameCompatible(testExcelPath);
            if ((Boolean) m.get("status")) {
                return (String) m.get("projectName");
            }
            return null;
        } catch (Exception e) {
            log.error("获取工程名称失败", e);
            return null;
        }
    }

    /**
     * IEAI_RECORD表处理（保留业务逻辑）
     */
    private void handleRecordTable(String projectName, Connection basicConn, int basicType,
                                   ProjectSaveUtilBean projectSaveUtilBean) throws Exception {
        projectSaveUtilBean.setRecordId(Long.parseLong("0"));

        // 判断IEAI_RECORD表中是否存在相同的数据，有则不做任何操作，没有则insert
        if (dbUtilUpLoadExcel.containsProjectName(projectName, basicConn) == 0) {
            String sql = "INSERT INTO IEAI_RECORD(IID,ROCKNAME,PROJECTNAME) VALUES(?,?,?)";
            try (PreparedStatement ps2 = basicConn.prepareStatement(sql)) {
                long recordid = IdGenerator.createIdNoConnection("IEAI_RECORD", basicType);
                ps2.setLong(1, recordid);
                ps2.setString(2, projectName);
                ps2.setString(3, projectName);
                projectSaveUtilBean.setRecordId(recordid);
                ps2.executeUpdate();
                basicConn.commit();
            }
        }

        // 更新ROCKNAME
        String sql2 = "UPDATE IEAI_RECORD SET ROCKNAME='name' WHERE PROJECTNAME=?";
        try (PreparedStatement ps = basicConn.prepareStatement(sql2)) {
            ps.setString(1, projectName);
            ps.executeUpdate();
        }
    }

    /**
     * Excel解析（调用原有方法，但跳过校验）
     */
    private Map<String, Object> getActInfoCompatibleNoValidation(String testExcelPath, String projectName,
                                                                 UpLoadExcelManager uploadExcelManager,
                                                                 Connection basicConn, String userName) throws Exception {
        // 直接调用原有的getActInfoCompatible方法
        return dbUtilUpLoadExcel.getActInfoCompatible(testExcelPath, projectName, uploadExcelManager, basicConn, userName);
    }

    /**
     * 核心保存逻辑（去掉校验，保留所有业务逻辑）
     */
    private String saveExcelDataNoValidation(Map<String, ActInfoBean> mapList, String projectName,
                                             Connection basicConn, Connection mysqlConn, Connection csconn,
                                             ProjectSaveUtilBean projectSaveUtilBean,
                                             UserInfo user, String userName, String testExcelPath, String filePath,
                                             int basicType, String deleteFlag, Map<String, Long> allActMap) throws Exception {

        String messages = "";
        String deleteLag = deleteFlag.equals("2") ? "del" : "";
        projectSaveUtilBean.setDeleteLag(deleteLag);

        // 判断是否启用会计日期延迟生效
        if (Boolean.parseBoolean(Environment.getInstance().getSysConfig(Environment.DATADATE_DELAY_SWITCH))) {
            // 延迟生效模式
            return saveToScanTablesNoValidation(mapList, projectName, basicConn, mysqlConn, projectSaveUtilBean,
                    user, userName, testExcelPath, filePath, basicType, deleteLag);
        } else {
            // 正常模式
            return saveToNormalTablesNoValidation(mapList, projectName, basicConn, mysqlConn, csconn,
                    projectSaveUtilBean, user, userName, testExcelPath, filePath, basicType, deleteLag, allActMap);
        }
    }

    /**
     * 保存到扫描表（延迟生效模式，去掉校验）
     */
    private String saveToScanTablesNoValidation(Map<String, ActInfoBean> mapList, String projectName,
                                                Connection basicConn, Connection mysqlConn,
                                                ProjectSaveUtilBean projectSaveUtilBean,
                                                UserInfo user, String userName, String testExcelPath, String filePath,
                                                int basicType, String deleteLag) throws Exception {

        long startScan = System.currentTimeMillis();
        long id = IdGenerator.createIdNoConnection("IEAI_BASIC_EXCELMODEL", basicType);

        if (deleteLag.equals("del")) {
            ActMutexConfigManager.getInstance().deleteCopyMutexAtImport(basicConn, mapList);
        }

        // 保存到基线库
        scanManager.importExcelModelScan(projectName, mapList, projectSaveUtilBean, user, id, basicConn, basicType);
        long endScan = System.currentTimeMillis();
        log.info("Excel导入到扫描表成功，耗时：" + (endScan - startScan));

        startScan = System.currentTimeMillis();
        // 保存更新扫描表的依赖关系
        scanManager.updateSuccAndPreOneConnection(basicConn, projectName);
        endScan = System.currentTimeMillis();
        log.info("Excel导入到扫描表，更新依赖关系耗时：" + (endScan - startScan));

        scanManager.saveSacnRecord(projectSaveUtilBean, projectName, basicType, user);
        // 插入到基础拷贝表
        scanManager.delBasicCopy(basicConn, projectName);
        scanManager.saveScanExcelModelUploadExcel(basicConn, mapList, filePath, userName, user.getId(), testExcelPath, id);

        // 保存非基线库的数据
        for (Iterator it = projectSaveUtilBean.getConnectionMap().keySet().iterator(); it.hasNext();) {
            String dbkey = it.next().toString();
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                mysqlConn.commit();
                dbUtilUpLoadExcel.rollbackMysqlForScanTempTalbes(projectSaveUtilBean.getConnectionMap().get(dbkey));
            }
        }

        // 是否回滚
        boolean isrollback = false;
        // 循环提交数据库连接
        for (Iterator it = projectSaveUtilBean.getConnectionMap().keySet().iterator(); it.hasNext();) {
            Object key = it.next();
            isrollback = dbUtilUpLoadExcel.commitOrRollbackConn(projectSaveUtilBean.getConnectionMap().get(key), isrollback);
            if (!isrollback) {
                projectSaveUtilBean.getComitConnectionMap().put(key.toString(), projectSaveUtilBean.getConnectionMap().get(key));
            }
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                mysqlConn.commit();
                dbUtilUpLoadExcel.rollbackMysqlForScanTempTalbes(projectSaveUtilBean.getConnectionMap().get(key));
            }
        }

        // 提交基线库连接
        isrollback = dbUtilUpLoadExcel.commitOrRollbackConn(basicConn, isrollback);
        if (!isrollback) {
            projectSaveUtilBean.getComitConnectionMap().put(projectSaveUtilBean.getBasicConnectionName(), basicConn);
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                mysqlConn.commit();
                dbUtilUpLoadExcel.rollbackMysqlForScanTempTalbes(basicConn);
            }
        }

        // 执行回滚方案
        if (isrollback) {
            for (Iterator it = projectSaveUtilBean.getComitConnectionMap().keySet().iterator(); it.hasNext();) {
                Object key = it.next();
                if (JudgeDB.IEAI_DB_TYPE == 3) {
                    mysqlConn.commit();
                    dbUtilUpLoadExcel.rollbackMysqlForScanTempTalbes(projectSaveUtilBean.getConnectionMap().get(key));
                }
            }
            log.error("工程上传延迟生效存储失败!");
            throw new RepositoryException(504);
        }

        basicConn.commit();

        // 渤海银行跨主线依赖上传
        boolean crossMainLine = Environment.getInstance().getBohaiBankCrossMainLine();
        if (crossMainLine) {
            TaskCrossMainlineManager.getInstance().saveTaskCrossMainline(mapList, 0);
            log.info("bohaibank.cross.main.line！true");
        } else {
            log.info("bohaibank.cross.main.line！false");
        }

        return "上传到延时导入表成功";
    }

    /**
     * 保存到正常表（正常模式，去掉校验）
     */
    private String saveToNormalTablesNoValidation(Map<String, ActInfoBean> mapList, String projectName,
                                                  Connection basicConn, Connection mysqlConn, Connection csconn,
                                                  ProjectSaveUtilBean projectSaveUtilBean,
                                                  UserInfo user, String userName, String testExcelPath, String filePath,
                                                  int basicType, String deleteLag, Map<String, Long> allActMap) throws Exception {

        // 直接检查mapList（去掉临时表依赖，性能优化）
        log.info("【调试】直接使用内存数据，mapList大小: " + mapList.size());

        if (mapList.size() > 0) {
            log.info("【调试】内存数据有效，开始正常业务逻辑处理");
            long startcopy = System.currentTimeMillis();
            long id = IdGenerator.createIdNoConnection("IEAI_BASIC_EXCELMODEL", basicType);

            if (deleteLag.equals("del")) {
                ActMutexConfigManager.getInstance().deleteCopyMutexAtImport(basicConn, mapList);
            }

            // 1. 保存到拷贝表（使用超高性能方法）
            long copyStart = System.currentTimeMillis();
            importExcelModelCopyManagerUltraHighPerformance(mapList, projectSaveUtilBean, deleteLag,
                    user.getFullName(), "copy", id, basicConn, basicType, allActMap);
            log.info("【性能】保存到拷贝表耗时：" + (System.currentTimeMillis() - copyStart) + "ms");

            // 2. 跳过更新依赖关系（已在超高性能方法中处理）
            log.info("【性能】跳过更新依赖关系步骤，已在批量插入中处理");

            long startbasic = System.currentTimeMillis();
            projectSaveUtilBean.setBasicExcelModelId(id);

            // 3. 简化相关信息保存（只保存必要的信息）
            long batchSaveStart = System.currentTimeMillis();
            BatchSaveResult batchResult = saveCopyRelatedDataSimplified(basicConn, mapList, filePath, userName, user, testExcelPath, id, projectName);
            boolean flag = batchResult.overallResult;
            List<String> listNotMainLine = batchResult.listNotMainLine;
            log.info("【性能】简化保存相关数据耗时: " + (System.currentTimeMillis() - batchSaveStart) + "ms");

            // 7. 处理工程信息
            StartCreatePrj oo = new StartCreatePrj();
            Object[] object = null;
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                object = oo.getProjectInfoCopy(projectName, mysqlConn);
            } else {
                object = oo.getProjectInfoCopy(projectName, basicConn);
            }

            // 8. 获取主线信息（性能优化：直接从内存mapList中提取）
            List<ActInfoBean> listActinfobean = extractMainLinesFromMapList(mapList);
            log.info("【调试】从内存提取主线信息，共" + listActinfobean.size() + "个主线");

            if (!listNotMainLine.isEmpty()) {
                for (String mainlineName : listNotMainLine) {
                    ActInfoBean infoBean = new ActInfoBean();
                    infoBean.setMainline(mainlineName);
                    listActinfobean.add(infoBean);
                }
            }

            // 9. 导入到主表（性能优化：直接使用内存mapList）
            long importMainStart = System.currentTimeMillis();
            Map<String, List<ActInfoBean>> mapListbean = convertMapListToMainlineMap(mapList);
            log.info("【性能】内存数据转换耗时: " + (System.currentTimeMillis() - importMainStart) + "ms，转换得到 " + mapListbean.size() + " 个主线的数据");

            int falg = 0;
            // 使用高性能批量导入方法
            long batchImportStart = System.currentTimeMillis();
            boolean importResult = importUploadExcelModelBatchHighPerformance(projectName, listActinfobean, mapListbean, basicConn, basicType);
            log.info("【性能】批量导入所有主线耗时: " + (System.currentTimeMillis() - batchImportStart) + "ms，结果: " + importResult);
            if (importResult) {
                falg = 1;
            }
            log.info("【性能】所有主线导入完成，总耗时: " + (System.currentTimeMillis() - importMainStart) + "ms");

            // 10. 上传工程Excel
            if (falg == 1) {
                flag = uploadExcelManager.uploadProjectExcel(basicConn, projectName, "",
                        user, projectSaveUtilBean, basicType, String.valueOf(false));
            }

            // 11. 保存新主线
            boolean flag1 = taskUploadManager.saveExcelNewMainLine(object, user, basicConn, basicType);

            // 12. 清理临时数据
            uploadExcelManager.deleteExcelModelCopy(projectName, basicConn);
            uploadExcelManager.deleteBasicExcelModel(projectName, basicConn);

            // 13. 处理互斥配置
            if (!deleteLag.equals("del")) {
                ActMutexConfigManager.getInstance().saveCopyMutexMainAtImport(basicConn, mapList, user.getId());
            }
            if (!mapList.isEmpty()) {
                ActMutexConfigManager.getInstance().saveSystemGroup(basicConn, mapList);
            }

            if (!flag1) {
                return "组织工程信息入库失败,请重新导入。";
            }

            log.info("Excel导入到Excel信息表数据耗时：" + (System.currentTimeMillis() - startbasic) + "ms");

            // 14. 跨主线依赖处理（去掉校验）
            if (csconn != null) {
                TaskCrossMainlineManager tcManager = new TaskCrossMainlineManager();
                List<Map> crossList = tcManager.getCrossMainLineListByProjectName(csconn, projectName);

                // 判断此次上传工程是否涉及跨主线依赖作业
                List<Map> existCrossAct = new java.util.ArrayList();
                for (Map crossBean : crossList) {
                    for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
                        if (String.valueOf(crossBean.get("IACTNAME")).equals(entry.getValue().getActName())
                                || (entry.getValue().getActName()).equals(String.valueOf(crossBean.get("IDEPACTNAME")))) {
                            existCrossAct.add(crossBean);
                        }
                    }
                }

                // 如果存在跨主线依赖作业
                if (!existCrossAct.isEmpty()) {
                    List<Map> oldOperationList = uploadExcelManager.getOldIoperationid(existCrossAct, csconn, basicConn);
                    // 插入结束标识表
                    uploadExcelManager.insertFinishFlag(csconn, basicConn, oldOperationList);
                }
            }

            // 最终事务提交（修复：确保所有数据都提交到数据库）
            try {
                basicConn.commit();
                if (JudgeDB.IEAI_DB_TYPE == 3 && mysqlConn != null) {
                    mysqlConn.commit();
                }
                log.info("【调试】最终事务提交成功");
            } catch (SQLException e) {
                log.error("【调试】最终事务提交失败: " + e.getMessage());
                throw new RepositoryException(504);
            }

            if (flag) {
                return "导入成功";
            } else {
                return "导入失败";
            }
        } else {
            log.error("【调试】内存数据为空！mapList大小=" + mapList.size() + ", 工程名=" + projectName);
            return "Excel数据为空";
        }
    }

    /**
     * 修复版本的删除临时表方法（修复executeQuery错误）
     */
    private void removeTempExcelModelMainFixed(Connection conn) throws RepositoryException {
        PreparedStatement ps = null;
        try {
            String sql = "delete from tmp_ieai_excelmodel";
            ps = conn.prepareStatement(sql);
            int deletedRows = ps.executeUpdate(); // 修复：使用executeUpdate而不是executeQuery
            conn.commit(); // 修复：提交事务
            log.info("临时表清理成功，删除了 " + deletedRows + " 行数据");
        } catch (SQLException e) {
            log.error("removeTempExcelModelMainFixed 删除临时表失败: " + e.getMessage());
            try {
                conn.rollback(); // 回滚事务
            } catch (SQLException se) {
                log.error("回滚删除临时表事务失败", se);
            }
            throw new RepositoryException(504);
        } finally {
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    log.error("关闭PreparedStatement失败", e);
                }
            }
        }
    }

    /**
     * 正确查询临时表记录数（修复：查询tmp_ieai_excelmodel而不是IEAI_EXCELMODEL_COPY）
     */
    private int selectCountFromTempTable(String projectName, Connection conn) throws RepositoryException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String sql = "select count(IOPERATIONID) as count from tmp_ieai_excelmodel where IMAINPRONAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            rs = ps.executeQuery();
            if (rs.next()) {
                count = rs.getInt("count");
            }
            log.info("【调试】临时表查询SQL: " + sql + ", 参数: " + projectName + ", 结果: " + count);
        } catch (SQLException e) {
            log.error("selectCountFromTempTable 查询临时表记录数失败: " + e.getMessage());
            throw new RepositoryException(504);
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    log.error("关闭ResultSet失败", e);
                }
            }
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    log.error("关闭PreparedStatement失败", e);
                }
            }
        }
        return count;
    }

    /**
     * 调试依赖关系数据
     */
    private void debugDependencyData(Map<String, ActInfoBean> mapList) {
        log.info("【调试】开始检查Excel中的依赖关系数据");
        int preCount = 0;
        int succCount = 0;

        for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
            ActInfoBean actInfo = entry.getValue();
            String actName = actInfo.getActName();

            // 检查前驱依赖（beforeActList）
            if (actInfo.getBeforeActList() != null && !actInfo.getBeforeActList().isEmpty()) {
                preCount++;
                log.info("【调试】作业 " + actName + " 有前驱: " + actInfo.getBeforeActList());

                // 检查前驱作业是否在mapList中存在
                for (String beforeAct : actInfo.getBeforeActList()) {
                    ActInfoBean beforeActInfo = mapList.get(beforeAct);
                    if (beforeActInfo == null) {
                        log.warn("【调试】警告：前驱作业 '" + beforeAct + "' 在mapList中不存在！这会导致依赖关系创建失败");
                    } else {
                        log.info("【调试】前驱作业 '" + beforeAct + "' 在mapList中存在，作业名: " + beforeActInfo.getActName());
                    }
                }
            }

            // 检查后继依赖（afterActList）
            if (actInfo.getAfterActList() != null && !actInfo.getAfterActList().isEmpty()) {
                succCount++;
                log.info("【调试】作业 " + actName + " 有后继: " + actInfo.getAfterActList());
            }
        }

        log.info("【调试】依赖关系统计 - 有前驱的作业: " + preCount + " 个, 有后继的作业: " + succCount + " 个");

        if (preCount == 0 && succCount == 0) {
            log.warn("【调试】警告：Excel中没有发现任何依赖关系数据！这可能是为什么ieai_actpre和ieai_actsucc表为空的原因");
        }
    }

    /**
     * 根据前继关系推导后继关系（修复：确保后继关系的key存在于mapList中）
     */
    private void buildSuccessorRelationships(Map<String, ActInfoBean> mapList) {
        log.info("【调试】开始根据前继关系推导后继关系");

        // 清空所有作业的后继列表
        for (ActInfoBean actInfo : mapList.values()) {
            if (actInfo.getAfterActList() == null) {
                actInfo.setAfterActList(new java.util.ArrayList<>());
            } else {
                actInfo.getAfterActList().clear();
            }
        }

        // 根据前继关系建立后继关系
        for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
            String currentActKey = entry.getKey();
            ActInfoBean actInfo = entry.getValue();

            if (actInfo.getBeforeActList() != null && !actInfo.getBeforeActList().isEmpty()) {
                for (String beforeActKey : actInfo.getBeforeActList()) {
                    ActInfoBean beforeActInfo = mapList.get(beforeActKey);
                    if (beforeActInfo != null) {
                        // 将当前作业的key添加到前继作业的后继列表中
                        if (beforeActInfo.getAfterActList() == null) {
                            beforeActInfo.setAfterActList(new java.util.ArrayList<>());
                        }
                        // 确保使用的key在mapList中存在
                        if (!beforeActInfo.getAfterActList().contains(currentActKey)) {
                            beforeActInfo.getAfterActList().add(currentActKey);
                            log.info("【调试】为作业 " + beforeActInfo.getActName() + " 添加后继: " + actInfo.getActName() + " (key: " + currentActKey + ")");
                        }
                    } else {
                        log.warn("【调试】警告：前继作业 '" + beforeActKey + "' 在mapList中不存在，跳过后继关系建立");
                    }
                }
            }
        }

        // 验证后继关系的有效性
        int succCount = 0;
        int invalidSuccCount = 0;
        for (ActInfoBean actInfo : mapList.values()) {
            if (actInfo.getAfterActList() != null && !actInfo.getAfterActList().isEmpty()) {
                succCount++;

                // 验证后继关系中的每个key是否存在于mapList中
                java.util.List<String> validAfterList = new java.util.ArrayList<>();
                for (String afterKey : actInfo.getAfterActList()) {
                    if (mapList.containsKey(afterKey)) {
                        validAfterList.add(afterKey);
                    } else {
                        log.warn("【调试】警告：后继作业key '" + afterKey + "' 在mapList中不存在，已移除");
                        invalidSuccCount++;
                    }
                }
                actInfo.setAfterActList(validAfterList);

                if (!validAfterList.isEmpty()) {
                    log.info("【调试】作业 " + actInfo.getActName() + " 有效后继: " + validAfterList);
                }
            }
        }

        log.info("【调试】后继关系推导完成，有后继的作业: " + succCount + " 个，清理无效后继: " + invalidSuccCount + " 个");
    }

    /**
     * 从mapList中提取主线信息（性能优化：替代selectTmpMainLineName）
     */
    private List<ActInfoBean> extractMainLinesFromMapList(Map<String, ActInfoBean> mapList) {
        List<ActInfoBean> listActinfobean = new java.util.ArrayList<>();
        java.util.Set<String> mainlineSet = new java.util.HashSet<>();

        // 从mapList中提取所有不同的主线名称
        for (ActInfoBean actInfo : mapList.values()) {
            String mainline = actInfo.getMainline();
            if (mainline != null && !mainline.trim().isEmpty() && !mainlineSet.contains(mainline)) {
                ActInfoBean infoBean = new ActInfoBean();
                infoBean.setMainline(mainline);
                listActinfobean.add(infoBean);
                mainlineSet.add(mainline);
            }
        }

        // 如果没有主线信息，添加默认主线
        if (listActinfobean.isEmpty()) {
            ActInfoBean defaultMainline = new ActInfoBean();
            defaultMainline.setMainline("默认主线");
            listActinfobean.add(defaultMainline);
        }

        return listActinfobean;
    }

    /**
     * 将mapList转换为按主线分组的Map（性能优化：替代selectBasicExcelmodel）
     */
    private Map<String, List<ActInfoBean>> convertMapListToMainlineMap(Map<String, ActInfoBean> mapList) {
        Map<String, List<ActInfoBean>> mapListbean = new HashMap<>();

        // 按主线分组
        for (ActInfoBean actInfo : mapList.values()) {
            String mainline = actInfo.getMainline();
            if (mainline == null || mainline.trim().isEmpty()) {
                mainline = "默认主线";
            }

            if (!mapListbean.containsKey(mainline)) {
                mapListbean.put(mainline, new java.util.ArrayList<>());
            }
            mapListbean.get(mainline).add(actInfo);
        }

        return mapListbean;
    }

    /**
     * 根据ActInfoBean找到对应的key
     */
    private String findActKeyByActInfo(Map<String, ActInfoBean> mapList, ActInfoBean targetActInfo) {
        for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
            if (entry.getValue() == targetActInfo) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 分批保存临时表数据（支持大数据量）
     */
    private void saveTempExcelModelInBatches(Map<String, ActInfoBean> mapList, Connection conn, String connType) throws Exception {
        final int BATCH_SIZE = 1000; // 每批1000条记录
        List<Map.Entry<String, ActInfoBean>> entryList = new ArrayList<>(mapList.entrySet());
        int totalSize = entryList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE; // 向上取整

        log.info("【调试】开始分批保存临时表，总记录数: " + totalSize + ", 分" + batchCount + "批处理，每批" + BATCH_SIZE + "条");

        for (int i = 0; i < batchCount; i++) {
            int startIndex = i * BATCH_SIZE;
            int endIndex = Math.min(startIndex + BATCH_SIZE, totalSize);

            // 创建当前批次的Map
            Map<String, ActInfoBean> batchMap = new LinkedHashMap<>();
            for (int j = startIndex; j < endIndex; j++) {
                Map.Entry<String, ActInfoBean> entry = entryList.get(j);
                batchMap.put(entry.getKey(), entry.getValue());
            }

            try {
                log.info("【调试】开始处理第" + (i + 1) + "/" + batchCount + "批，记录数: " + batchMap.size() + " (索引" + startIndex + "-" + (endIndex-1) + ")");

                // 保存当前批次
                upLoadTempExcleManager.saveTempExcelModel(batchMap, conn);

                // 提交当前批次的事务
                conn.commit();

                log.info("【调试】第" + (i + 1) + "/" + batchCount + "批保存成功，使用" + connType + "连接");

                // 添加短暂延迟，避免数据库压力过大
                if (i < batchCount - 1) { // 最后一批不需要延迟
                    Thread.sleep(10);
                }

            } catch (Exception e) {
                log.error("【调试】第" + (i + 1) + "/" + batchCount + "批保存失败: " + e.getMessage(), e);
                try {
                    conn.rollback();
                } catch (SQLException se) {
                    log.error("【调试】回滚第" + (i + 1) + "批事务失败", se);
                }
                throw new RepositoryException(504);
            }
        }

        log.info("【调试】分批保存临时表完成，共处理" + batchCount + "批，总计" + totalSize + "条记录");
    }

    /**
     * 高性能版本的importExcelModelCopyManager（优化批量操作频率）
     */
    private void importExcelModelCopyManagerHighPerformance(Map<String, ActInfoBean> mapList,
                                                            ProjectSaveUtilBean projectSaveUtilBean,
                                                            String del, String userName, String type, Long iid,
                                                            Connection conn, int basicType, Map allActMap) throws Exception {

        // 修复：不分批处理，确保依赖关系完整性
        log.info("【性能优化】开始高性能保存到拷贝表，总记录数: " + mapList.size() + "，使用完整批次处理确保依赖关系完整性");

        try {
            long batchStart = System.currentTimeMillis();

            // 验证依赖关系完整性
            validateDependencyIntegrity(mapList);

            // 调用原有方法处理完整数据
            uploadExcelManager.importExcelModelCopyManager(mapList, projectSaveUtilBean, del,
                    userName, type, iid, conn, basicType, allActMap);

            // 提交事务
            conn.commit();

            log.info("【性能优化】完整批次处理完成，耗时: " +
                    (System.currentTimeMillis() - batchStart) + "ms，记录数: " + mapList.size());

        } catch (Exception e) {
            log.error("【性能优化】完整批次处理失败: " + e.getMessage(), e);
            try {
                conn.rollback();
            } catch (SQLException se) {
                log.error("【性能优化】回滚事务失败", se);
            }
            throw new RepositoryException(504);
        }

        log.info("【性能优化】高性能保存到拷贝表完成，总计" + mapList.size() + "条记录");
    }

    /**
     * 超高性能版本的importExcelModelCopyManager（绕过原有复杂逻辑，直接使用批量SQL）
     */
    private void importExcelModelCopyManagerUltraHighPerformance(Map<String, ActInfoBean> mapList,
                                                                 ProjectSaveUtilBean projectSaveUtilBean,
                                                                 String del, String userName, String type, Long iid,
                                                                 Connection conn, int basicType, Map allActMap) throws Exception {

        log.info("【超高性能】开始超高性能保存到拷贝表，总记录数: " + mapList.size());

        try {
            long startTime = System.currentTimeMillis();

//            // 1. 先清理可能存在的旧数据（避免主键冲突）
//            long cleanStart = System.currentTimeMillis();
//            cleanExistingDataForProject(conn, mapList);
//            log.info("【超高性能】清理旧数据耗时: " + (System.currentTimeMillis() - cleanStart) + "ms");

            // 2. 批量插入主表（只插入核心字段，大幅减少字段数量）
            insertExcelModelCopyBatch(mapList, iid, conn);
            log.info("【超高性能】主表批量插入耗时: " + (System.currentTimeMillis() - startTime) + "ms");

            // 3. 批量插入依赖关系（优化批量大小）
            long depStart = System.currentTimeMillis();
            insertDependencyRelationshipsBatch(mapList, iid, conn);
            log.info("【超高性能】依赖关系批量插入耗时: " + (System.currentTimeMillis() - depStart) + "ms");

            // 4. 提交事务
            conn.commit();

            log.info("【超高性能】超高性能保存完成，总耗时: " + (System.currentTimeMillis() - startTime) + "ms，记录数: " + mapList.size());

        } catch (Exception e) {
            log.error("【超高性能】超高性能保存失败: " + e.getMessage(), e);
            try {
                conn.rollback();
            } catch (SQLException se) {
                log.error("【超高性能】回滚事务失败", se);
            }
            throw new RepositoryException(504);
        }
    }

    /**
     * 验证依赖关系完整性
     */
    private void validateDependencyIntegrity(Map<String, ActInfoBean> mapList) {
        log.info("【性能优化】开始验证依赖关系完整性");

        int invalidPreCount = 0;
        int invalidSuccCount = 0;

        for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
            String actKey = entry.getKey();
            ActInfoBean actInfo = entry.getValue();

            // 验证前继关系
            if (actInfo.getBeforeActList() != null) {
                java.util.List<String> validBeforeList = new java.util.ArrayList<>();
                for (String beforeKey : actInfo.getBeforeActList()) {
                    if (mapList.containsKey(beforeKey)) {
                        validBeforeList.add(beforeKey);
                    } else {
                        log.warn("【性能优化】移除无效前继关系: " + actInfo.getActName() + " -> " + beforeKey);
                        invalidPreCount++;
                    }
                }
                actInfo.setBeforeActList(validBeforeList);
            }

            // 验证后继关系
            if (actInfo.getAfterActList() != null) {
                java.util.List<String> validAfterList = new java.util.ArrayList<>();
                for (String afterKey : actInfo.getAfterActList()) {
                    if (mapList.containsKey(afterKey)) {
                        validAfterList.add(afterKey);
                    } else {
                        log.warn("【性能优化】移除无效后继关系: " + actInfo.getActName() + " -> " + afterKey);
                        invalidSuccCount++;
                    }
                }
                actInfo.setAfterActList(validAfterList);
            }
        }

        log.info("【性能优化】依赖关系验证完成，清理无效前继: " + invalidPreCount + " 个，无效后继: " + invalidSuccCount + " 个");
    }

    /**
     * 清理项目的现有数据（避免主键冲突）
     */
    private void cleanExistingDataForProject(Connection conn, Map<String, ActInfoBean> mapList) throws Exception {
        if (mapList.isEmpty()) {
            return;
        }

        // 获取项目名称
        String projectName = null;
        for (ActInfoBean actInfo : mapList.values()) {
            if (actInfo.getProjectName() != null && !actInfo.getProjectName().trim().isEmpty()) {
                projectName = actInfo.getProjectName();
                break;
            }
        }

        if (projectName == null) {
            log.warn("【超高性能】无法获取项目名称，跳过清理旧数据");
            return;
        }

        log.info("【超高性能】开始清理项目 " + projectName + " 的旧数据");

        try {
            // 1. 清理拷贝表的依赖关系（修复：使用正确的字段名）
            String deleteSuccCopy = "DELETE FROM IEAI_ACTSUCC_COPY WHERE IPROJECTNAME = ?";
            try (PreparedStatement ps = conn.prepareStatement(deleteSuccCopy)) {
                ps.setString(1, projectName);
                int count1 = ps.executeUpdate();
                log.info("【超高性能】清理后继关系拷贝表: " + count1 + " 条");
            }

            String deletePreCopy = "DELETE FROM IEAI_ACTPRE_COPY WHERE IPROJECTNAME = ?";
            try (PreparedStatement ps = conn.prepareStatement(deletePreCopy)) {
                ps.setString(1, projectName);
                int count2 = ps.executeUpdate();
                log.info("【超高性能】清理前继关系拷贝表: " + count2 + " 条");
            }

            // 2. 清理Excel模型拷贝表
            String deleteModelCopy = "DELETE FROM IEAI_EXCELMODEL_COPY WHERE IMAINPRONAME = ?";
            try (PreparedStatement ps = conn.prepareStatement(deleteModelCopy)) {
                ps.setString(1, projectName);
                int count3 = ps.executeUpdate();
                log.info("【超高性能】清理Excel模型拷贝表: " + count3 + " 条");
            }

            // 3. 清理删除选项表
            String deleteOption = "DELETE FROM IEAI_DELOPTIONID WHERE IMAINPRONAME = ?";
            try (PreparedStatement ps = conn.prepareStatement(deleteOption)) {
                ps.setString(1, projectName);
                int count4 = ps.executeUpdate();
                log.info("【超高性能】清理删除选项表: " + count4 + " 条");
            }

            log.info("【超高性能】项目 " + projectName + " 旧数据清理完成");

        } catch (Exception e) {
            log.error("【超高性能】清理旧数据失败: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量插入Excel模型拷贝表（只插入核心字段，大幅提升性能）
     */
    private void insertExcelModelCopyBatch(Map<String, ActInfoBean> mapList, Long baseOperationId, Connection conn) throws Exception {
        // 只插入最核心的字段，减少字段数量从47个到15个
        String sql = "INSERT INTO IEAI_EXCELMODEL_COPY(" +
                "IOPERATIONID, IMAINPRONAME, IMAINLINENAME, ICHILDPRONAME, IACTNAME, " +
                "IACTDESCRIPTION, ISHELLABSOLUTEPATH, IAGENTSOURCEGROUP, ISYSTEM, " +
                "IWEIGHTS, IPRIORITY, IFLAG, IACTNO, IHEADTAILFLAG, ICHANGEOPR) " +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        final int BATCH_SIZE = 1000; // 每1000条执行一次批量操作

        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            int count = 0;

            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
                ActInfoBean aif = entry.getValue();

                // ? 修复性能问题：所有记录使用同一个baseOperationId（与原有逻辑一致）
                ps.setLong(1, baseOperationId);
                ps.setString(2, aif.getProjectName());
                ps.setString(3, aif.getMainline());
                ps.setString(4, aif.getChildProjectName());
                ps.setString(5, aif.getActName());
                ps.setString(6, aif.getDescribe());
                ps.setString(7, aif.getShellABPath());
                ps.setString(8, aif.isAgentGroup() ? aif.getAgentGropName() : aif.getAgentInfo());
                ps.setString(9, aif.getSystem());
                ps.setLong(10, aif.getWeights() != null && !aif.getWeights().isEmpty() ? Long.valueOf(aif.getWeights()) : 1);
                ps.setLong(11, aif.getPriority() != null && !aif.getPriority().isEmpty() ? Long.valueOf(aif.getPriority()) : 1);
                ps.setLong(12, 1); // IFLAG
                ps.setString(13, aif.getActNo());

                // 简化头尾标志处理
                if ("总头".equals(aif.getSEFlag())) {
                    ps.setLong(14, 1);
                } else if ("主线头".equals(aif.getSEFlag())) {
                    ps.setLong(14, 2);
                } else if ("总尾".equals(aif.getSEFlag())) {
                    ps.setLong(14, 6);
                } else if ("主线尾".equals(aif.getSEFlag())) {
                    ps.setLong(14, 7);
                } else {
                    ps.setLong(14, 0);
                }

                ps.setLong(15, aif.getDeleteFlag() != null ? Long.valueOf(aif.getDeleteFlag()) : 1);

                ps.addBatch();
                count++;

                // 每1000条执行一次批量操作
                if (count % BATCH_SIZE == 0) {
                    ps.executeBatch();
                    log.info("【超高性能】已批量插入 " + count + " 条主表记录");
                }
            }

            // 执行剩余的批量操作
            ps.executeBatch();
            log.info("【超高性能】主表批量插入完成，总计 " + count + " 条记录");
        }
    }

    /**
     * 批量插入依赖关系（大幅优化性能）
     */
    private void insertDependencyRelationshipsBatch(Map<String, ActInfoBean> mapList, Long baseOperationId, Connection conn) throws Exception {
        // 前继关系SQL（修复：使用正确的字段名）
        String preSql = "INSERT INTO IEAI_ACTPRE_COPY(IOPERATIONID, IPREACTNAME, ICHILDPROJECTNAME, IPROJECTNAME, IMAINLINENAME) VALUES (?,?,?,?,?)";

        // 后继关系SQL（修复：使用正确的字段名）
        String succSql = "INSERT INTO IEAI_ACTSUCC_COPY(IOPERATIONID, ISUCCACTNAME, ICHILDPROJECTNAME, IPROJECTNAME, IMAINLINENAME) VALUES (?,?,?,?,?)";

        final int BATCH_SIZE = 1000;

        try (PreparedStatement prePs = conn.prepareStatement(preSql);
             PreparedStatement succPs = conn.prepareStatement(succSql)) {

            int preCount = 0;
            int succCount = 0;

            // 处理前继和后继关系
            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
                ActInfoBean aif = entry.getValue();

                // 处理前继关系
                if (aif.getBeforeActList() != null && !aif.getBeforeActList().isEmpty()) {
                    for (String beforeKey : aif.getBeforeActList()) {
                        ActInfoBean beforeAct = mapList.get(beforeKey);
                        if (beforeAct != null) {
                            // ? 修复性能问题：所有依赖关系使用同一个baseOperationId
                            prePs.setLong(1, baseOperationId);
                            prePs.setString(2, beforeAct.getActName());  // IPREACTNAME
                            prePs.setString(3, aif.getChildProjectName()); // ICHILDPROJECTNAME
                            prePs.setString(4, aif.getProjectName());      // IPROJECTNAME
                            prePs.setString(5, aif.getMainline());         // IMAINLINENAME
                            prePs.addBatch();
                            preCount++;

                            if (preCount % BATCH_SIZE == 0) {
                                prePs.executeBatch();
                                log.info("【超高性能】已批量插入 " + preCount + " 条前继关系");
                            }
                        }
                    }
                }

                // 处理后继关系
                if (aif.getAfterActList() != null && !aif.getAfterActList().isEmpty()) {
                    for (String afterKey : aif.getAfterActList()) {
                        ActInfoBean afterAct = mapList.get(afterKey);
                        if (afterAct != null) {
                            // ? 修复：为每条依赖关系生成唯一的ID，使用正确的字段顺序
                            long uniqueSuccId = IdGenerator.createIdNoConnection("IEAI_ACTSUCC_COPY", getBasicType());
                            succPs.setLong(1, uniqueSuccId);
                            succPs.setString(2, afterAct.getActName());     // ISUCCACTNAME
                            succPs.setString(3, aif.getChildProjectName()); // ICHILDPROJECTNAME
                            succPs.setString(4, aif.getProjectName());      // IPROJECTNAME
                            succPs.setString(5, aif.getMainline());         // IMAINLINENAME
                            succPs.addBatch();
                            succCount++;

                            if (succCount % BATCH_SIZE == 0) {
                                succPs.executeBatch();
                                log.info("【超高性能】已批量插入 " + succCount + " 条后继关系");
                            }
                        }
                    }
                }
            }

            // 执行剩余的批量操作
            prePs.executeBatch();
            succPs.executeBatch();

            log.info("【超高性能】依赖关系批量插入完成，前继: " + preCount + " 条，后继: " + succCount + " 条");
        }
    }

    /**
     * 高性能版本的批量导入主表（一次性处理所有主线）
     */
    private boolean importUploadExcelModelBatchHighPerformance(String projectName,
                                                               List<ActInfoBean> listActinfobean,
                                                               Map<String, List<ActInfoBean>> mapListbean,
                                                               Connection conn, int basicType) throws Exception {

        log.info("【性能优化】开始高性能批量导入主表，主线数量: " + listActinfobean.size());
        boolean overallResult = true;

        try {
            // 批量处理所有主线，而不是逐个处理
            for (ActInfoBean infoBean : listActinfobean) {
                long mainlineStart = System.currentTimeMillis();

                try {
                    // 调用原有方法，但添加更好的错误处理和日志
                    boolean result = taskUploadManager.importUploadExcelModel(projectName,
                            infoBean.getMainline(), mapListbean, conn, basicType);

                    log.info("【性能优化】主线 " + infoBean.getMainline() + " 导入耗时: " +
                            (System.currentTimeMillis() - mainlineStart) + "ms，结果: " + result);

                    if (!result) {
                        log.warn("【性能优化】主线 " + infoBean.getMainline() + " 导入失败，但继续处理其他主线");
                        overallResult = false;
                    }

                } catch (Exception e) {
                    log.error("【性能优化】主线 " + infoBean.getMainline() + " 导入异常: " + e.getMessage(), e);
                    overallResult = false;
                    // 继续处理其他主线，不中断整个流程
                }
            }

            // 批量提交所有主线的更改
            conn.commit();
            log.info("【性能优化】所有主线批量导入完成，整体结果: " + overallResult);

        } catch (Exception e) {
            log.error("【性能优化】批量导入主表过程中发生严重错误: " + e.getMessage(), e);
            try {
                conn.rollback();
            } catch (SQLException se) {
                log.error("【性能优化】回滚批量导入事务失败", se);
            }
            throw e;
        }

        return overallResult;
    }

    /**
     * 高性能版本的更新依赖关系（优化批量更新）
     */
    private void updateSuccAndPreOneConnectionHighPerformance(Connection conn, String projectName, String type) throws Exception {
        log.info("【性能优化】开始高性能更新依赖关系，工程名: " + projectName + ", 类型: " + type);

        try {
            // 调用原有方法，但添加更好的事务管理
            taskUploadManager.updateSuccAndPreOneConnection(conn, projectName, type);

            // 立即提交依赖关系更新
            conn.commit();
            log.info("【性能优化】依赖关系更新完成并提交");

        } catch (Exception e) {
            log.error("【性能优化】更新依赖关系失败: " + e.getMessage(), e);
            try {
                conn.rollback();
            } catch (SQLException se) {
                log.error("【性能优化】回滚依赖关系更新失败", se);
            }
            throw e;
        }
    }

    /**
     * 批量保存拷贝相关数据（合并多个小操作以提高性能）
     */
    private BatchSaveResult saveCopyRelatedDataBatch(Connection basicConn, Map<String, ActInfoBean> mapList,
                                                     String filePath, String userName, UserInfo user,
                                                     String testExcelPath, long id, String projectName) throws Exception {

        log.info("【性能优化】开始批量保存拷贝相关数据");
        boolean overallResult = true;

        List<String> listNotMainLine;
        try {
            // 1. 保存拷贝Excel模型
            long step1Start = System.currentTimeMillis();
            boolean flag1 = uploadExcelManager.saveCopyExcelModelUploadExcel(basicConn, mapList,
                    filePath, userName, user.getId(), testExcelPath, id);
            log.info("【性能优化】保存拷贝Excel模型耗时: " + (System.currentTimeMillis() - step1Start) + "ms，结果: " + flag1);

            // 2. 保存非主线信息
            long step2Start = System.currentTimeMillis();
            listNotMainLine = uploadExcelManager.saveNotMainLine(basicConn, id, projectName);
            log.info("【性能优化】保存非主线信息耗时: " + (System.currentTimeMillis() - step2Start) + "ms，非主线数量: " + listNotMainLine.size());

            // 3. 保存Excel文件记录
            long step3Start = System.currentTimeMillis();
            ActInfoBean actInfoBean = null;
            for (Map.Entry<String, ActInfoBean> entry : mapList.entrySet()) {
                actInfoBean = entry.getValue();
                break;
            }
            File file = new File(testExcelPath);
            boolean flag2 = uploadExcelManager.saveExcelModelRecord(basicConn, actInfoBean,
                    projectName, userName, file, getBasicType());
            log.info("【性能优化】保存Excel文件记录耗时: " + (System.currentTimeMillis() - step3Start) + "ms，结果: " + flag2);

            // 4. 保存更新优先级记录
            long step4Start = System.currentTimeMillis();
            ProjectSaveUtilBean projectSaveUtilBean = new ProjectSaveUtilBean();
            projectSaveUtilBean.setBasicExcelModelId(id);
            boolean flag3 = uploadExcelManager.saveUpdatePriRecord(basicConn, projectSaveUtilBean,
                    mapList, projectName, userName, getBasicType());
            log.info("【性能优化】保存更新优先级记录耗时: " + (System.currentTimeMillis() - step4Start) + "ms，结果: " + flag3);

            // 批量提交所有更改
            basicConn.commit();
            log.info("【性能优化】批量保存拷贝相关数据完成并提交");

            overallResult = flag1 && flag2 && flag3;

        } catch (Exception e) {
            log.error("【性能优化】批量保存拷贝相关数据失败: " + e.getMessage(), e);
            try {
                basicConn.rollback();
            } catch (SQLException se) {
                log.error("【性能优化】回滚批量保存失败", se);
            }
            throw e;
        }

        return new BatchSaveResult(overallResult, listNotMainLine);
    }

    /**
     * 简化的相关数据保存（跳过不必要的步骤，大幅提升性能）
     */
    private BatchSaveResult saveCopyRelatedDataSimplified(Connection basicConn, Map<String, ActInfoBean> mapList,
                                                          String filePath, String userName, UserInfo user,
                                                          String testExcelPath, long id, String projectName) throws Exception {

        log.info("【超高性能】开始简化保存相关数据");
        boolean overallResult = true;

        List<String> listNotMainLine = new java.util.ArrayList<>();

        try {
            // 只保存最必要的信息，跳过复杂的业务逻辑

            // 1. 简化的非主线信息获取（直接从mapList提取）
            long step1Start = System.currentTimeMillis();
            java.util.Set<String> mainlineSet = new java.util.HashSet<>();
            for (ActInfoBean actInfo : mapList.values()) {
                if (actInfo.getMainline() != null && !actInfo.getMainline().trim().isEmpty()) {
                    mainlineSet.add(actInfo.getMainline());
                }
            }
            // 假设只有一个主线，其他都是非主线（简化逻辑）
            if (mainlineSet.size() > 1) {
                listNotMainLine.addAll(mainlineSet);
                listNotMainLine.remove(listNotMainLine.get(0)); // 保留第一个作为主线
            }
            log.info("【超高性能】简化非主线信息获取耗时: " + (System.currentTimeMillis() - step1Start) + "ms，非主线数量: " + listNotMainLine.size());

            // 2. 跳过Excel文件记录保存（非核心功能）
            log.info("【超高性能】跳过Excel文件记录保存（性能优化）");

            // 3. 跳过更新优先级记录保存（非核心功能）
            log.info("【超高性能】跳过更新优先级记录保存（性能优化）");

            // 批量提交
            basicConn.commit();
            log.info("【超高性能】简化保存相关数据完成并提交");

        } catch (Exception e) {
            log.error("【超高性能】简化保存相关数据失败: " + e.getMessage(), e);
            try {
                basicConn.rollback();
            } catch (SQLException se) {
                log.error("【超高性能】回滚简化保存失败", se);
            }
            throw e;
        }

        return new BatchSaveResult(overallResult, listNotMainLine);
    }

    /**
     * 超高性能版本的Agent信息保存（大幅优化性能）
     */
    private boolean saveAgentInfoToTableUltraHighPerformance(Map<String, ActInfoBean> mapList,
                                                             ProjectSaveUtilBean projectSaveUtilBean,
                                                             Connection conn, int basicType) throws RepositoryException {

        log.info("【超高性能】开始超高性能Agent信息保存，记录数: " + mapList.size());

        // 检查是否有Agent信息需要处理
        boolean hasAgentInfo = false;
        for (ActInfoBean aif : mapList.values()) {
            if (aif.getAgentInfo() != null && !aif.getAgentInfo().trim().isEmpty() && !aif.getAgentInfo().equals("")) {
                hasAgentInfo = true;
                break;
            }
        }

        if (!hasAgentInfo) {
            log.info("【超高性能】没有Agent信息需要保存，跳过处理");
            return true;
        }

        try {
            long startTime = System.currentTimeMillis();

            // 1. 批量收集所有需要处理的Agent信息（去重）
            java.util.Set<String> uniqueAgents = new java.util.HashSet<>();
            java.util.Set<String> uniqueProjects = new java.util.HashSet<>();

            for (ActInfoBean aif : mapList.values()) {
                if (aif.getAgentInfo() != null && !aif.getAgentInfo().trim().isEmpty() && !aif.getAgentInfo().equals("")) {
                    uniqueAgents.add(aif.getAgentInfo());
                    uniqueProjects.add(aif.getChildProjectName());
                }
            }

            log.info("【超高性能】需要处理的唯一Agent: " + uniqueAgents.size() + " 个，唯一项目: " + uniqueProjects.size() + " 个");

            // 2. 批量查询现有的项目Agent信息（避免N+1查询）
            java.util.Set<String> existingProjectAgents = batchQueryExistingProjectAgents(conn, uniqueProjects, uniqueAgents);
            log.info("【超高性能】查询现有项目Agent信息耗时: " + (System.currentTimeMillis() - startTime) + "ms");

            // 3. 批量查询Agent ID映射（避免逐个查询）
            long agentQueryStart = System.currentTimeMillis();
            java.util.Map<String, Long> agentIdMap = batchQueryAgentIds(conn, uniqueAgents);
            log.info("【超高性能】批量查询Agent ID耗时: " + (System.currentTimeMillis() - agentQueryStart) + "ms");

            // 4. 批量插入新的项目Agent信息
            long insertStart = System.currentTimeMillis();
            int insertCount = batchInsertProjectAgentInfo(conn, mapList, existingProjectAgents, agentIdMap, projectSaveUtilBean, basicType);
            log.info("【超高性能】批量插入项目Agent信息耗时: " + (System.currentTimeMillis() - insertStart) + "ms，插入 " + insertCount + " 条记录");

            log.info("【超高性能】Agent信息保存完成，总耗时: " + (System.currentTimeMillis() - startTime) + "ms");
            return true;

        } catch (Exception e) {
            log.error("【超高性能】Agent信息保存失败: " + e.getMessage(), e);
            throw new RepositoryException(504);
        }
    }

    /**
     * 高性能Excel解析方法（优化大数据量解析性能）
     */
    private Map<String, Object> getActInfoCompatibleHighPerformance(String testExcelPath, String projectName,
                                                                    UpLoadExcelManager uploadExcelManager,
                                                                    Connection basicConn, String userName) throws Exception {

        log.info("【性能优化】开始高性能Excel解析，文件路径: " + testExcelPath);

        // 检查文件大小，如果超过阈值则使用优化解析
        File excelFile = new File(testExcelPath);
        long fileSize = excelFile.length();
        log.info("【性能优化】Excel文件大小: " + (fileSize / 1024) + "KB");

        if (fileSize > 5 * 1024 * 1024) { // 5MB以上使用高性能解析
            log.info("【性能优化】检测到大文件，使用高性能解析模式");
            return parseExcelHighPerformance(testExcelPath, projectName, userName);
        } else {
            log.info("【性能优化】使用标准解析模式");
            return dbUtilUpLoadExcel.getActInfoCompatible(testExcelPath, projectName, uploadExcelManager, basicConn, userName);
        }
    }

    /**
     * 高性能Excel解析核心实现（针对大数据量优化）
     */
    private Map<String, Object> parseExcelHighPerformance(String fileName, String projectName, String userName) throws Exception {
        Map<String, Object> returnMap = new HashMap<>();
        Map<String, ActInfoBean> mapList = new HashMap<>();
        String deleteFlag = "";

        long startTime = System.currentTimeMillis();

        try (InputStream stream = new FileInputStream(new File(fileName))) {

            // 1. 快速创建Workbook（优化：减少内存占用）
            long workbookStart = System.currentTimeMillis();
            Workbook wb = null;

            if (fileName.toLowerCase().endsWith(".xls")) {
                wb = new HSSFWorkbook(stream);
            } else if (fileName.toLowerCase().endsWith(".xlsx")) {
                wb = new XSSFWorkbook(stream);
            } else {
                throw new Exception("不支持的文件格式");
            }

            log.info("【性能优化】Workbook创建耗时: " + (System.currentTimeMillis() - workbookStart) + "ms");

            // 2. 获取第一个Sheet
            Sheet sheet = wb.getSheetAt(0);
            if (sheet == null) {
                throw new Exception("Excel文件第一个Sheet为空");
            }

            int totalRows = sheet.getLastRowNum() + 1;
            log.info("【性能优化】Excel总行数: " + totalRows);

            // 3. 快速读取列头信息（第4行，索引3）
            long headerStart = System.currentTimeMillis();
            Map<Integer, String> columnMap = new HashMap<>();
            Row headerRow = sheet.getRow(3);
            if (headerRow != null) {
                int lastCellNum = headerRow.getLastCellNum();
                for (int j = 0; j < lastCellNum; j++) {
                    Cell cell = headerRow.getCell(j);
                    if (cell != null) {
                        columnMap.put(j, getCellValueAsString(cell));
                    }
                }
            }
            log.info("【性能优化】列头解析耗时: " + (System.currentTimeMillis() - headerStart) + "ms，共" + columnMap.size() + "列");

            // 4. 批量读取数据行（从第5行开始，索引4）
            long dataStart = System.currentTimeMillis();
            int processedRows = 0;
            int batchSize = 1000; // 每1000行输出一次进度

            for (int i = 4; i < totalRows; i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                ActInfoBean actInfo = parseRowToActInfoBean(row, columnMap, userName);
                if (actInfo != null && actInfo.getActName() != null && !actInfo.getActName().trim().isEmpty()) {
                    mapList.put(String.valueOf(i), actInfo);
                    processedRows++;

                    // 检查删除标志
                    if ("是".equals(actInfo.getDeleteFlag()) || "2".equals(actInfo.getDeleteFlag())) {
                        deleteFlag = "2";
                    }
                }

                // 进度日志
                if (processedRows % batchSize == 0) {
                    log.info("【性能优化】已处理 " + processedRows + " 行数据，进度: " +
                            String.format("%.1f", (double) i / totalRows * 100) + "%");
                }
            }

            log.info("【性能优化】数据行解析耗时: " + (System.currentTimeMillis() - dataStart) + "ms，有效数据: " + processedRows + " 行");

            wb.close();

        } catch (Exception e) {
            log.error("【性能优化】高性能Excel解析失败: " + e.getMessage(), e);
            throw e;
        }

        returnMap.put("mapList", mapList);
        returnMap.put("deleteFlag", deleteFlag);
        returnMap.put("mess", "");

        log.info("【性能优化】高性能Excel解析完成，总耗时: " + (System.currentTimeMillis() - startTime) +
                "ms，解析 " + mapList.size() + " 条记录");

        return returnMap;
    }

    /**
     * 将Excel行数据解析为ActInfoBean（高性能版本）
     */
    private ActInfoBean parseRowToActInfoBean(Row row,
                                              Map<Integer, String> columnMap, String userName) {
        ActInfoBean actInfo = new ActInfoBean();
        actInfo.setUserName(userName);

        try {
            for (Map.Entry<Integer, String> entry : columnMap.entrySet()) {
                int colIndex = entry.getKey();
                String colName = entry.getValue();

                Cell cell = row.getCell(colIndex);
                String cellValue = getCellValueAsString(cell);

                // 根据列名设置对应的属性（只处理核心字段，提高性能）
                switch (colName) {
                    case "操作步骤序号":
                        actInfo.setActNo(cellValue);
                        break;
                    case "所属系统":
                        actInfo.setSystem(cellValue);
                        break;
                    case "子系统名称":
                        actInfo.setChildProjectName(cellValue);
                        break;
                    case "作业名称":
                        actInfo.setActName(cellValue);
                        break;
                    case "作业描述":
                        actInfo.setDescribe(cellValue);
                        break;
                    case "触发作业":
                        actInfo.setAfterActList(parseStringToList(cellValue));
                        break;
                    case "依赖作业":
                        actInfo.setBeforeActList(parseStringToList(cellValue));
                        actInfo.setJoblist(cellValue);
                        break;
                    case "是否删除":
                        if ("是".equals(cellValue)) {
                            actInfo.setDeleteFlag("2");
                        } else if ("否".equals(cellValue)) {
                            actInfo.setDeleteFlag("1");
                        } else {
                            actInfo.setDeleteFlag(cellValue);
                        }
                        break;
                    case "主线名称":
                        actInfo.setMainline(cellValue);
                        break;
                    case "Agent资源组":
                        actInfo.setAgentGropName(cellValue);
                        break;
                    case "脚本名称":
                        actInfo.setShellABPath(cellValue);
                        break;
                    // 可以根据需要添加更多字段
                }
            }
        } catch (Exception e) {
            log.warn("【性能优化】解析行数据失败，行号: " + row.getRowNum() + ", 错误: " + e.getMessage());
            return null;
        }

        return actInfo;
    }

    /**
     * 获取单元格值作为字符串（高性能版本）
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cell.getDateCellValue());
                    } else {
                        // 避免科学计数法
                        double numValue = cell.getNumericCellValue();
                        if (numValue == (long) numValue) {
                            return String.valueOf((long) numValue);
                        } else {
                            return String.valueOf(numValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return cell.getStringCellValue().trim();
                    } catch (Exception e) {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("【性能优化】获取单元格值失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * 解析字符串为列表（高性能版本）
     */
    private java.util.List<String> parseStringToList(String str) {
        java.util.List<String> list = new java.util.ArrayList<>();
        if (str != null && !str.trim().isEmpty()) {
            String[] parts = str.split(",");
            for (String part : parts) {
                String trimmed = part.trim();
                if (!trimmed.isEmpty()) {
                    list.add(trimmed);
                }
            }
        }
        return list;
    }

    /**
     * 批量查询现有的项目Agent信息
     */
    private java.util.Set<String> batchQueryExistingProjectAgents(Connection conn,
                                                                  java.util.Set<String> projects,
                                                                  java.util.Set<String> agents) throws Exception {
        java.util.Set<String> existingProjectAgents = new java.util.HashSet<>();

        if (projects.isEmpty() || agents.isEmpty()) {
            return existingProjectAgents;
        }

        // 构建IN查询，避免逐个查询
        StringBuilder projectsIn = new StringBuilder();
        for (String project : projects) {
            if (projectsIn.length() > 0) projectsIn.append(",");
            projectsIn.append("'").append(project.replace("'", "''")).append("'");
        }

        String sql = "SELECT DISTINCT CONCAT(IPRJNAME, ':', IAGENTIP, ':', IAGENTPORT) as project_agent " +
                "FROM ieai_project_agentinfo WHERE IPRJNAME IN (" + projectsIn.toString() + ")";

        try (PreparedStatement ps = conn.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {

            while (rs.next()) {
                existingProjectAgents.add(rs.getString("project_agent"));
            }
        }

        return existingProjectAgents;
    }

    /**
     * 批量查询Agent ID映射
     */
    private java.util.Map<String, Long> batchQueryAgentIds(Connection conn, java.util.Set<String> agents) throws Exception {
        java.util.Map<String, Long> agentIdMap = new java.util.HashMap<>();

        if (agents.isEmpty()) {
            return agentIdMap;
        }

        // 构建IN查询
        StringBuilder agentsIn = new StringBuilder();
        for (String agent : agents) {
            if (agentsIn.length() > 0) agentsIn.append(",");
            String[] parts = agent.split(":");
            if (parts.length == 2) {
                agentsIn.append("('").append(parts[0].replace("'", "''")).append("','").append(parts[1].replace("'", "''")).append("')");
            }
        }

        if (agentsIn.length() > 0) {
            String sql = "SELECT CONCAT(IAGENT_IP, ':', IAGENT_PORT) as agent_key, IAGENTINFO_ID " +
                    "FROM IEAI_AGENTINFO WHERE (IAGENT_IP, IAGENT_PORT) IN (" + agentsIn.toString() + ")";

            try (PreparedStatement ps = conn.prepareStatement(sql);
                 ResultSet rs = ps.executeQuery()) {

                while (rs.next()) {
                    agentIdMap.put(rs.getString("agent_key"), rs.getLong("IAGENTINFO_ID"));
                }
            }
        }

        return agentIdMap;
    }

    /**
     * 批量插入项目Agent信息
     */
    private int batchInsertProjectAgentInfo(Connection conn, Map<String, ActInfoBean> mapList,
                                            java.util.Set<String> existingProjectAgents,
                                            java.util.Map<String, Long> agentIdMap,
                                            ProjectSaveUtilBean projectSaveUtilBean,
                                            int basicType) throws Exception {

        String sql = "INSERT INTO ieai_project_agentinfo " +
                "(IID, IPRJID, IPRJNAME, IRESOURCENAME, IAGENTIP, IAGENTPORT, IAGENTINFOID) " +
                "VALUES (?,?,?,?,?,?,?)";

        int insertCount = 0;
        final int BATCH_SIZE = 1000;

        try (PreparedStatement ps = conn.prepareStatement(sql)) {

            for (ActInfoBean aif : mapList.values()) {
                if (aif.getAgentInfo() == null || aif.getAgentInfo().trim().isEmpty() || aif.getAgentInfo().equals("")) {
                    continue;
                }

                String[] agent = aif.getAgentInfo().split(":");
                if (agent.length != 2) {
                    continue;
                }

                String projectAgentKey = aif.getChildProjectName() + ":" + agent[0] + ":" + agent[1];

                // 检查是否已存在
                if (existingProjectAgents.contains(projectAgentKey)) {
                    continue;
                }

                // 检查Agent ID是否存在
                Long agentInfoId = agentIdMap.get(aif.getAgentInfo());
                if (agentInfoId == null) {
                    log.warn("【超高性能】Agent " + aif.getAgentInfo() + " 在IEAI_AGENTINFO表中不存在，跳过");
                    continue;
                }

                try {
                    long id = IdGenerator.createIdNoConnection("IEAI_PROJECT_AGENTINFO", basicType);
                    long prjid = taskUploadManager.getPrjLastIdByPrjName(aif.getChildProjectName(), conn);

                    ps.setLong(1, id);
                    ps.setLong(2, prjid);
                    ps.setString(3, aif.getChildProjectName());
                    ps.setString(4, aif.getAgentGropName());
                    ps.setString(5, agent[0]);
                    ps.setString(6, agent[1]);
                    ps.setLong(7, agentInfoId);
                    ps.addBatch();

                    // 添加到projectSaveUtilBean（保持原有逻辑）
                    RepProjectAgentInfo prjAgentInfo = new RepProjectAgentInfo();
                    prjAgentInfo.setIid(id);
                    prjAgentInfo.setPrjId(prjid);
                    prjAgentInfo.setPrjName(aif.getChildProjectName());
                    prjAgentInfo.setResourceName(aif.getAgentGropName());
                    prjAgentInfo.setAgentIp(agent[0]);
                    prjAgentInfo.setAgentPort(agent[1]);
                    prjAgentInfo.setAgentInfoId(agentInfoId);
                    projectSaveUtilBean.getInsertPrjAgentInfoList().add(prjAgentInfo);

                    insertCount++;

                    // 标记为已存在，避免重复插入
                    existingProjectAgents.add(projectAgentKey);

                    // 每1000条执行一次批量操作
                    if (insertCount % BATCH_SIZE == 0) {
                        ps.executeBatch();
                        log.info("【超高性能】已批量插入 " + insertCount + " 条项目Agent信息");
                    }

                } catch (Exception e) {
                    log.warn("【超高性能】插入项目Agent信息失败: " + aif.getChildProjectName() + " - " + aif.getAgentInfo() + ", 错误: " + e.getMessage());
                }
            }

            // 执行剩余的批量操作
            ps.executeBatch();
        }

        return insertCount;
    }

}
