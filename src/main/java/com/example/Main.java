package com.example;

import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.wedata.v20210820.WedataClient;
import com.tencentcloudapi.wedata.v20210820.models.*;

public class Main {
    public static void main(String[] args) {

        try{
            //端点需要一个参数
            //secretId
            //secretkey
            //地域需要传   chongqing
            //projectid
            //开始时间
            //结束时间
            if(args.length != 8){
                System.out.println("参数个数不正确！！！");
                System.out.println("本接口需要8个参数分别是：");
                System.out.println("1.端点(Endpoint)");
                System.out.println("2.secretId");
                System.out.println("3.secretkey");
                System.out.println("4.地域(Region)");
                System.out.println("5.ProjectId");
                System.out.println("6.开始时间(DateFrom),时间格式为：YYYY-MM-DD HH:mm:ss");
                System.out.println("7.结束时间(DateTo),时间格式为：YYYY-MM-DD HH:mm:ss");
                System.out.println("8.任务名称(Keyword)");
                System.exit(1);
            }

            String endpoint = args[0];
            String secretId = args[1];
            String secretKey = args[2];
            String region = args[3];
            String projectId = args[4];
            String dateFrom = args[5];
            String dateTo = args[6];
            String keyWord = args[7];

            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性
            // 以下代码示例仅供参考，建议采用更安全的方式来使用密钥
            // 请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取

            Credential cred = new Credential(secretId, secretKey);
            // 使用临时密钥示例
            // Credential cred = new Credential("SecretId", "SecretKey", "Token");
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endpoint);
            httpProfile.setProtocol(HttpProfile.REQ_HTTP);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            WedataClient client = new WedataClient(cred, region, clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DescribeScheduleInstancesRequest req = new DescribeScheduleInstancesRequest();
            //项目id
            req.setProjectId(projectId);
            req.setPageSize(10L);
            req.setPageIndex(1L);

            InstanceApiOpsRequest instanceSearchCondition1 = new InstanceApiOpsRequest();
            //查询开始时间,结束时间
            instanceSearchCondition1.setDateFrom(dateFrom);
            instanceSearchCondition1.setDateTo(dateTo);
            instanceSearchCondition1.setKeyword(keyWord);

            InstanceOpsDto instanceCondition1 = new InstanceOpsDto();
            instanceCondition1.setProductName("DATA_DEV");
            instanceCondition1.setExecutionSpace("CYCLIC");
            instanceSearchCondition1.setInstance(instanceCondition1);
            req.setSearchCondition(instanceSearchCondition1);
            // 返回的resp是一个DescribeTaskRunHistoryResponse的实例，与请求对象对应
            DescribeScheduleInstancesResponse resp = client.DescribeScheduleInstances(req);
            // 输出json格式的字符串回包
            System.out.println(AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
    }
}