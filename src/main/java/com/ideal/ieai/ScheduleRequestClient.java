package com.ideal.ieai;

import cn.com.antcloud.api.antcloud.rest.AntCloudRestClient;

public class ScheduleRequestClient {

    public static AntCloudRestClient buildProducer(String endpoint, String ak, String sk) {
        return AntCloudRestClient
                .newBuilder()
//                .setEndpoint("http://10.254.162.27:80/api")
                .setEndpoint(endpoint)
//                .setAccess("ez8I93JW36VQXB3Y", "2g8YX4bd7JaG9IdaXpzRqBkPoDIfGt")
                .setAccess(ak, sk)
                .build();
    }
}
